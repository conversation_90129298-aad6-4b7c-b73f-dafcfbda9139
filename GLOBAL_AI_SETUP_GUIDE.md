# 🚀 Yannova Global AI Workflow Manager

**Nu met LM Studio support voor lokale AI coding!**

## ✨ Wat is er nieuw?

- 🏠 **LM Studio Integratie** - Gebruik je lokale AI modellen
- 🌍 **Global Installation** - Werk in elk project met `yannova-ai`
- 🔄 **Smart Fallback** - Automatisch switchen tussen AI providers
- 📊 **Cross-Project Tracking** - Logs en configuratie gedeeld

## 🚀 Snelle Installatie

### Stap 1: Global Installatie

```bash
# In je Yannova CRM directory
python install_global_ai_workflow.py

# Herstart terminal
source ~/.bashrc  # of ~/.zshrc voor zsh
```

### Stap 2: LM Studio Setup

1. **Download LM Studio** van https://lmstudio.ai
2. **Installeer een model** (aanbevolen: Mistral-7B-Instruct)
3. **Start Local Server**:
   - Ga naar "Local Server" tab
   - Start server op http://127.0.0.1:1234
   - Model wordt automatisch gedetecteerd

### Stap 3: Test de Setup

```bash
# Test in elk project
cd /pad/naar/project
yannova-ai

# Output toont:
# ✅ LM Studio verbonden - lokale AI actief!
```

## 🎯 Gebruik

### Basic Workflow

```bash
# In elk project directory
yannova-ai

# De workflow:
# 1. Detecteert code wijzigingen
# 2. Genereert smart commit message (via LM Studio!)
# 3. Scant voor bugs
# 4. Maakt project rapport
```

### Geavanceerde Opties

```bash
# Alleen in specifieke project directory
cd ~/mijn-project
yannova-ai  # Werkt met project-specifieke .env

# Global config controleren
ls ~/.yannova_ai/
# config.json - Global settings
# logs/       - Alle project logs
```

## 🔧 Configuratie

### AI Provider Prioriteit

1. **🏠 LM Studio** (primair) - Lokaal, privé, gratis
2. **☁️ OpenAI API** (fallback) - Voor complexe taken
3. **🛠️ Fallback Logic** - Als beide falen

### Environment Variables

Maak `.env` in je project directory (optioneel):

```env
# Voor OpenAI fallback (optioneel)
OPENAI_API_KEY=sk-jouw-key-hier

# Voor GitHub integratie (optioneel)  
GITHUB_TOKEN=ghp_jouw-token

# Project specifieke settings
PROJECT_NAME=Mijn Project
ENVIRONMENT=development
```

### Global Config

Bestand: `~/.yannova_ai/config.json`

```json
{
  "version": "2.0.0",
  "ai_preferences": {
    "prefer_lm_studio": true,
    "fallback_to_openai": true,
    "model": "mistral-7b-instruct-v0.1"
  },
  "projects": [
    "/pad/naar/project1",
    "/pad/naar/project2"
  ]
}
```

## 🤖 LM Studio Modellen

### Aanbevolen Modellen

| Model | Size | Use Case | Performance |
|-------|------|----------|-------------|
| **Mistral-7B-Instruct** | 4.1GB | Algemeen coding | ⭐⭐⭐⭐⭐ |
| DeepSeek-Coder-6.7B | 3.8GB | Code review | ⭐⭐⭐⭐ |
| CodeLlama-7B | 3.5GB | Code generatie | ⭐⭐⭐⭐ |
| Llama-2-7B-Chat | 3.5GB | Chat/planning | ⭐⭐⭐ |

### Model Setup

1. **Download** via LM Studio interface
2. **Load** het model in Local Server
3. **Test** via LM Studio chat
4. **Start** de workflow manager

## 📊 Features & Voorbeelden

### Smart Commit Messages

**Input:** Code wijzigingen gedetecteerd
**LM Studio Output:** `feat: voeg AI workflow manager toe met LM Studio support`

### Bug Detection

Detecteert automatisch:
- `TODO:` items
- `FIXME:` notities
- Error patterns
- Code smells

### Project Tracking

Alle logs in `~/.yannova_ai/logs/`:
```
project1_20241206.log
project2_20241206.log
yannova-crm_20241206.log
```

## 🔍 Troubleshooting

### LM Studio Problemen

```bash
# Test LM Studio verbinding
curl http://127.0.0.1:1234/v1/models

# Expected output:
{"data": [{"id": "mistral-7b-instruct-v0.1", ...}]}
```

**Common Issues:**
- ❌ Server niet gestart → Start Local Server in LM Studio
- ❌ Geen model geladen → Laad een model in LM Studio
- ❌ Poort bezet → Check andere applicaties op port 1234

### OpenAI Fallback

```bash
# Test OpenAI key
python -c "import openai; print('OpenAI beschikbaar')"

# Check API key
echo $OPENAI_API_KEY
```

### Global Installation Issues

```bash
# Check if yannova-ai executable exists
which yannova-ai

# Re-install if needed
python install_global_ai_workflow.py

# Check PATH
echo $PATH | grep ".local/bin"
```

## 🎯 Best Practices

### Voor Beste Resultaten

1. **Regelmatig gebruiken** - Voor de beste commit messages
2. **LM Studio actief houden** - Voor snelle AI responses  
3. **Clean commits** - Kleinere, focused changes werken beter
4. **Project organization** - Houd je projecten georganiseerd

### Workflow Tips

```bash
# Daily routine
cd project && yannova-ai  # Analyse + commit

# Before pushing
yannova-ai                # Final check

# New project setup  
cd new-project && yannova-ai  # Auto-detecteert als nieuw project
```

### Performance Optimization

- **Model Selection**: Mistral-7B voor balans tussen snelheid/kwaliteit
- **Batch Processing**: Workflow manager bundelt taken
- **Smart Caching**: Hergebruikt AI responses waar mogelijk

## 🔐 Privacy & Security

### LM Studio Voordelen

- ✅ **Data blijft lokaal** - Geen externe API calls
- ✅ **Geen internet vereist** - Werkt volledig offline
- ✅ **Geen API kosten** - Gratis na model download
- ✅ **Snelle responses** - Directe lokale processing

### Best Practices

- Gebruik LM Studio voor gevoelige projecten
- OpenAI alleen als fallback voor publieke code
- Logs blijven lokaal in `~/.yannova_ai/`

## 🚀 Geavanceerde Gebruik

### Custom Prompts

De workflow manager gebruikt geoptimaliseerde prompts voor:

- **Commit Messages**: Nederlandse, professionele format
- **Bug Detection**: Context-aware scanning  
- **Code Analysis**: Project-specifieke feedback

### Integration met Bestaande Tools

```bash
# Git hooks
yannova-ai && git push

# CI/CD integration
yannova-ai --report-only

# IDE integration  
# Voeg toe aan je editor scripts
```

## 🎉 Resultaat

Na installatie heb je:

- ✅ **Global AI assistant** beschikbaar in elk project
- ✅ **LM Studio integratie** voor lokale AI
- ✅ **Automatische workflows** voor alle projecten
- ✅ **Smart commit messages** gegenereerd door AI
- ✅ **Cross-project logging** en tracking
- ✅ **Privacy-first** approach met lokale AI

**Ready to code smarter! 🚀** 