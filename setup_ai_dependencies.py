#!/usr/bin/env python3
"""
Yannova CRM AI Dependencies Setup Script
Helpt bij het installeren van benodigde packages voor de AI Workflow Manager
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Voer een command uit en toon de voortgang"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} succesvol!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Fout bij {description}: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Controleer Python versie"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 of hoger is vereist")
        print(f"Huidige versie: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python versie: {version.major}.{version.minor}.{version.micro}")
    return True

def create_env_file():
    """Maak een .env bestand aan als het niet bestaat"""
    env_file = Path('.env')
    if env_file.exists():
        print("✅ .env bestand bestaat al")
        return True
    
    env_content = """# Yannova CRM Environment Variables
# Vul je eigen waarden in

# OpenAI API Configuration (verplicht voor AI features)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# GitHub Integration (optioneel)
GITHUB_TOKEN=your_github_token_here
GITHUB_REPO=your_username/yannova-crm

# Project Configuration
PROJECT_NAME=Yannova CRM
ENVIRONMENT=development
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ .env bestand aangemaakt")
        print("📝 Vergeet niet je OpenAI API key in te vullen!")
        return True
    except Exception as e:
        print(f"❌ Fout bij aanmaken .env bestand: {e}")
        return False

def main():
    """Hoofdfunctie voor de setup"""
    print("🚀 Yannova CRM AI Dependencies Setup")
    print("=" * 50)
    
    # Controleer Python versie
    if not check_python_version():
        return False
    
    # Controleer of we in de juiste directory zijn
    if not Path('ai_workflow_manager.py').exists():
        print("❌ Voer dit script uit vanuit de Yannova CRM root directory")
        return False
    
    # Update pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Pip updaten")
    
    # Installeer basis dependencies
    dependencies = [
        "python-dotenv>=1.0.0",
        "openai>=1.0.0",
        "requests>=2.31.0"
    ]
    
    for dep in dependencies:
        if not run_command(f"{sys.executable} -m pip install '{dep}'", f"Installeren {dep}"):
            print(f"⚠️  Kon {dep} niet installeren - ga door met de rest")
    
    # Optionele dependencies
    optional_deps = [
        ("GitPython>=3.1.40", "Git integratie"),
        ("composio-openai>=0.5.0", "GitHub integratie (optioneel)")
    ]
    
    print("\n🔧 Optionele dependencies installeren...")
    for dep, description in optional_deps:
        user_input = input(f"Wil je {description} installeren? (y/n): ").lower()
        if user_input == 'y':
            run_command(f"{sys.executable} -m pip install '{dep}'", f"Installeren {dep}")
    
    # Maak .env bestand aan
    create_env_file()
    
    # Test de setup
    print("\n🧪 Setup testen...")
    try:
        import dotenv
        print("✅ python-dotenv geïnstalleerd")
    except ImportError:
        print("❌ python-dotenv niet beschikbaar")
    
    try:
        import openai
        print("✅ openai geïnstalleerd")
    except ImportError:
        print("❌ openai niet beschikbaar")
    
    print("\n" + "=" * 50)
    print("✅ Setup voltooid!")
    print("📝 Volgende stappen:")
    print("1. Vul je OpenAI API key in het .env bestand in")
    print("2. Run: python ai_workflow_manager.py")
    print("3. Verkrijg een API key op: https://platform.openai.com/api-keys")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 