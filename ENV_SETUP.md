# Environment Variables Setup

## ⚠️ BELANGRIJK: Locatie van .env bestand

De `.env` file moet in de **`apps/api/`** folder staan, NIET in de root directory!

```
yannova-crm/
├── apps/
│   └── api/
│       └── .env  ← HIER moet je .env bestand staan!
├── package.json
└── README.md
```

## Waarom?

- De API draait vanuit `apps/api/` 
- Next.js zoekt daar naar de `.env` file
- Root `.env` wordt NIET gebruikt door de applicatie

## Setup Instructies

1. **Navigeer naar de juiste folder:**
   ```bash
   cd apps/api
   ```

2. **Check of .env bestaat:**
   ```bash
   ls -la | grep .env
   ```

3. **Als .env niet bestaat, maak hem aan:**
   ```bash
   touch .env
   ```

4. **Voeg je configuratie toe:**
   ```env
   # Database
   DATABASE_URL="file:./dev.db"

   # Email (Resend)
   RESEND_API_KEY=re_JOUW_API_KEY_HIER
   FROM_EMAIL=<EMAIL>

   # AI (OpenAI)
   OPENAI_API_KEY=sk-proj-JOUW_KEY_HIER

   # Telegram Bot (optioneel)
   TELEGRAM_BOT_TOKEN=JOUW_BOT_TOKEN

   # Development
   NODE_ENV=development
   NEXT_PUBLIC_APP_URL=http://localhost:3001
   ```

## Verifieer Setup

Run vanuit de `apps/api` folder:
```bash
npm run dev
```

De server start op http://localhost:3001 