# 🚀 Yannova CRM AI Workflows

Geautomatiseerde AI-workflows die tijd besparen voor je Yannova CRM project. Deze tool gebruikt OpenAI en Composio om repetitieve taken te automatiseren en je development workflow te verbeteren.

## ✨ Features

### 🤖 Automatische Code Analyse
- **Smart Commit Messages**: AI genereert intelligente commit messages op basis van je code wijzigingen
- **Bug Detection**: Automatische scanning voor potentiële bugs en code issues
- **Code Quality Checks**: Detecteert code smells en verbetersuggesties

### 🐛 Issue Management
- **Auto Issue Creation**: Maakt automatisch GitHub issues aan voor gevonden bugs
- **Smart Labeling**: Voegt relevante labels toe aan issues
- **Priority Classification**: Classificeert issues op basis van ernst

### 📊 Project Monitoring
- **Status Reports**: Genereert dagelijkse project status rapporten
- **Change Tracking**: Houdt wijzigingen bij en analyseert trends
- **Performance Metrics**: Meet en rapporteert development metrics

### 🔄 Workflow Automation
- **Git Hooks**: Automatische checks bij commits
- **Scheduled Tasks**: Dagelijkse/wekelijkse geautomatiseerde taken
- **Notification System**: <PERSON>lack, Discord, en email notificaties

## 🛠️ Installatie

### Stap 1: Quick Setup
```bash
# Voer de automatische setup uit
python setup_ai_workflows.py
```

### Stap 2: Configureer API Keys
Bewerk het `.env` bestand en vul je API keys in:
```env
OPENAI_API_KEY=your_openai_api_key_here
COMPOSIO_API_KEY=your_composio_api_key_here
GITHUB_TOKEN=your_github_token_here
```

### Stap 3: Test de Workflow
```bash
# Linux/macOS
./run_workflow.sh

# Windows
run_workflow.bat

# Of direct
python ai_workflow_manager.py
```

## 🎯 Gebruik

### Basis Workflow
```bash
python ai_workflow_manager.py
```

Dit voert de volledige workflow uit:
1. 📊 Analyseert code wijzigingen
2. 💡 Genereert smart commit message
3. 🔍 Scant voor bugs en issues
4. 🐛 Maakt GitHub issues aan (optioneel)
5. 📈 Genereert status rapport

### Geavanceerde Opties

#### Alleen Code Analyse
```python
from ai_workflow_manager import YannovaCRMWorkflowManager

manager = YannovaCRMWorkflowManager()
changes = manager.analyze_code_changes()
print(changes)
```

#### Custom Error Scanning
```python
manager = YannovaCRMWorkflowManager()
errors = manager.scan_for_errors()
for error in errors:
    print(f"Gevonden: {error}")
```

#### Smart Commit Messages
```python
manager = YannovaCRMWorkflowManager()
changes = manager.analyze_code_changes()
if changes['has_changes']:
    message = manager.generate_smart_commit_message(changes)
    print(f"Voorgestelde commit: {message}")
```

## ⚙️ Configuratie

Pas `workflow_config.json` aan voor je specifieke behoeften:

### Basis Instellingen
```json
{
  "workflow_settings": {
    "auto_commit": false,
    "auto_create_issues": false,
    "max_issues_per_run": 3
  }
}
```

### Error Patterns
```json
{
  "error_patterns": {
    "critical": ["throw new Error", "FATAL:"],
    "warnings": ["TODO: FIX", "FIXME"],
    "code_smells": ["console.log", "debugger;"]
  }
}
```

### GitHub Integratie
```json
{
  "github_settings": {
    "issue_labels": ["bug", "automated", "ai-generated"],
    "assignee": "jouw-github-username"
  }
}
```

## 🔧 Geavanceerde Features

### Scheduled Tasks
Configureer automatische taken:
```json
{
  "scheduled_tasks": {
    "daily_scan": {
      "enabled": true,
      "time": "09:00"
    },
    "weekly_report": {
      "enabled": true,
      "day": "monday",
      "time": "10:00"
    }
  }
}
```

### Notificaties
Setup Slack/Discord notificaties:
```json
{
  "notifications": {
    "slack": {
      "enabled": true,
      "webhook_url": "your_slack_webhook"
    }
  }
}
```

### Custom AI Settings
Pas AI gedrag aan:
```json
{
  "ai_settings": {
    "model": "gpt-4",
    "temperature": 0.3,
    "language": "nl"
  }
}
```

## 📋 Workflow Voorbeelden

### Dagelijkse Development Routine
```bash
# Ochtend check
python ai_workflow_manager.py

# Na development werk
git add .
python -c "
from ai_workflow_manager import YannovaCRMWorkflowManager
manager = YannovaCRMWorkflowManager()
changes = manager.analyze_code_changes()
message = manager.generate_smart_commit_message(changes)
print(f'Commit message: {message}')
"
```

### Bug Hunting Session
```python
from ai_workflow_manager import YannovaCRMWorkflowManager

manager = YannovaCRMWorkflowManager()

# Scan voor alle errors
errors = manager.scan_for_errors()
print(f"Gevonden {len(errors)} potentiële problemen:")

for error in errors:
    print(f"- {error}")

# Maak automatisch issues aan
if len(errors) > 0:
    manager.create_github_issue_for_bugs(errors)
```

## 🚨 Troubleshooting

### Veelvoorkomende Problemen

#### "OpenAI API key not found"
```bash
# Controleer je .env bestand
cat .env | grep OPENAI_API_KEY

# Of stel in via environment
export OPENAI_API_KEY="your-key-here"
```

#### "Composio not authenticated"
```bash
# Login bij Composio
composio login

# Controleer verbinding
composio apps
```

#### "Git repository not found"
```bash
# Zorg dat je in de juiste directory bent
pwd
ls -la | grep .git

# Initialiseer git als nodig
git init
```

## 📈 Best Practices

### 1. Dagelijkse Workflow
- Run de workflow elke ochtend
- Review gegenereerde commit messages
- Check automatisch aangemaakte issues

### 2. Code Quality
- Gebruik de error scanning voor code reviews
- Configureer custom error patterns voor je project
- Setup pre-commit hooks voor automatische checks

### 3. Team Collaboration
- Deel workflow configuratie met je team
- Gebruik consistent labeling voor issues
- Setup team notificaties

## 🔮 Toekomstige Features

- [ ] **Code Review Assistant**: AI die pull requests reviewt
- [ ] **Deployment Automation**: Automatische deployment workflows
- [ ] **Performance Monitoring**: Code performance analysis
- [ ] **Security Scanning**: Automatische security vulnerability detection
- [ ] **Documentation Generation**: Auto-generated code documentation
- [ ] **Test Generation**: Automatische unit test creation

## 🤝 Contributing

Wil je bijdragen aan de AI Workflows? Geweldig!

1. Fork het project
2. Maak een feature branch
3. Commit je wijzigingen
4. Push naar de branch
5. Open een Pull Request

## 📄 License

Dit project is gelicenseerd onder de MIT License.

## 🆘 Support

Heb je vragen of problemen?

- 📧 Email: <EMAIL>
- 💬 Discord: [Yannova Community](https://discord.gg/yannova)
- 🐛 Issues: [GitHub Issues](https://github.com/yannova/crm/issues)

---

**Gemaakt met ❤️ voor de Yannova CRM community**
