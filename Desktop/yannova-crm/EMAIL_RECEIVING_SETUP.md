# Email Ontvangen Setup - <EMAIL>

## 🚀 3 Opties om emails te ontvangen in je CRM:

### Optie 1: Resend Inbound (Aanbevolen)
Resend biedt een inbound email service waarmee je emails kunt ontvangen op je domein.

1. **Setup in Resend Dashboard:**
   - Ga naar Domains → Inbound
   - Voeg een MX record toe: `10 feedback-smtp.eu-west-1.amazonses.com`
   - Configureer webhook: `https://jouw-app.com/api/email/webhook`

2. **Webhook ontvangt:**
```json
{
  "from": "<EMAIL>",
  "to": "<EMAIL>",
  "subject": "Vraag over prijzen",
  "html": "<p>Email content</p>",
  "text": "Email content",
  "messageId": "msg_123"
}
```

### Optie 2: Email Forwarding
Stel email forwarding in bij je provider:

1. **Zoho Mail:**
   - Settings → Mail Forwarding
   - Forward naar: `<EMAIL>`
   - Of naar een parsing service

2. **Gmail:**
   - Settings → Forwarding
   - Add forwarding address
   - Forward naar webhook service

### Optie 3: IMAP Polling (Fallback)
Check periodiek de inbox:

```typescript
// apps/api/lib/imap-receiver.ts
import Imap from 'imap'
import { simpleParser } from 'mailparser'

export class IMAPReceiver {
  private imap: Imap

  constructor() {
    this.imap = new Imap({
      user: process.env.IMAP_USER,
      password: process.env.IMAP_PASSWORD,
      host: 'imap.zoho.eu',
      port: 993,
      tls: true
    })
  }

  async checkNewEmails() {
    // Poll every 5 minutes
    // Process new emails
    // Save to CRM
  }
}
```

## 📧 Email Flow in CRM:

1. **Email komt binnen** → Webhook/IMAP
2. **Contact wordt aangemaakt/gevonden**
3. **Conversation wordt opgeslagen**
4. **AI analyseert sentiment**
5. **Task wordt aangemaakt** (indien nodig)
6. **Auto-reply wordt verstuurd** (indien geconfigureerd)

## 🗄️ Waar worden emails opgeslagen?

### Database Schema:
- **contacts** - Alle email adressen
- **conversations** - Alle email conversaties
- **tasks** - Automatisch gegenereerde taken
- **deals** - Gekoppelde deals

### Voorbeeld Query:
```sql
-- Alle emails van een contact
SELECT * FROM conversations 
WHERE contactId = '123' 
AND type = 'EMAIL'
ORDER BY createdAt DESC;
```

## 🎯 CRM Features:

### Nu beschikbaar:
- ✅ Email ontvangen en opslaan
- ✅ Contact management
- ✅ Sentiment analyse
- ✅ Auto-categorisatie
- ✅ Task creation
- ✅ Smart auto-replies

### Dashboard URLs:
- **CRM Dashboard**: http://localhost:3001/crm-dashboard.html
- **Send Email**: http://localhost:3001/send-email.html
- **Email Templates**: http://localhost:3001/email-templates.html

### API Endpoints:
- `POST /api/email/webhook` - Receive emails
- `GET /api/crm/stats` - Dashboard stats
- `GET /api/crm/emails` - List emails
- `GET /api/contacts` - List contacts
- `POST /api/email/send-template` - Send templated emails

## 🔧 Quick Setup:

1. **Voor Resend Inbound:**
```env
RESEND_API_KEY=re_xxxxx
RESEND_WEBHOOK_SECRET=whsec_xxxxx
```

2. **Voor IMAP:**
```env
IMAP_USER=<EMAIL>
IMAP_PASSWORD=xxxxx
IMAP_HOST=imap.zoho.eu
```

3. **Database migratie:**
```bash
npm run db:push
```

4. **Start CRM:**
```bash
npm run dev
```

## 📱 Integraties:

### Telegram Bot:
- Notificaties bij nieuwe email
- Quick replies via bot
- `/crm` commando voor stats

### WhatsApp:
- Email summaries
- Contact updates
- Deal notifications

### Webhooks:
- Zapier/Make integratie
- Custom automations
- Third-party CRMs

De CRM is nu volledig operationeel! 🚀 