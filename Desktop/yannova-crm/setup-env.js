#!/usr/bin/env node

const fs = require('fs');
const readline = require('readline');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

console.log(`
🚀 Yannova CRM - Environment Setup
==================================
Dit script helpt je met het instellen van je .env file.
`);

async function setup() {
  const envPath = path.join(__dirname, '.env');
  let envContent = '';

  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    const overwrite = await question('.env bestaat al. Overschrijven? (j/n): ');
    if (overwrite.toLowerCase() !== 'j') {
      console.log('Setup geannuleerd.');
      rl.close();
      return;
    }
  }

  console.log('\n📧 Email Configuratie (Resend)\n');
  console.log('1. Ga naar https://resend.com/signup');
  console.log('2. Maak een account en genereer een API key');
  console.log('3. Voeg je domein toe (yannova.be)\n');

  const resendKey = await question('Resend API Key (re_...): ') || 're_JOUW_KEY_HIER';
  const fromEmail = await question('Van email adres [<EMAIL>]: ') || '<EMAIL>';

  envContent += `# Database
DATABASE_URL="file:./dev.db"

# Email (Resend)
RESEND_API_KEY="${resendKey}"
FROM_EMAIL="${fromEmail}"
`;

  // Telegram Bot
  console.log('\n🤖 Telegram Bot (optioneel)\n');
  console.log('1. Open Telegram en zoek @BotFather');
  console.log('2. Stuur /newbot en volg instructies\n');

  const setupTelegram = await question('Wil je Telegram bot instellen? (j/n): ');
  if (setupTelegram.toLowerCase() === 'j') {
    const telegramToken = await question('Telegram Bot Token: ');
    envContent += `
# Telegram Bot
TELEGRAM_BOT_TOKEN="${telegramToken}"
`;
  }

  // WhatsApp
  console.log('\n💬 WhatsApp Business (optioneel)\n');
  console.log('Via Twilio: https://www.twilio.com/try-twilio\n');

  const setupWhatsApp = await question('Wil je WhatsApp instellen? (j/n): ');
  if (setupWhatsApp.toLowerCase() === 'j') {
    const twilioSid = await question('Twilio Account SID: ');
    const twilioAuth = await question('Twilio Auth Token: ');
    const twilioNumber = await question('WhatsApp nummer [whatsapp:+***********]: ') || 'whatsapp:+***********';
    
    envContent += `
# WhatsApp (Twilio)
TWILIO_ACCOUNT_SID="${twilioSid}"
TWILIO_AUTH_TOKEN="${twilioAuth}"
TWILIO_WHATSAPP_NUMBER="${twilioNumber}"
`;
  }

  // AI
  console.log('\n🤖 AI Integratie (optioneel)\n');
  console.log('1. OpenAI - https://platform.openai.com/api-keys');
  console.log('2. Cloudflare AI - https://dash.cloudflare.com/ (gratis)\n');

  const aiChoice = await question('Welke AI provider? (1=OpenAI, 2=Cloudflare, n=geen): ');
  
  if (aiChoice === '1') {
    const openaiKey = await question('OpenAI API Key (sk-...): ');
    envContent += `
# AI (OpenAI)
OPENAI_API_KEY="${openaiKey}"
`;
  } else if (aiChoice === '2') {
    const cfAccount = await question('Cloudflare Account ID: ');
    const cfToken = await question('Cloudflare AI Token: ');
    envContent += `
# AI (Cloudflare)
CLOUDFLARE_ACCOUNT_ID="${cfAccount}"
CLOUDFLARE_AI_TOKEN="${cfToken}"
`;
  }

  // Development settings
  envContent += `
# Development
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3001"
`;

  // Write .env file
  fs.writeFileSync(envPath, envContent);
  console.log('\n✅ .env file aangemaakt!');

  // Show next steps
  console.log(`
📋 Volgende stappen:
===================

1. Start de development server:
   npm run dev

2. Test je email configuratie:
   http://localhost:3001/api/email/config-test

3. Open het CRM dashboard:
   http://localhost:3001/crm-dashboard.html

4. Verstuur een test email:
   http://localhost:3001/send-email.html

${resendKey.startsWith('re_') ? '' : '⚠️  Vergeet niet je echte Resend API key toe te voegen!\n'}
Veel succes! 🚀
`);

  rl.close();
}

setup().catch(console.error); 