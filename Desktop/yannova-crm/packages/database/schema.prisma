// packages/database/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Contact {
  id          String   @id @default(cuid())
  email       String   @unique
  phone       String?
  name        String?
  company     String?
  source      Source   @default(EMAIL)
  status      Status   @default(LEAD)
  tags        String[]
  
  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastContact DateTime?
  
  // Relations
  conversations Conversation[]
  deals        Deal[]
  tasks        Task[]
  
  @@map("contacts")
}

model Conversation {
  id        String   @id @default(cuid())
  contactId String
  type      ConversationType
  channel   Channel
  subject   String?
  content   String
  direction Direction @default(INBOUND)
  sentiment Float?   // -1 to 1, AI analyzed
  
  // Metadata
  createdAt DateTime @default(now())
  readAt    DateTime?
  
  // Relations
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  @@map("conversations")
}

model Deal {
  id          String     @id @default(cuid())
  contactId   String
  title       String
  value       Float?
  currency    String     @default("EUR")
  stage       DealStage  @default(QUALIFICATION)
  probability Int        @default(10) // 0-100
  
  // Dates
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  closedAt    DateTime?
  
  // Relations
  contact     Contact    @relation(fields: [contactId], references: [id])
  tasks       Task[]
  
  @@map("deals")
}

model Task {
  id          String    @id @default(cuid())
  contactId   String?
  dealId      String?
  title       String
  description String?
  type        TaskType  @default(FOLLOWUP)
  priority    Priority  @default(MEDIUM)
  completed   Boolean   @default(false)
  
  // Dates
  dueDate     DateTime?
  createdAt   DateTime  @default(now())
  completedAt DateTime?
  
  // Relations
  contact     Contact?  @relation(fields: [contactId], references: [id])
  deal        Deal?     @relation(fields: [dealId], references: [id])
  
  @@map("tasks")
}

// Enums
enum Source {
  EMAIL
  WEBSITE
  WHATSAPP
  TELEGRAM
  PHONE
  REFERRAL
  SOCIAL
}

enum Status {
  LEAD
  PROSPECT
  CUSTOMER
  INACTIVE
}

enum ConversationType {
  EMAIL
  CHAT
  CALL
  MEETING
  NOTE
}

enum Channel {
  EMAIL
  WHATSAPP
  TELEGRAM
  PHONE
  IN_PERSON
}

enum Direction {
  INBOUND
  OUTBOUND
}

enum DealStage {
  QUALIFICATION
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum TaskType {
  FOLLOWUP
  CALL
  EMAIL
  MEETING
  QUOTE
  DEMO
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}