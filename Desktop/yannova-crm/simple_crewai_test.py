#!/usr/bin/env python3
"""
Eenvoudige CrewAI test voor Yannova CRM
"""
import os

# Mock implementaties voor CrewAI functionaliteit
class Agent:
    def __init__(self, role, goal, backstory, llm=None, tools=None, verbose=False):
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.llm = llm
        self.tools = tools or []
        self.verbose = verbose

class Task:
    def __init__(self, description, agent, expected_output=None):
        self.description = description
        self.agent = agent
        self.expected_output = expected_output

class Crew:
    def __init__(self, agents, tasks, verbose=False):
        self.agents = agents
        self.tasks = tasks
        self.verbose = verbose
    
    def kickoff(self):
        return {"result": "Mock crew execution completed"}

class BaseTool:
    def __init__(self, name, description):
        self.name = name
        self.description = description

# Mock LLM die lokaal werkt
class SimpleLLM:
    def __init__(self):
        self.model_name = "simple-mock"
    
    def invoke(self, messages):
        # Simpele response gebaseerd op input
        if isinstance(messages, list) and len(messages) > 0:
            content = str(messages[-1].get('content', ''))
        else:
            content = str(messages)
            
        if "plan" in content.lower():
            return SimpleResponse("""
# CRM Feature Ontwikkelingsplan

## 1. Feature Analyse
- **Doel**: Nieuwe functionaliteit voor Yannova CRM
- **Type**: Gebruikerservaring verbetering
- **Prioriteit**: Hoog

## 2. Technische Architectuur
- **Frontend**: Vue.js 3 + TypeScript
- **Backend**: Next.js API routes
- **Database**: Prisma ORM + PostgreSQL
- **AI**: LM Studio integratie

## 3. Implementatie Roadmap

### Fase 1: Planning (Week 1)
- Requirements gathering
- UI/UX wireframes
- Database schema ontwerp
- API endpoint specificaties

### Fase 2: Development (Week 2-3)
- Backend API ontwikkeling
- Frontend componenten
- Database migraties
- Unit tests

### Fase 3: Integratie (Week 4)
- AI model integratie
- End-to-end testing
- Performance optimalisatie
- Security audit

### Fase 4: Deployment (Week 5)
- Production deployment
- Monitoring setup
- Gebruikers training
- Documentatie

## 4. Resources Benodigd
- **Team**: 2 developers, 1 designer, 1 QA
- **Tijd**: 5 weken
- **Budget**: €15.000
- **Tools**: Figma, GitHub, Vercel

## 5. Risico Management
- **Technisch**: API integratie complexiteit
- **Planning**: Scope creep
- **Business**: User adoption
- **Mitigatie**: Agile development, user feedback loops

## 6. Success Metrics
- **Performance**: Laadtijd < 2 seconden
- **Gebruikers**: 90% adoption rate
- **Business**: 25% efficiency verbetering
- **Kwaliteit**: < 1% error rate

*Gegenereerd door AI Project Manager*
            """)
        elif "code" in content.lower():
            return SimpleResponse("""
# TypeScript Code Voorbeeld

```typescript
// CRM Feature Component
import { defineComponent, ref, onMounted } from 'vue'
import { useCRMStore } from '@/stores/crm'

export default defineComponent({
  name: 'NewCRMFeature',
  setup() {
    const crmStore = useCRMStore()
    const loading = ref(false)
    const data = ref([])
    
    const loadData = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/crm/feature-data')
        data.value = await response.json()
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        loading.value = false
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      data,
      loadData
    }
  }
})
```

```typescript
// API Route
import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    try {
      const data = await prisma.crmFeature.findMany({
        include: {
          user: true,
          metadata: true
        }
      })
      
      res.status(200).json(data)
    } catch (error) {
      res.status(500).json({ error: 'Database error' })
    }
  }
}
```

*Gegenereerd door Lead Developer*
            """)
        else:
            return SimpleResponse("""
# AI Analyse Rapport

## CRM Performance Insights

### 1. Huidige Status
- **Gebruikers**: 150+ actieve gebruikers
- **Performance**: Gemiddeld goed
- **Bottlenecks**: Email verwerking, data sync

### 2. Verbeterpunten
- **AI Integratie**: Automatische lead scoring
- **Performance**: Database query optimalisatie
- **UX**: Responsieve design verbetering
- **Workflow**: Geautomatiseerde taken

### 3. Aanbevelingen
1. Implementeer LM Studio voor lokale AI
2. Optimaliseer database indices
3. Voeg real-time notifications toe
4. Verbeter mobile experience

### 4. ROI Projectie
- **Tijdsbesparing**: 40% per gebruiker
- **Lead conversie**: +25%
- **Kosten reductie**: €10.000/jaar
- **Implementatie**: 4-6 weken

*Gegenereerd door CRM Analyst*
            """)

class SimpleResponse:
    def __init__(self, content):
        self.content = content

# Simple tool
class SimpleAPITool(BaseTool):
    name: str = "Simple_API"
    description: str = "Eenvoudige API tool voor testing"
    
    def _run(self, query: str) -> str:
        return f"API Response voor: {query}"

def test_crewai():
    """Test CrewAI met eenvoudige setup"""
    print("🧪 Testing CrewAI met simple setup...")
    
    # Maak LLM
    llm = SimpleLLM()
    # Maak tool
    api_tool = SimpleAPITool(
        name="Simple_API",
        description="Eenvoudige API tool voor testing"
    )
    
    # Maak agent
    project_manager = Agent(
        role="Technical Project Manager",
        goal="Maak projectplannen voor CRM features",
        backstory="Je bent een ervaren project manager bij Yannova CRM.",
        tools=[api_tool],
        llm=llm,
        verbose=True
    )
    
    # Maak taak
    planning_task = Task(
        description="Maak een gedetailleerd plan voor een nieuwe CRM feature",
        agent=project_manager,
        expected_output="Compleet projectplan met timeline en resources"
    )
    
    # Maak crew
    crew = Crew(
        agents=[project_manager],
        tasks=[planning_task],
        verbose=True
    )
    
    # Run crew
    try:
        result = crew.kickoff()
        print("\n" + "="*50)
        print("✅ CREWAI TEST SUCCESVOL")
        print("="*50)
        print(result)
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    test_crewai() 