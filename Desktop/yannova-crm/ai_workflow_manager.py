#!/usr/bin/env python3
"""
Yannova CRM AI Workflow Manager
Geautomatiseerde workflows die tijd besparen voor je CRM project
"""

import os
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Optional
from composio_openai import ComposioToolSet, Action
from openai import OpenAI

class YannovaCRMWorkflowManager:
    def __init__(self):
        """Initialiseer de AI Workflow Manager voor Yannova CRM"""
        self.openai_client = OpenAI()
        self.composio_toolset = ComposioToolSet()
        self.project_root = os.getcwd()
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging voor workflow tracking"""
        self.log_file = f"workflow_log_{datetime.now().strftime('%Y%m%d')}.txt"
        
    def log_action(self, action: str, details: str = ""):
        """Log workflow acties"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {action}: {details}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
        print(f"✅ {action}")
        
    def analyze_code_changes(self) -> Dict:
        """Analyseer recente code wijzigingen voor automatische acties"""
        try:
            # Git status ophalen
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  capture_output=True, text=True, cwd=self.project_root)
            
            changes = {
                'modified_files': [],
                'new_files': [],
                'deleted_files': [],
                'has_changes': False
            }
            
            if result.stdout:
                changes['has_changes'] = True
                for line in result.stdout.strip().split('\n'):
                    if line.startswith(' M '):
                        changes['modified_files'].append(line[3:])
                    elif line.startswith('A '):
                        changes['new_files'].append(line[3:])
                    elif line.startswith(' D '):
                        changes['deleted_files'].append(line[3:])
                        
            return changes
            
        except Exception as e:
            self.log_action("ERROR", f"Fout bij analyseren code wijzigingen: {str(e)}")
            return {'has_changes': False}
    
    def generate_smart_commit_message(self, changes: Dict) -> str:
        """Genereer intelligente commit message op basis van wijzigingen"""
        if not changes['has_changes']:
            return ""
            
        files_context = []
        
        # Analyseer gewijzigde bestanden
        for file in changes['modified_files'][:5]:  # Limiteer tot 5 bestanden
            try:
                with open(os.path.join(self.project_root, file), 'r', encoding='utf-8') as f:
                    content = f.read()[:500]  # Eerste 500 karakters
                    files_context.append(f"Bestand: {file}\nInhoud preview: {content}")
            except:
                files_context.append(f"Bestand: {file}")
        
        prompt = f"""
        Genereer een professionele commit message in het Nederlands voor deze wijzigingen in het Yannova CRM project:
        
        Gewijzigde bestanden: {', '.join(changes['modified_files'])}
        Nieuwe bestanden: {', '.join(changes['new_files'])}
        Verwijderde bestanden: {', '.join(changes['deleted_files'])}
        
        Context van bestanden:
        {chr(10).join(files_context)}
        
        Maak een korte, duidelijke commit message die beschrijft wat er is veranderd.
        Format: "type: beschrijving" (bijv. "feat: voeg klant dashboard toe" of "fix: los email validatie bug op")
        """
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=100,
                temperature=0.3
            )
            
            commit_message = response.choices[0].message.content.strip()
            self.log_action("COMMIT_MESSAGE_GENERATED", commit_message)
            return commit_message
            
        except Exception as e:
            self.log_action("ERROR", f"Fout bij genereren commit message: {str(e)}")
            return "chore: automatische commit via AI workflow"
    
    def create_github_issue_for_bugs(self, error_logs: List[str]) -> bool:
        """Maak automatisch GitHub issues aan voor gevonden bugs"""
        if not error_logs:
            return False
            
        try:
            # Gebruik Composio om GitHub issue aan te maken
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_ISSUE])
            
            for error in error_logs[:3]:  # Max 3 issues per run
                issue_title = f"🐛 Bug gevonden: {error[:50]}..."
                issue_body = f"""
## Bug Beschrijving
Er is automatisch een bug gedetecteerd in het Yannova CRM systeem.

**Error Details:**
```
{error}
```

**Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Automatisch aangemaakt door:** AI Workflow Manager

## Volgende Stappen
- [ ] Reproduceer de bug
- [ ] Identificeer root cause
- [ ] Implementeer fix
- [ ] Test de oplossing
- [ ] Deploy naar productie

---
*Dit issue is automatisch aangemaakt door de AI Workflow Manager*
                """
                
                # GitHub issue aanmaken via Composio
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{
                        "role": "user", 
                        "content": f"Maak een GitHub issue aan met titel: {issue_title} en body: {issue_body}"
                    }],
                    tools=tools
                )
                
                self.log_action("GITHUB_ISSUE_CREATED", f"Issue aangemaakt voor: {error[:50]}")
                
            return True
            
        except Exception as e:
            self.log_action("ERROR", f"Fout bij aanmaken GitHub issue: {str(e)}")
            return False
    
    def scan_for_errors(self) -> List[str]:
        """Scan project bestanden voor potentiële errors en bugs"""
        error_patterns = [
            'console.error',
            'throw new Error',
            'catch (error)',
            'TODO: FIX',
            'FIXME',
            'BUG:',
            'HACK:'
        ]
        
        found_errors = []
        
        # Scan belangrijke directories
        scan_dirs = ['apps/api', 'apps/mobile', 'apps/workers', 'packages']
        
        for scan_dir in scan_dirs:
            dir_path = os.path.join(self.project_root, scan_dir)
            if not os.path.exists(dir_path):
                continue
                
            for root, dirs, files in os.walk(dir_path):
                # Skip node_modules
                if 'node_modules' in root:
                    continue
                    
                for file in files:
                    if file.endswith(('.ts', '.tsx', '.js', '.jsx', '.py')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                for i, line in enumerate(content.split('\n'), 1):
                                    for pattern in error_patterns:
                                        if pattern.lower() in line.lower():
                                            error_info = f"{file_path}:{i} - {line.strip()}"
                                            found_errors.append(error_info)
                        except:
                            continue
        
        return found_errors[:10]  # Limiteer tot 10 errors
    
    def run_automated_workflow(self):
        """Voer de volledige geautomatiseerde workflow uit"""
        print("🚀 Yannova CRM AI Workflow Manager gestart...")
        self.log_action("WORKFLOW_STARTED", "Automatische workflow gestart")
        
        # 1. Analyseer code wijzigingen
        changes = self.analyze_code_changes()
        
        # 2. Genereer smart commit message als er wijzigingen zijn
        if changes['has_changes']:
            commit_message = self.generate_smart_commit_message(changes)
            print(f"💡 Voorgestelde commit message: {commit_message}")
            
            # Vraag gebruiker of commit moet worden uitgevoerd
            user_input = input("Wil je deze commit message gebruiken? (y/n): ")
            if user_input.lower() == 'y':
                try:
                    subprocess.run(['git', 'add', '.'], cwd=self.project_root)
                    subprocess.run(['git', 'commit', '-m', commit_message], cwd=self.project_root)
                    self.log_action("GIT_COMMIT", f"Commit uitgevoerd: {commit_message}")
                except Exception as e:
                    self.log_action("ERROR", f"Fout bij git commit: {str(e)}")
        
        # 3. Scan voor errors en maak issues aan
        errors = self.scan_for_errors()
        if errors:
            print(f"⚠️  {len(errors)} potentiële problemen gevonden")
            for error in errors[:5]:
                print(f"   - {error}")
                
            user_input = input("Wil je automatisch GitHub issues aanmaken voor deze problemen? (y/n): ")
            if user_input.lower() == 'y':
                self.create_github_issue_for_bugs(errors)
        
        # 4. Project status rapport
        self.generate_status_report()
        
        print("✅ Workflow voltooid! Check het logbestand voor details.")
    
    def generate_status_report(self):
        """Genereer een status rapport van het project"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'project': 'Yannova CRM',
            'git_status': self.analyze_code_changes(),
            'error_count': len(self.scan_for_errors()),
            'workflow_log': self.log_file
        }
        
        with open('project_status_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        self.log_action("STATUS_REPORT", "Project status rapport gegenereerd")

if __name__ == "__main__":
    # Controleer of we in de juiste directory zijn
    if not os.path.exists('package.json'):
        print("❌ Fout: Voer dit script uit vanuit de root directory van je Yannova CRM project")
        exit(1)
    
    # Start de workflow manager
    workflow_manager = YannovaCRMWorkflowManager()
    workflow_manager.run_automated_workflow()
