# 🚀 Simpele Email Setup - Alleen Resend!

## Stap 1: <PERSON><PERSON>send API Key
1. Ga naar https://resend.com/api-keys
2. Klik "Create API Key"
3. Naam: "Yannova CRM"
4. Permission: "Full Access"
5. Domain: "yannova.be"
6. **KOPIEER DE KEY** (begint met `re_`)

## Stap 2: Maak een .env file

Maak een `.env` file in de root directory met alleen dit:

```env
# Database
DATABASE_URL="file:./dev.db"

# Email - Dat is alles wat je nodig hebt!
RESEND_API_KEY="re_PLAK_HIER_JE_KEY"
FROM_EMAIL="<EMAIL>"

# AI (je hebt dit al)
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Development
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3001"
```

Geen Zoho, geen SMTP gedoe - gewoon Resend!

## Stap 3: Herstart de server
```bash
# Stop de server (Ctrl+C)
# Start opnieuw:
npm run dev
```

## Stap 4: Test je setup
Open in browser: http://localhost:3001/api/email/config-test

Je zou moeten zien:
```json
{
  "status": "Email Configuration Check",
  "config": {
    "resendApiKey": "✅ Configured"
  },
  "smtpStatus": "✅ SMTP connection successful!"
}
```

## 🎉 Klaar!
Nu kun je:
- Emails versturen via http://localhost:3001/send-email.html
- Templates gebruiken via http://localhost:3001/email-templates.html
- CRM dashboard gebruiken via http://localhost:3001/crm-dashboard.html 