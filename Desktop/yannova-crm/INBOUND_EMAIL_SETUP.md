# 📨 Inbound Email Setup - Emails Ontvangen in CRM

## Optie 1: Em<PERSON> Forwarding (Makkelijkste)

Als je al email <NAME_EMAIL> (bijv. via Zoho/Gmail):

1. **Stel forwarding in** naar een Resend parse address
2. **In Zoho Mail:**
   - Settings → Mail Forwarding
   - Add forwarding address: `<EMAIL>`
   - Verify the forwarding
3. **In Resend:**
   - Ga naar Webhooks
   - Add webhook URL: `https://jouw-app.com/api/email/resend-webhook`

## Optie 2: Direct MX Records (Geavanceerd)

⚠️ **LET OP**: Dit vervangt je huidige email setup!

Als je ALL emails via Resend wilt ontvangen:
1. Voeg MX record toe voor `@` (root):
   ```
   MX  @  10  feedback-smtp.eu-west-1.amazonses.com
   ```
2. Configureer Resend Inbound routing

## Optie 3: Subdomain voor CRM (Aanbevolen)

Gebruik een subdomain zoals `crm.yannova.be`:

1. **Voeg MX record toe:**
   ```
   MX  crm  10  feedback-smtp.eu-west-1.amazonses.com
   ```

2. **Klanten mailen naar:** `<EMAIL>`

3. **Voordelen:**
   - Houdt je normale email (info@) intact
   - Dedicated CRM email adres
   - Makkelijk te testen

## Webhook Configuratie

1. **Voor lokaal testen**, gebruik ngrok:
   ```bash
   ngrok http 3001
   ```
   Webhook URL: `https://xxx.ngrok.io/api/email/resend-webhook`

2. **Voor productie:**
   Webhook URL: `https://crm.yannova.be/api/email/resend-webhook`

3. **In Resend Dashboard:**
   - Ga naar Webhooks
   - Add Endpoint
   - URL: je webhook URL
   - Events: Selecteer minimaal "email.received"

## Test Inbound Email

1. Stuur een test email naar je CRM adres
2. Check de logs:
   ```bash
   npm run dev
   # Kijk in terminal voor "Email received from..."
   ```
3. Check CRM Dashboard voor nieuwe contact/conversation

## Email Flow

```
Klant stuurt email → MX Record → Resend → Webhook → CRM Database
                                     ↓
                              Auto-reply (indien ingesteld)
```

## Troubleshooting

- **Emails komen niet binnen**: Check MX records (kan 24u duren)
- **Webhook fails**: Check ngrok is running voor local dev
- **Database errors**: Run `npm run db:push` eerst 