{"name": "yannova-crm", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"setup": "node setup-env.js", "build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "db:generate": "prisma generate --schema=./packages/database/schema.prisma", "db:push": "prisma db push --schema=./packages/database/schema.prisma", "db:studio": "prisma studio --schema=./packages/database/schema.prisma"}, "devDependencies": {"@turbo/gen": "^1.10.7", "prettier": "^3.0.0", "turbo": "latest"}, "dependencies": {"prisma": "^5.6.0", "@prisma/client": "^5.6.0"}, "packageManager": "npm@8.0.0"}