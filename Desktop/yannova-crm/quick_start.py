#!/usr/bin/env python3
"""
Quick Start voor Yannova CRM AI Workflows
Eenvoudige interface om snel te beginnen
"""

import os
import sys
from pathlib import Path

def print_banner():
    """Print een mooie banner"""
    print("=" * 70)
    print("🚀 YANNOVA CRM AI WORKFLOWS - QUICK START")
    print("=" * 70)
    print()

def check_setup():
    """Controleer of alles is geïnstalleerd"""
    required_files = [
        'ai_workflow_manager.py',
        'workflow_config.json',
        '.env',
        'AI_WORKFLOWS_README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Setup niet compleet. Ontbrekende bestanden:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n🔧 Run eerst: python setup_ai_workflows.py")
        return False
    
    print("✅ Setup compleet!")
    return True

def check_api_keys():
    """Controleer of API keys zijn ingesteld"""
    if not os.path.exists('.env'):
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
    
    has_openai = 'OPENAI_API_KEY=' in content and 'your_openai_api_key_here' not in content
    has_composio = 'COMPOSIO_API_KEY=' in content and 'your_composio_api_key_here' not in content
    
    return has_openai, has_composio

def show_menu():
    """Toon het hoofdmenu"""
    print("\n📋 WAT WIL JE DOEN?")
    print()
    print("1. 🎭 Demo uitvoeren (geen API keys nodig)")
    print("2. 🚀 Volledige workflow uitvoeren")
    print("3. 🔧 Setup controleren")
    print("4. 📖 Documentatie openen")
    print("5. ⚙️  Configuratie bewerken")
    print("6. 🔑 API keys instellen")
    print("7. ❌ Afsluiten")
    print()

def run_demo():
    """Voer de demo uit"""
    print("\n🎭 Demo wordt gestart...")
    os.system("python demo_workflow.py")

def run_full_workflow():
    """Voer de volledige workflow uit"""
    openai_key, composio_key = check_api_keys()
    
    if not openai_key:
        print("\n⚠️  OpenAI API key niet gevonden!")
        print("   Stel je API key in via optie 6 of bewerk het .env bestand")
        return
    
    print("\n🚀 Volledige workflow wordt gestart...")
    os.system("python ai_workflow_manager.py")

def check_setup_status():
    """Controleer en toon setup status"""
    print("\n🔍 SETUP STATUS:")
    print()
    
    # Check bestanden
    files_status = {
        'AI Workflow Manager': 'ai_workflow_manager.py',
        'Configuratie': 'workflow_config.json',
        'Environment variabelen': '.env',
        'Documentatie': 'AI_WORKFLOWS_README.md',
        'Demo script': 'demo_workflow.py'
    }
    
    for name, file in files_status.items():
        if os.path.exists(file):
            print(f"   ✅ {name}")
        else:
            print(f"   ❌ {name} - {file}")
    
    # Check API keys
    print("\n🔑 API KEYS:")
    openai_key, composio_key = check_api_keys()
    
    if openai_key:
        print("   ✅ OpenAI API key ingesteld")
    else:
        print("   ❌ OpenAI API key niet ingesteld")
    
    if composio_key:
        print("   ✅ Composio API key ingesteld")
    else:
        print("   ⚠️  Composio API key niet ingesteld (optioneel)")
    
    # Check virtual environment
    if os.path.exists('ai-agents-env'):
        print("   ✅ Virtual environment aanwezig")
    else:
        print("   ❌ Virtual environment niet gevonden")

def open_documentation():
    """Open de documentatie"""
    if os.path.exists('AI_WORKFLOWS_README.md'):
        print("\n📖 Documentatie wordt geopend...")
        # Probeer verschillende editors
        editors = ['code', 'nano', 'vim', 'cat']
        for editor in editors:
            try:
                os.system(f"{editor} AI_WORKFLOWS_README.md")
                break
            except:
                continue
    else:
        print("\n❌ Documentatie niet gevonden")

def edit_config():
    """Bewerk de configuratie"""
    if os.path.exists('workflow_config.json'):
        print("\n⚙️  Configuratie wordt geopend...")
        # Probeer verschillende editors
        editors = ['code', 'nano', 'vim', 'cat']
        for editor in editors:
            try:
                os.system(f"{editor} workflow_config.json")
                break
            except:
                continue
    else:
        print("\n❌ Configuratie bestand niet gevonden")

def setup_api_keys():
    """Help bij het instellen van API keys"""
    print("\n🔑 API KEYS INSTELLEN")
    print("=" * 40)
    
    if not os.path.exists('.env'):
        print("❌ .env bestand niet gevonden. Run eerst de setup.")
        return
    
    print("\n📋 BENODIGDE API KEYS:")
    print()
    print("1. 🤖 OpenAI API Key (VEREIST)")
    print("   - Ga naar: https://platform.openai.com/api-keys")
    print("   - Maak een nieuwe API key aan")
    print("   - Kopieer de key")
    print()
    print("2. 🔗 Composio API Key (OPTIONEEL)")
    print("   - Run: composio login")
    print("   - Of ga naar: https://app.composio.dev")
    print()
    print("3. 🐙 GitHub Token (OPTIONEEL)")
    print("   - Ga naar: https://github.com/settings/tokens")
    print("   - Maak een Personal Access Token")
    print()
    
    choice = input("Wil je nu je OpenAI API key instellen? (y/n): ")
    if choice.lower() == 'y':
        api_key = input("Plak je OpenAI API key hier: ").strip()
        if api_key and api_key != 'your_openai_api_key_here':
            # Update .env bestand
            with open('.env', 'r') as f:
                content = f.read()
            
            content = content.replace('OPENAI_API_KEY=your_openai_api_key_here', 
                                    f'OPENAI_API_KEY={api_key}')
            
            with open('.env', 'w') as f:
                f.write(content)
            
            print("✅ OpenAI API key opgeslagen!")
        else:
            print("❌ Geen geldige API key ingevoerd")
    
    print("\n💡 TIP: Bewerk het .env bestand handmatig voor meer opties")

def main():
    """Hoofdfunctie"""
    print_banner()
    
    # Controleer of we in de juiste directory zijn
    if not os.path.exists('package.json'):
        print("❌ Fout: Voer dit script uit vanuit de root directory van je Yannova CRM project")
        return
    
    # Controleer setup
    if not check_setup():
        print("\n🔧 Run eerst de setup:")
        print("   python setup_ai_workflows.py")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("Kies een optie (1-7): ").strip()
            
            if choice == '1':
                run_demo()
            elif choice == '2':
                run_full_workflow()
            elif choice == '3':
                check_setup_status()
            elif choice == '4':
                open_documentation()
            elif choice == '5':
                edit_config()
            elif choice == '6':
                setup_api_keys()
            elif choice == '7':
                print("\n👋 Tot ziens!")
                break
            else:
                print("\n❌ Ongeldige keuze. Probeer opnieuw.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Tot ziens!")
            break
        except Exception as e:
            print(f"\n❌ Fout: {e}")
        
        input("\nDruk op Enter om door te gaan...")

if __name__ == "__main__":
    main()
