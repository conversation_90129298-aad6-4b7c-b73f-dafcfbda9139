#!/usr/bin/env python3
"""
CrewAI Multi-Agent System voor Yannova CRM
Gebruikt lokale LM Studio modellen voor AI agents
"""

import os
import requests
from typing import Dict, Any, Optional
try:
    from crewai import Agent, Task, Crew
    from crewai.tools import BaseTool
    from langchain_openai import ChatOpenAI
    CREWAI_AVAILABLE = True
except ImportError:
    print("⚠️  CrewAI not available. Run: pip install composio_crewai crewai langchain_openai")
    CREWAI_AVAILABLE = False

# Configuratie voor LM Studio
LM_STUDIO_BASE_URL = "http://127.0.0.1:1234/v1"
LM_STUDIO_API_KEY = "lm-studio"  # Dummy key

# LM Studio LLM configuratie
def create_lm_studio_llm():
    """Maak een LangChain LLM die LM Studio gebruikt met OpenAI fallback"""
    if not CREWAI_AVAILABLE:
        return None
    
    # Probeer eerst LM Studio
    try:
        # Test of LM Studio beschikbaar is
        test_response = requests.get(f"{LM_STUDIO_BASE_URL}/models", timeout=5)
        if test_response.status_code == 200:
            print("✅ LM Studio beschikbaar - gebruik lokale AI")
            return ChatOpenAI(
                base_url=LM_STUDIO_BASE_URL,
                api_key=LM_STUDIO_API_KEY,
                model="mistral-7b-instruct-v0.1",
                temperature=0.7,
                max_tokens=1000
            )
    except Exception as e:
        print(f"⚠️  LM Studio niet beschikbaar: {e}")
    
    # Fallback naar OpenAI
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        print("🔄 Fallback naar OpenAI API")
        return ChatOpenAI(
            api_key=openai_key,
            model="gpt-3.5-turbo",
            temperature=0.7,
            max_tokens=1000
        )
    else:
        print("❌ Geen OpenAI API key gevonden in environment")
        # Gebruik mock LLM voor testing
        print("🧪 Gebruik mock LLM voor testing")
        return MockLLM()

class MockLLM:
    """Mock LLM voor testing zonder echte API"""
    
    def __init__(self):
        self.model_name = "mock-llm"
    
    def invoke(self, messages):
        """Mock response voor testing"""
        if isinstance(messages, str):
            prompt = messages
        else:
            prompt = str(messages)
            
        return MockResponse(f"""
# CRM Feature Plan

## Nieuwe Feature: {prompt[:50]}...

### 1. Analyse
- Feature is gerelateerd aan: CRM optimalisatie
- Doelgroep: Yannova CRM gebruikers
- Verwachte impact: Hoog

### 2. Technische Specificaties
- Platform: Vue.js + TypeScript
- Backend: Next.js API routes
- Database: Prisma + PostgreSQL
- AI integratie: LM Studio + OpenAI fallback

### 3. Implementatie Plan
**Week 1-2:**
- Requirements analyse
- UI/UX design
- Database schema

**Week 3-4:**
- Backend API development
- Frontend componenten
- Testing & debugging

**Week 5:**
- Deployment
- Documentatie
- Training

### 4. Resources
- 1 Frontend developer (Vue.js)
- 1 Backend developer (Node.js)
- 1 AI specialist
- 2 weken development tijd

### 5. Risico's
- API integratie complexiteit
- Performance impact
- User adoption

### 6. Success Metrics
- User engagement +30%
- Task completion time -20%
- Customer satisfaction +15%

*Gegenereerd door AI Project Manager*
        """)

class MockResponse:
    def __init__(self, content):
        self.content = content

# Tools voor agents
def call_yannova_api(endpoint: str, data: Optional[Dict[str, Any]] = None) -> str:
    """Tool om Yannova CRM API aan te roepen"""
    try:
        url = f"http://localhost:3001/api/{endpoint}"
        if data:
            response = requests.post(url, json=data)
        else:
            response = requests.get(url)
        
        if response.status_code == 200:
            return str(response.json())
        else:
            return f"API Error: {response.status_code} - {response.text}"
    except Exception as e:
        return f"Error calling API: {str(e)}"

# CrewAI compatible tools
class CRMAnalysisTool(BaseTool):
    name: str = "CRM_Analysis"
    description: str = "Analyseer CRM data en geef business insights"
    
    def _run(self, data: str) -> str:
        return call_yannova_api("ai/ask", {
            "question": f"Analyseer deze CRM data en geef insights: {data}"
        })

class CodeGenerationTool(BaseTool):
    name: str = "Code_Generator"
    description: str = "Genereer TypeScript/JavaScript code voor CRM features"
    
    def _run(self, prompt: str) -> str:
        return call_yannova_api("ai/coding-assistant", {
            "code": prompt,
            "action": "generate",
            "language": "typescript"
        })

class ProjectPlanningTool(BaseTool):
    name: str = "Project_Planner"
    description: str = "Maak gedetailleerde projectplannen voor CRM ontwikkeling"
    
    def _run(self, description: str) -> str:
        return call_yannova_api("ai/project-planning", {
            "description": description,
            "projectType": "crm-feature"
        })

class YannovaAPITool(BaseTool):
    name: str = "Yannova_API"
    description: str = "Roep Yannova CRM API endpoints aan"
    
    def _run(self, endpoint: str) -> str:
        return call_yannova_api(endpoint)

# Definieer tools
def create_tools():
    """Maak tools voor agents"""
    if not CREWAI_AVAILABLE:
        return {}
        
    return {
        "crm_analysis": CRMAnalysisTool(),
        "code_generation": CodeGenerationTool(),
        "project_planning": ProjectPlanningTool(),
        "api": YannovaAPITool()
    }

# Agent definities
def create_agents():
    """Maak alle AI agents"""
    if not CREWAI_AVAILABLE:
        print("❌ CrewAI niet beschikbaar")
        return {}
        
    llm = create_lm_studio_llm()
    tools = create_tools()
    
    # 1. CRM Analyst Agent
    crm_analyst = Agent(
        role="CRM Data Analyst",
        goal="Analyseer CRM data en identificeer trends, problemen en kansen",
        backstory="""Je bent een ervaren CRM data analist bij Yannova. 
        Je specialiseert in het vinden van patronen in klantdata en het voorstellen van verbeteringen.
        Je spreekt Nederlands en geeft concrete, actionable insights.""",
        tools=[tools["crm_analysis"], tools["api"]],
        llm=llm,
        verbose=True
    )
    
    # 2. Lead Developer Agent  
    lead_developer = Agent(
        role="Lead CRM Developer",
        goal="Ontwikkel en verbeter CRM features met hoogwaardige code",
        backstory="""Je bent een senior TypeScript/Vue.js developer bij Yannova.
        Je bouwt schaalbare CRM oplossingen en integreert AI functionaliteit.
        Je schrijft clean, geoptimaliseerde code en volgt best practices.""",
        tools=[tools["code_generation"], tools["api"]],
        llm=llm,
        verbose=True
    )
    
    # 3. Project Manager Agent
    project_manager = Agent(
        role="Technical Project Manager", 
        goal="Plan en coördineer CRM ontwikkelingsprojecten",
        backstory="""Je bent een ervaren technical project manager bij Yannova.
        Je plant projecten, schat tijdslijnen in en coördineert tussen teams.
        Je zorgt voor realistische planning en risicomanagement.""",
        tools=[tools["project_planning"], tools["api"]],
        llm=llm,
        verbose=True
    )
    
    # 4. AI Integration Specialist
    ai_specialist = Agent(
        role="AI Integration Specialist",
        goal="Integreer AI functionaliteit in CRM workflows",
        backstory="""Je bent een AI specialist die focust op het integreren van 
        machine learning en AI in CRM systemen. Je optimaliseert AI workflows
        en zorgt voor naadloze integratie met bestaande systemen.""",
        tools=[tools["crm_analysis"], tools["code_generation"], tools["api"]],
        llm=llm,
        verbose=True
    )
    
    return {
        "analyst": crm_analyst,
        "developer": lead_developer, 
        "pm": project_manager,
        "ai_specialist": ai_specialist
    }

def create_crm_development_crew():
    """Maak een crew voor CRM ontwikkeling"""
    if not CREWAI_AVAILABLE:
        print("❌ CrewAI niet beschikbaar")
        return None
        
    agents = create_agents()
    
    # Definieer taken
    analysis_task = Task(
        description="""Analyseer de huidige CRM performance en identificeer:
        1. Belangrijkste bottlenecks
        2. Gebruikersgedrag patronen  
        3. Verbeterkansen
        4. ROI van AI integratie
        
        Geef concrete aanbevelingen in het Nederlands.""",
        agent=agents["analyst"],
        expected_output="Gedetailleerd analyserapport met concrete aanbevelingen"
    )
    
    planning_task = Task(
        description="""Maak een projectplan voor de voorgestelde verbeteringen:
        1. Technische architectuur
        2. Implementatie roadmap
        3. Resource planning
        4. Risico assessment
        5. Timeline met milestones
        
        Plan in het Nederlands met realistische tijdsinschattingen.""",
        agent=agents["pm"],
        expected_output="Compleet projectplan met timeline en resources"
    )
    
    development_task = Task(
        description="""Ontwikkel de technische implementatie:
        1. Code architectuur
        2. API endpoints
        3. Database schema wijzigingen
        4. Frontend componenten
        5. Testing strategie
        
        Gebruik TypeScript en Vue.js best practices.""",
        agent=agents["developer"],
        expected_output="Technische implementatie met code voorbeelden"
    )
    
    ai_integration_task = Task(
        description="""Optimaliseer AI integratie:
        1. Model selectie en routing
        2. Performance optimalisatie
        3. Fallback strategieën
        4. Monitoring en logging
        5. User experience verbetering
        
        Focus op LM Studio integratie en lokale AI.""",
        agent=agents["ai_specialist"],
        expected_output="AI integratie strategie met implementatie details"
    )
    
    # Maak crew
    crew = Crew(
        agents=list(agents.values()),
        tasks=[analysis_task, planning_task, development_task, ai_integration_task],
        verbose=True,
        process="sequential"  # Taken worden sequentieel uitgevoerd
    )
    
    return crew

def run_crm_optimization():
    """Run een complete CRM optimalisatie workflow"""
    print("🚀 Starting Yannova CRM Multi-Agent Optimization...")
    print("🤖 Using LM Studio for AI processing...")
    
    if not CREWAI_AVAILABLE:
        print("❌ CrewAI packages niet geïnstalleerd")
        return None
    
    try:
        crew = create_crm_development_crew()
        if crew is None:
            return None
            
        result = crew.kickoff()
        
        print("\n" + "="*50)
        print("✅ MULTI-AGENT ANALYSIS COMPLETE")
        print("="*50)
        print(result)
        
        return result
        
    except Exception as e:
        print(f"❌ Error in multi-agent workflow: {str(e)}")
        return None

def run_custom_task(task_description: str):
    """Run een custom taak met de agents"""
    print(f"🎯 Running custom task: {task_description}")
    
    if not CREWAI_AVAILABLE:
        print("❌ CrewAI packages niet geïnstalleerd")
        return None
    
    agents = create_agents()
    
    # Bepaal welke agent het beste past
    if "analys" in task_description.lower() or "data" in task_description.lower():
        agent = agents["analyst"]
    elif "code" in task_description.lower() or "ontwikkel" in task_description.lower():
        agent = agents["developer"]
    elif "plan" in task_description.lower() or "project" in task_description.lower():
        agent = agents["pm"]
    else:
        agent = agents["ai_specialist"]
    
    task = Task(
        description=task_description,
        agent=agent,
        expected_output="Gedetailleerd antwoord op de gestelde vraag"
    )
    
    crew = Crew(
        agents=[agent],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        print(f"\n✅ Task completed: {result}")
        return result
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Custom task van command line
        task = " ".join(sys.argv[1:])
        run_custom_task(task)
    else:
        # Run complete optimalisatie
        run_crm_optimization() 