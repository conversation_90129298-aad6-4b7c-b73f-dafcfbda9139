<template>
  <div class="contact-card" @click="openContact">
    <div class="contact-avatar">
      {{ contact.name?.charAt(0) || contact.email.charAt(0) }}
    </div>
    
    <div class="contact-info">
      <h3>{{ contact.name || contact.email }}</h3>
      <p class="company">{{ contact.company }}</p>
      <div class="status-badge" :class="contact.status.toLowerCase()">
        {{ contact.status }}
      </div>
    </div>
    
    <div class="contact-stats">
      <div class="stat">
        <span class="count">{{ contact.conversationCount }}</span>
        <span class="label">Gesprekken</span>
      </div>
      <div class="stat">
        <span class="count">{{ contact.dealCount }}</span>
        <span class="label">Deals</span>
      </div>
    </div>
    
    <div class="quick-actions">
      <button @click.stop="callContact" class="action-btn">
        📞
      </button>
      <button @click.stop="openWhatsApp" class="action-btn">
        💬
      </button>
      <button @click.stop="createTask" class="action-btn">
        ✅
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Contact } from '../types';

const props = defineProps<{
  contact: Contact;
}>();

const emit = defineEmits<{
  open: [contact: Contact];
  call: [contact: Contact];
  whatsapp: [contact: Contact];
  task: [contact: Contact];
}>();

const openContact = () => emit('open', props.contact);
const callContact = () => emit('call', props.contact);
const openWhatsApp = () => emit('whatsapp', props.contact);
const createTask = () => emit('task', props.contact);
</script>

<style scoped>
.contact-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 12px;
  cursor: pointer;
  transition: transform 0.2s;
}

.contact-card:hover {
  transform: translateY(-2px);
}

.contact-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 16px;
}

.contact-info {
  flex: 1;
}

.contact-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.company {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.lead { background: #fef3c7; color: #92400e; }
.status-badge.prospect { background: #dbeafe; color: #1e40af; }
.status-badge.customer { background: #d1fae5; color: #065f46; }

.contact-stats {
  display: flex;
  gap: 16px;
  margin-right: 16px;
}

.stat {
  text-align: center;
}

.stat .count {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #374151;
}

.stat .label {
  font-size: 12px;
  color: #6b7280;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #f3f4f6;
  cursor: pointer;
  transition: background 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}
</style>