{"name": "yannova-crm-mobile", "version": "0.1.0", "private": true, "scripts": {"build": "vite build", "dev": "vite --port 3002", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.4.0", "typescript": "~5.1.6", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.4", "vue-tsc": "^1.8.5"}}