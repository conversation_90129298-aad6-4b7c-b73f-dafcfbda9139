import { NextApiRequest, NextApiResponse } from 'next'
import { EmailService } from '../../../lib/email-service'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { to, subject, content, testType } = req.body

  try {
    let result = false;

    switch (testType) {
      case 'auto-reply':
        result = await EmailService.sendAutoReply(to, subject);
        break;
      case 'intelligent':
        result = await EmailService.sendIntelligentReply(to, subject, content);
        break;
      case 'direct':
        result = await EmailService.sendEmail(to, subject, content);
        break;
      default:
        return res.status(400).json({ error: 'Invalid test type' });
    }

    if (result) {
      res.json({ success: true, message: `Email sent successfully via ${testType}` });
    } else {
      res.json({ success: false, message: '<PERSON><PERSON> failed to send' });
    }
  } catch (error) {
    console.error('Email test error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
} 