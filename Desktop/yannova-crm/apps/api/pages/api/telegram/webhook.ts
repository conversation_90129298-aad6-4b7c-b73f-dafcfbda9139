import { TelegramService } from '../../../lib/telegram-service';
import { prisma } from '../../../lib/prisma';
import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  const { message } = req.body;
  
  if (!message) {
    return res.json({ ok: true });
  }
  
  const telegram = new TelegramService();
  const chatId = message.chat.id.toString();
  const text = message.text;
  
  try {
    if (text?.startsWith('/')) {
      await handleCommand(telegram, chatId, text, message);
    } else {
      await handleMessage(telegram, chatId, text, message);
    }
    
    res.json({ ok: true });
  } catch (error) {
    console.error('Telegram webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleCommand(telegram: TelegramService, chatId: string, command: string, message: any) {
  // Handle Telegram commands here
  if (command === '/start') {
    await telegram.sendMessage(chatId, 'Welkom bij YannovaCRM Bot!');
  }
}

async function handleMessage(telegram: TelegramService, chatId: string, text: string, message: any) {
  // Handle regular messages here
  await telegram.sendMessage(chatId, `Je zei: ${text}`);
}