import { NextApiRequest, NextApiResponse } from 'next'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (req.method === 'POST') {
    try {
      const conversation = await prisma.conversation.update({
        where: { id: id as string },
        data: { readAt: new Date() }
      })

      res.json({ success: true, conversation })
    } catch (error) {
      console.error('Mark read error:', error)
      res.status(500).json({ error: 'Internal server error' })
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' })
  }
} 