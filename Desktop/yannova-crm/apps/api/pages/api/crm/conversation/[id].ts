import { NextApiRequest, NextApiResponse } from 'next'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (req.method === 'GET') {
    try {
      const conversation = await prisma.conversation.findUnique({
        where: { id: id as string },
        include: {
          contact: true
        }
      })

      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' })
      }

      res.json(conversation)
    } catch (error) {
      console.error('Conversation error:', error)
      res.status(500).json({ error: 'Internal server error' })
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' })
  }
} 