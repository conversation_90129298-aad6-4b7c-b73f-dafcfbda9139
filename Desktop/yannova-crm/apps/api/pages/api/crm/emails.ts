import { NextApiRequest, NextApiResponse } from 'next'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Mock data for demo
  const mockEmails = [
    {
      id: '1',
      subject: 'Vraag over CRM systeem',
      content: '<PERSON><PERSON>, ik ben g<PERSON>nteresseerd in jullie CRM oplossing...',
      createdAt: new Date(),
      readAt: null,
      contact: {
        id: '1',
        email: '<EMAIL>',
        name: '<PERSON>',
        company: 'Tech Solutions B.V.'
      }
    },
    {
      id: '2',
      subject: 'Offerte aanvraag Telegram bot',
      content: 'Kunnen jullie een offerte sturen voor een Telegram bot?',
      createdAt: new Date(Date.now() - 86400000),
      readAt: new Date(),
      contact: {
        id: '2',
        email: '<EMAIL>',
        name: '<PERSON>',
        company: 'Marketing Pro'
      }
    }
  ]

  try {
    const limit = parseInt(req.query.limit as string) || 10
    const offset = parseInt(req.query.offset as string) || 0

    const conversations = await prisma.conversation.findMany({
      where: {
        type: 'EMAIL'
      },
      include: {
        contact: {
          select: {
            id: true,
            email: true,
            name: true,
            company: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    res.json(conversations)
  } catch (error) {
    console.error('Emails error:', error)
    // Return mock data if database fails
    const requestLimit = parseInt(req.query.limit as string) || 10
    res.json(mockEmails.slice(0, requestLimit))
  }
} 