import { NextApiRequest, NextApiResponse } from 'next'
import { OpenAIService } from '../../../lib/openai-service'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { type, content, subject } = req.body

  try {
    let result;

    switch (type) {
      case 'reply':
        result = await OpenAIService.generateEmailReply(
          content || 'I<PERSON> ben geïnteresseerd in jullie CRM systeem. Kunnen jullie meer informatie sturen?',
          subject
        );
        break;

      case 'sentiment':
        result = await OpenAIService.analyzeSentiment(
          content || 'Ik ben zeer tevreden met jullie service!'
        );
        break;

      case 'categorize':
        result = await OpenAIService.categorizeEmail(
          subject || 'Offerte aanvraag',
          content || 'Kunnen jullie een offerte sturen voor een Telegram bot?'
        );
        break;

      case 'subjects':
        result = await OpenAIService.generateSubjectSuggestions(
          content || 'Ik wil graag meer weten over jullie bot development services.'
        );
        break;

      case 'extract':
        result = await OpenAIService.extractKeyInfo(
          content || 'Hallo, mijn naam is Jan de Vries van Tech Solutions. Kunnen jullie me bellen op 06-12345678? Het is vrij urgent.'
        );
        break;

      default:
        return res.status(400).json({ error: 'Invalid test type' });
    }

    res.json({
      success: true,
      type,
      result,
      hasOpenAI: !!process.env.OPENAI_API_KEY
    });
  } catch (error: any) {
    console.error('AI test error:', error);
    res.status(500).json({ 
      error: 'AI test failed',
      message: error.message,
      hasOpenAI: !!process.env.OPENAI_API_KEY
    });
  }
} 