import { NextApiRequest, NextApiResponse } from 'next';
import { AIOrchestrator } from '../../../lib/ai-orchestrator';
import { AutoAIWorkflows } from '../../../lib/auto-ai-workflows';

const orchestrator = new AIOrchestrator();
const autoAI = new AutoAIWorkflows();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { 
      prompt, 
      taskType = 'coding', 
      language = 'typescript',
      context = '',
      priority = 'high',
      requester = 'claude'
    } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    console.log(`🤖 Claude → LM Studio: ${taskType} task`);
    console.log(`📝 Prompt preview: ${prompt.substring(0, 100)}...`);

    let result;

    // Route naar juiste AI functie op basis van task type
    switch (taskType) {
      case 'coding':
      case 'code-review':
      case 'debug':
      case 'optimize':
        result = await orchestrator.codingAssistant(prompt, language);
        break;
        
      case 'planning':
      case 'architecture':
        result = await orchestrator.planningAssistant(prompt);
        break;
        
      case 'analysis':
      case 'crm-analysis':
        result = await orchestrator.crmAnalysis(prompt, context || 'general-analysis');
        break;
        
      case 'chat':
      case 'question':
      case 'help':
        result = await orchestrator.query(prompt, {
          type: 'chat',
          priority: priority as 'low' | 'medium' | 'high',
          context: `Claude vraagt: ${context}`
        });
        break;
        
      case 'email-processing':
        // Speciaal voor email verwerking
        const emailData = JSON.parse(context);
        result = await autoAI.processIncomingEmail(emailData);
        break;
        
      case 'bot-enhancement':
        result = await autoAI.enhanceBotResponse(prompt, context);
        break;
        
      default:
        // Algemene query
        result = await orchestrator.query(prompt, {
          type: 'chat',
          priority: priority as 'low' | 'medium' | 'high',
          context: `${requester}: ${taskType}`
        });
    }

    // Log het resultaat
    console.log(`✅ LM Studio → Claude: ${result.model} (${result.provider})`);

    res.status(200).json({
      success: true,
      taskType,
      requester,
      result,
      metadata: {
        promptLength: prompt.length,
        responseLength: result.response?.length || 0,
        processingTime: new Date().toISOString(),
        modelUsed: result.model,
        provider: result.provider
      }
    });

  } catch (error) {
    console.error('Claude→LM Studio orchestration error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to process AI request',
      details: error.message,
      fallback: 'Claude will handle this task directly'
    });
  }
} 