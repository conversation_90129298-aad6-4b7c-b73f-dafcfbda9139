import { NextApiRequest, NextApiResponse } from 'next';
import { AIOrchestrator } from '../../../lib/ai-orchestrator';

const orchestrator = new AIOrchestrator();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { code, language = 'typescript', action = 'review' } = req.body;

    if (!code) {
      return res.status(400).json({ error: 'Code is required' });
    }

    let result;
    
    switch (action) {
      case 'review':
        result = await orchestrator.codingAssistant(
          `Review deze ${language} code en geef feedback:\n\n${code}`,
          language
        );
        break;
        
      case 'optimize':
        result = await orchestrator.codingAssistant(
          `Optimaliseer deze ${language} code voor performance en leesbaarheid:\n\n${code}`,
          language
        );
        break;
        
      case 'debug':
        result = await orchestrator.codingAssistant(
          `Debug deze ${language} code en vind potentiële bugs:\n\n${code}`,
          language
        );
        break;
        
      case 'explain':
        result = await orchestrator.codingAssistant(
          `Leg deze ${language} code uit in eenvoudige Nederlandse termen:\n\n${code}`,
          language
        );
        break;
        
      case 'refactor':
        result = await orchestrator.codingAssistant(
          `Refactor deze ${language} code volgens best practices:\n\n${code}`,
          language
        );
        break;
        
      default:
        return res.status(400).json({ error: 'Invalid action. Use: review, optimize, debug, explain, refactor' });
    }
    
    res.status(200).json({
      ...result,
      action,
      language,
      originalCode: code
    });
  } catch (error) {
    console.error('Coding assistant error:', error);
    res.status(500).json({ 
      error: 'Failed to process code',
      details: error.message 
    });
  }
} 