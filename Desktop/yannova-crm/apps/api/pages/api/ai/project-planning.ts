import { NextApiRequest, NextApiResponse } from 'next';
import { AIOrchestrator } from '../../../lib/ai-orchestrator';

const orchestrator = new AIOrchestrator();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { 
      projectType = 'crm-feature',
      description,
      requirements = [],
      timeline = 'medium',
      complexity = 'medium'
    } = req.body;

    if (!description) {
      return res.status(400).json({ error: 'Project description is required' });
    }

    const projectDetails = `
Project Type: ${projectType}
Beschrijving: ${description}
Requirements: ${requirements.join(', ')}
Timeline: ${timeline}
Complexiteit: ${complexity}

Context: Dit is voor het Yannova CRM systeem dat gebruik maakt van:
- Next.js API routes
- Prisma database
- Telegram/WhatsApp integratie
- Email service (Resend)
- LM Studio voor AI
- Vue.js mobile app
`;

    const result = await orchestrator.planningAssistant(projectDetails);
    
    res.status(200).json({
      ...result,
      projectType,
      timeline,
      complexity,
      requirements,
      suggestions: generateProjectSuggestions(projectType, complexity)
    });
  } catch (error) {
    console.error('Project planning error:', error);
    res.status(500).json({ 
      error: 'Failed to generate project plan',
      details: error.message 
    });
  }
}

function generateProjectSuggestions(projectType: string, complexity: string): string[] {
  const suggestions: string[] = [];
  
  if (projectType === 'crm-feature') {
    suggestions.push('🔄 Integreer met bestaande CRM database schema');
    suggestions.push('📱 Overweeg mobile app impact');
    suggestions.push('🤖 Gebruik AI orchestrator voor intelligente features');
  }
  
  if (projectType === 'bot-integration') {
    suggestions.push('📞 Test met Telegram webhook lokaal via ngrok');
    suggestions.push('💬 Implementeer fallback berichten');
    suggestions.push('🔐 Beveilig webhook endpoints');
  }
  
  if (complexity === 'high') {
    suggestions.push('🧪 Implementeer uitgebreide testing');
    suggestions.push('📊 Monitor performance metrics');
    suggestions.push('🔄 Plan voor iteratieve releases');
  }
  
  suggestions.push('💾 Backup database voor productie changes');
  suggestions.push('📝 Update API documentatie');
  
  return suggestions;
} 