import { NextApiRequest, NextApiResponse } from 'next';
import { OpenAIService } from '../../../lib/openai-service';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, context } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email content is required' });
    }

    const reply = await OpenAIService.generateEmailReply(email, context);
    
    res.status(200).json({ 
      reply,
      originalEmail: email,
      context,
      timestamp: new Date().toISOString(),
      aiProvider: process.env.USE_LM_STUDIO === 'true' ? 'LM Studio' : 'OpenAI'
    });
  } catch (error) {
    console.error('Email reply test error:', error);
    res.status(500).json({ error: 'Failed to generate email reply' });
  }
} 