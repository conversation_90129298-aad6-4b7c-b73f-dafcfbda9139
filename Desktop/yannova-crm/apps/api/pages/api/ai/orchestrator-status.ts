import { NextApiRequest, NextApiResponse } from 'next';
import { AIOrchestrator } from '../../../lib/ai-orchestrator';

const orchestrator = new AIOrchestrator();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const modelStatus = await orchestrator.checkModelStatus();
    
    const totalModels = Object.keys(modelStatus).length;
    const onlineModels = Object.values(modelStatus).filter(Boolean).length;
    const offlineModels = totalModels - onlineModels;
    
    res.status(200).json({
      status: 'success',
      summary: {
        total: totalModels,
        online: onlineModels,
        offline: offlineModels,
        healthScore: Math.round((onlineModels / totalModels) * 100)
      },
      models: modelStatus,
      timestamp: new Date().toISOString(),
      recommendations: generateRecommendations(modelStatus)
    });
  } catch (error) {
    console.error('Orchestrator status error:', error);
    res.status(500).json({ 
      error: 'Failed to check model status',
      details: error.message 
    });
  }
}

function generateRecommendations(modelStatus: {[key: string]: boolean}): string[] {
  const recommendations: string[] = [];
  
  if (!modelStatus['mistral-code']) {
    recommendations.push('🔧 Start LM Studio en laad het Mistral model voor optimale coding performance');
  }
  
  if (!modelStatus['deepseek-coder']) {
    recommendations.push('💻 Overweeg het DeepSeek Coder model te laden voor gespecialiseerde code taken');
  }
  
  if (!modelStatus['openai-gpt4'] && !modelStatus['openai-gpt3']) {
    recommendations.push('🔑 Voeg een OpenAI API key toe voor fallback functionaliteit');
  }
  
  const onlineCount = Object.values(modelStatus).filter(Boolean).length;
  if (onlineCount === 0) {
    recommendations.push('⚠️ Geen modellen beschikbaar - check LM Studio en API configuratie');
  } else if (onlineCount === Object.keys(modelStatus).length) {
    recommendations.push('✅ Alle modellen zijn online - optimale AI performance!');
  }
  
  return recommendations;
} 