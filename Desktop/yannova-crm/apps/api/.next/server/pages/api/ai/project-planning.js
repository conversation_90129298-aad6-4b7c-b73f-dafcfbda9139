"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/project-planning";
exports.ids = ["pages/api/ai/project-planning"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fproject-planning&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fproject-planning.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fproject-planning&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fproject-planning.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/ai/project-planning.ts */ \"(api)/./pages/api/ai/project-planning.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/project-planning\",\n        pathname: \"/api/ai/project-planning\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_ai_project_planning_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFU19BUEkmcGFnZT0lMkZhcGklMkZhaSUyRnByb2plY3QtcGxhbm5pbmcmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRmFpJTJGcHJvamVjdC1wbGFubmluZy50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUMrRDtBQUMvRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsOERBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLDhEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCxxQyIsInNvdXJjZXMiOlsid2VicGFjazovL3lhbm5vdmEtY3JtLWFwaS8/ZDk5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL2FpL3Byb2plY3QtcGxhbm5pbmcudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9haS9wcm9qZWN0LXBsYW5uaW5nXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYWkvcHJvamVjdC1wbGFubmluZ1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fproject-planning&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fproject-planning.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/ai-orchestrator.ts":
/*!********************************!*\
  !*** ./lib/ai-orchestrator.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIOrchestrator: () => (/* binding */ AIOrchestrator)\n/* harmony export */ });\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\n/* harmony import */ var _openai_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./openai-service */ \"(api)/./lib/openai-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__]);\n([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nclass AIOrchestrator {\n    constructor(){\n        this.models = new Map();\n        this.initializeModels();\n    }\n    initializeModels() {\n        // LM Studio Models\n        this.models.set(\"mistral-code\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"planning\"\n            ],\n            priority: 1,\n            maxTokens: 4096\n        });\n        this.models.set(\"deepseek-coder\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"debugging\",\n                \"refactoring\"\n            ],\n            priority: 2,\n            maxTokens: 4096\n        });\n        // API Models (fallback)\n        this.models.set(\"openai-gpt4\", {\n            name: \"gpt-4\",\n            type: \"api\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"email\",\n                \"chat\",\n                \"planning\"\n            ],\n            priority: 3,\n            maxTokens: 8192\n        });\n        this.models.set(\"openai-gpt3\", {\n            name: \"gpt-3.5-turbo\",\n            type: \"api\",\n            capabilities: [\n                \"chat\",\n                \"email\",\n                \"analysis\"\n            ],\n            priority: 4,\n            maxTokens: 4096\n        });\n    }\n    // Intelligente model selectie\n    selectModel(task) {\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type)).sort((a, b)=>a.priority - b.priority);\n        // Speciale logica voor verschillende taken\n        switch(task.type){\n            case \"coding\":\n                // Prioriteer code-gespecialiseerde modellen\n                return availableModels.find((m)=>m.name.includes(\"coder\") || m.name.includes(\"code\")) || availableModels[0];\n            case \"analysis\":\n                // Gebruik lokale modellen voor analyse\n                return availableModels.find((m)=>m.type === \"local\") || availableModels[0];\n            case \"email\":\n                // Gebruik snelle modellen voor email\n                return availableModels.find((m)=>m.maxTokens >= 2048) || availableModels[0];\n            default:\n                return availableModels[0];\n        }\n    }\n    // Universele query functie\n    async query(prompt, task) {\n        const selectedModel = this.selectModel(task);\n        if (!selectedModel) {\n            throw new Error(`Geen geschikt model gevonden voor taak: ${task.type}`);\n        }\n        try {\n            let response;\n            let provider;\n            if (selectedModel.type === \"local\") {\n                // Gebruik LM Studio\n                response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, selectedModel.name);\n                provider = \"LM Studio\";\n            } else {\n                // Gebruik OpenAI als fallback\n                response = await this.queryOpenAI(prompt, selectedModel, task);\n                provider = \"OpenAI\";\n            }\n            return {\n                response,\n                model: selectedModel.name,\n                provider,\n                timestamp: new Date().toISOString()\n            };\n        } catch (error) {\n            console.error(`Model ${selectedModel.name} failed:`, error);\n            // Fallback naar volgende model\n            return this.fallbackQuery(prompt, task, selectedModel);\n        }\n    }\n    // Fallback systeem\n    async fallbackQuery(prompt, task, failedModel) {\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type) && model.name !== failedModel.name).sort((a, b)=>a.priority - b.priority);\n        for (const model of availableModels){\n            try {\n                let response;\n                let provider;\n                if (model.type === \"local\") {\n                    response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, model.name);\n                    provider = \"LM Studio (Fallback)\";\n                } else {\n                    response = await this.queryOpenAI(prompt, model, task);\n                    provider = \"OpenAI (Fallback)\";\n                }\n                return {\n                    response,\n                    model: model.name,\n                    provider,\n                    timestamp: new Date().toISOString()\n                };\n            } catch (error) {\n                console.error(`Fallback model ${model.name} failed:`, error);\n                continue;\n            }\n        }\n        throw new Error(\"Alle modellen zijn niet beschikbaar\");\n    }\n    // OpenAI query helper\n    async queryOpenAI(prompt, model, task) {\n        switch(task.type){\n            case \"email\":\n                return await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.generateEmailReply(prompt, task.context);\n            case \"analysis\":\n                const sentiment = await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.analyzeSentiment(prompt);\n                return `Sentiment: ${sentiment}`;\n            case \"planning\":\n            case \"coding\":\n            case \"chat\":\n                // Voor deze taken gebruiken we een generieke fallback\n                return `OpenAI fallback niet beschikbaar voor ${task.type}. Gebruik LM Studio voor deze functionaliteit.`;\n            default:\n                throw new Error(`OpenAI fallback niet geïmplementeerd voor taak: ${task.type}`);\n        }\n    }\n    // Speciale methoden voor verschillende taken\n    async codingAssistant(codePrompt, language = \"typescript\") {\n        const enhancedPrompt = `\nJe bent een expert ${language} developer. Analyseer deze code en geef concrete, bruikbare feedback:\n\n${codePrompt}\n\nGeef antwoord in het Nederlands met:\n1. Code review\n2. Verbeteringsvoorstellen\n3. Potentiële bugs\n4. Best practices\n`;\n        return this.query(enhancedPrompt, {\n            type: \"coding\",\n            priority: \"high\",\n            language\n        });\n    }\n    async crmAnalysis(data, analysisType) {\n        const prompt = `\nAnalyseer deze CRM data voor ${analysisType}:\n\n${data}\n\nGeef een Nederlandse analyse met:\n1. Belangrijkste inzichten\n2. Aanbevelingen\n3. Actie items\n`;\n        return this.query(prompt, {\n            type: \"analysis\",\n            priority: \"medium\",\n            context: analysisType\n        });\n    }\n    async planningAssistant(projectDetails) {\n        const prompt = `\nMaak een ontwikkelingsplan voor dit project:\n\n${projectDetails}\n\nGeef een gestructureerd plan met:\n1. Technische architectuur\n2. Implementatie stappen\n3. Tijdsinschattingen\n4. Risico's en mitigaties\n`;\n        return this.query(prompt, {\n            type: \"planning\",\n            priority: \"high\"\n        });\n    }\n    // Model status checker\n    async checkModelStatus() {\n        const status = {};\n        const modelKeys = Array.from(this.models.keys());\n        for (const key of modelKeys){\n            const model = this.models.get(key);\n            try {\n                if (model.type === \"local\") {\n                    await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(\"test\", model.name);\n                    status[key] = true;\n                } else {\n                    // Voor API modellen, check of de key beschikbaar is\n                    status[key] = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== \"dummy-key-for-lm-studio\";\n                }\n            } catch (error) {\n                status[key] = false;\n            }\n        }\n        return status;\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/ai-orchestrator.ts\n");

/***/ }),

/***/ "(api)/./lib/lmstudio-service.ts":
/*!*********************************!*\
  !*** ./lib/lmstudio-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryLMStudio: () => (/* binding */ queryLMStudio)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function queryLMStudio(prompt, model = \"mistral-7b-instruct-v0.1\") {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"http://127.0.0.1:1234/v1/chat/completions\", {\n            model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        }, {\n            headers: {\n                \"Authorization\": \"Bearer lm-studio\"\n            },\n            timeout: 30000 // 30 seconden timeout\n        });\n        return response.data.choices[0]?.message?.content || \"Geen antwoord ontvangen\";\n    } catch (error) {\n        console.error(\"LM Studio fout:\", error.response?.data || error.message);\n        return \"Fout bij verwerken verzoek\";\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUVuQixlQUFlQyxjQUFjQyxNQUFjLEVBQUVDLFFBQVEsMEJBQTBCO0lBQ3BGLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1KLGtEQUFVLENBQUMsNkNBQTZDO1lBQzdFRztZQUNBRyxVQUFVO2dCQUFDO29CQUFFQyxNQUFNO29CQUFRQyxTQUFTTjtnQkFBTzthQUFFO1lBQzdDTyxhQUFhO1lBQ2JDLFlBQVk7UUFDZCxHQUFHO1lBQ0RDLFNBQVM7Z0JBQUUsaUJBQWlCO1lBQW1CO1lBQy9DQyxTQUFTLE1BQU8sc0JBQXNCO1FBQ3hDO1FBRUEsT0FBT1IsU0FBU1MsSUFBSSxDQUFDQyxPQUFPLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxXQUFXO0lBQ3ZELEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQSxNQUFNWixRQUFRLEVBQUVTLFFBQVFHLE1BQU1ELE9BQU87UUFDdEUsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cz80NzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeUxNU3R1ZGlvKHByb21wdDogc3RyaW5nLCBtb2RlbCA9ICdtaXN0cmFsLTdiLWluc3RydWN0LXYwLjEnKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjEyMzQvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogcHJvbXB0IH1dLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heF90b2tlbnM6IDUwMFxuICAgIH0sIHtcbiAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIGxtLXN0dWRpbycgfSxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwICAvLyAzMCBzZWNvbmRlbiB0aW1lb3V0XG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnR2VlbiBhbnR3b29yZCBvbnR2YW5nZW4nO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xNIFN0dWRpbyBmb3V0OicsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiAnRm91dCBiaWogdmVyd2Vya2VuIHZlcnpvZWsnO1xuICB9XG59ICJdLCJuYW1lcyI6WyJheGlvcyIsInF1ZXJ5TE1TdHVkaW8iLCJwcm9tcHQiLCJtb2RlbCIsInJlc3BvbnNlIiwicG9zdCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJoZWFkZXJzIiwidGltZW91dCIsImRhdGEiLCJjaG9pY2VzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/lmstudio-service.ts\n");

/***/ }),

/***/ "(api)/./lib/openai-service.ts":
/*!*******************************!*\
  !*** ./lib/openai-service.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAIService: () => (/* binding */ OpenAIService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__]);\n([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Helper functie om te bepalen of LM Studio gebruikt moet worden\nconst shouldUseLMStudio = ()=>process.env.USE_LM_STUDIO === \"true\";\nclass OpenAIService {\n    // Generate smart email reply\n    static async generateEmailReply(originalEmail, context) {\n        try {\n            const systemPrompt = `Je bent Leon van Yannova, een vriendelijke Nederlandse AI developer en bot specialist. \n            Je antwoordt professioneel maar toegankelijk in het Nederlands.\n            Specialisaties: Telegram/WhatsApp bots, trading bots, CRM-systemen, apps.\n            Houd antwoorden kort en actionable.`;\n            const userPrompt = `Schrijf een antwoord op deze email:\\n\\n${originalEmail}\\n\\n${context ? `Context: ${context}` : \"\"}`;\n            if (shouldUseLMStudio()) {\n                const fullPrompt = `${systemPrompt}\\n\\n${userPrompt}`;\n                return await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(fullPrompt);\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-4\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: systemPrompt\n                    },\n                    {\n                        role: \"user\",\n                        content: userPrompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 500\n            });\n            return completion.choices[0].message.content || \"Kon geen antwoord genereren.\";\n        } catch (error) {\n            console.error(\"AI error:\", error);\n            return \"Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.\";\n        }\n    }\n    // Analyze email sentiment\n    static async analyzeSentiment(text) {\n        try {\n            if (shouldUseLMStudio()) {\n                const prompt = `Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\\n\\nTekst: ${text}`;\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const sentiment = parseFloat(result);\n                return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: text\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const sentiment = parseFloat(completion.choices[0].message.content || \"0\");\n            return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n        } catch (error) {\n            console.error(\"Sentiment analysis error:\", error);\n            return 0;\n        }\n    }\n    // Categorize email\n    static async categorizeEmail(subject, content) {\n        try {\n            const prompt = `Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\\n\\nOnderwerp: ${subject}\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const category = result.trim().toUpperCase();\n                return [\n                    \"QUOTE\",\n                    \"BOT\",\n                    \"URGENT\",\n                    \"GENERAL\"\n                ].includes(category) ? category : \"GENERAL\";\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Categoriseer deze email in \\xe9\\xe9n van deze categorie\\xebn: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: `Onderwerp: ${subject}\\n\\nInhoud: ${content}`\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const category = completion.choices[0].message.content?.trim().toUpperCase();\n            return [\n                \"QUOTE\",\n                \"BOT\",\n                \"URGENT\",\n                \"GENERAL\"\n            ].includes(category || \"\") ? category : \"GENERAL\";\n        } catch (error) {\n            console.error(\"Categorization error:\", error);\n            return \"GENERAL\";\n        }\n    }\n    // Generate email subject suggestions\n    static async generateSubjectSuggestions(content) {\n        try {\n            const prompt = `Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                return result.split(\"\\n\").filter((s)=>s.trim()) || [];\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: content\n                    }\n                ],\n                temperature: 0.8,\n                max_tokens: 100\n            });\n            return completion.choices[0].message.content?.split(\"\\n\").filter((s)=>s.trim()) || [];\n        } catch (error) {\n            console.error(\"Subject generation error:\", error);\n            return [\n                \"Uw aanvraag\",\n                \"Informatie verzoek\",\n                \"Contact\"\n            ];\n        }\n    }\n    // Extract key information from email\n    static async extractKeyInfo(emailContent) {\n        try {\n            const prompt = `Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\\n\\nEmail: ${emailContent}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                try {\n                    return JSON.parse(result);\n                } catch  {\n                    return {};\n                }\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: emailContent\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 200\n            });\n            return JSON.parse(completion.choices[0].message.content || \"{}\");\n        } catch (error) {\n            console.error(\"Info extraction error:\", error);\n            return {};\n        }\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/openai-service.ts\n");

/***/ }),

/***/ "(api)/./pages/api/ai/project-planning.ts":
/*!******************************************!*\
  !*** ./pages/api/ai/project-planning.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/ai-orchestrator */ \"(api)/./lib/ai-orchestrator.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst orchestrator = new _lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__.AIOrchestrator();\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { projectType = \"crm-feature\", description, requirements = [], timeline = \"medium\", complexity = \"medium\" } = req.body;\n        if (!description) {\n            return res.status(400).json({\n                error: \"Project description is required\"\n            });\n        }\n        const projectDetails = `\nProject Type: ${projectType}\nBeschrijving: ${description}\nRequirements: ${requirements.join(\", \")}\nTimeline: ${timeline}\nComplexiteit: ${complexity}\n\nContext: Dit is voor het Yannova CRM systeem dat gebruik maakt van:\n- Next.js API routes\n- Prisma database\n- Telegram/WhatsApp integratie\n- Email service (Resend)\n- LM Studio voor AI\n- Vue.js mobile app\n`;\n        const result = await orchestrator.planningAssistant(projectDetails);\n        res.status(200).json({\n            ...result,\n            projectType,\n            timeline,\n            complexity,\n            requirements,\n            suggestions: generateProjectSuggestions(projectType, complexity)\n        });\n    } catch (error) {\n        console.error(\"Project planning error:\", error);\n        res.status(500).json({\n            error: \"Failed to generate project plan\",\n            details: error.message\n        });\n    }\n}\nfunction generateProjectSuggestions(projectType, complexity) {\n    const suggestions = [];\n    if (projectType === \"crm-feature\") {\n        suggestions.push(\"\\uD83D\\uDD04 Integreer met bestaande CRM database schema\");\n        suggestions.push(\"\\uD83D\\uDCF1 Overweeg mobile app impact\");\n        suggestions.push(\"\\uD83E\\uDD16 Gebruik AI orchestrator voor intelligente features\");\n    }\n    if (projectType === \"bot-integration\") {\n        suggestions.push(\"\\uD83D\\uDCDE Test met Telegram webhook lokaal via ngrok\");\n        suggestions.push(\"\\uD83D\\uDCAC Implementeer fallback berichten\");\n        suggestions.push(\"\\uD83D\\uDD10 Beveilig webhook endpoints\");\n    }\n    if (complexity === \"high\") {\n        suggestions.push(\"\\uD83E\\uDDEA Implementeer uitgebreide testing\");\n        suggestions.push(\"\\uD83D\\uDCCA Monitor performance metrics\");\n        suggestions.push(\"\\uD83D\\uDD04 Plan voor iteratieve releases\");\n    }\n    suggestions.push(\"\\uD83D\\uDCBE Backup database voor productie changes\");\n    suggestions.push(\"\\uD83D\\uDCDD Update API documentatie\");\n    return suggestions;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/ai/project-planning.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fproject-planning&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fproject-planning.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();