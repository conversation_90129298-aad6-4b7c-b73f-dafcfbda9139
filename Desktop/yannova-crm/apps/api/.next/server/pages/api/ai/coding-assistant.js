"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/coding-assistant";
exports.ids = ["pages/api/ai/coding-assistant"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fcoding-assistant&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fcoding-assistant.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fcoding-assistant&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fcoding-assistant.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/ai/coding-assistant.ts */ \"(api)/./pages/api/ai/coding-assistant.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/coding-assistant\",\n        pathname: \"/api/ai/coding-assistant\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_ai_coding_assistant_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fcoding-assistant&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fcoding-assistant.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/ai-orchestrator.ts":
/*!********************************!*\
  !*** ./lib/ai-orchestrator.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIOrchestrator: () => (/* binding */ AIOrchestrator)\n/* harmony export */ });\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\n/* harmony import */ var _openai_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./openai-service */ \"(api)/./lib/openai-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__]);\n([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nclass AIOrchestrator {\n    constructor(){\n        this.models = new Map();\n        this.initializeModels();\n    }\n    initializeModels() {\n        // LM Studio Models\n        this.models.set(\"mistral-code\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"planning\"\n            ],\n            priority: 1,\n            maxTokens: 4096\n        });\n        this.models.set(\"deepseek-coder\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"debugging\",\n                \"refactoring\"\n            ],\n            priority: 2,\n            maxTokens: 4096\n        });\n        // API Models (fallback)\n        this.models.set(\"openai-gpt4\", {\n            name: \"gpt-4\",\n            type: \"api\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"email\",\n                \"chat\",\n                \"planning\"\n            ],\n            priority: 3,\n            maxTokens: 8192\n        });\n        this.models.set(\"openai-gpt3\", {\n            name: \"gpt-3.5-turbo\",\n            type: \"api\",\n            capabilities: [\n                \"chat\",\n                \"email\",\n                \"analysis\"\n            ],\n            priority: 4,\n            maxTokens: 4096\n        });\n    }\n    // Intelligente model selectie\n    selectModel(task) {\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type)).sort((a, b)=>a.priority - b.priority);\n        // Speciale logica voor verschillende taken\n        switch(task.type){\n            case \"coding\":\n                // Prioriteer code-gespecialiseerde modellen\n                return availableModels.find((m)=>m.name.includes(\"coder\") || m.name.includes(\"code\")) || availableModels[0];\n            case \"analysis\":\n                // Gebruik lokale modellen voor analyse\n                return availableModels.find((m)=>m.type === \"local\") || availableModels[0];\n            case \"email\":\n                // Gebruik snelle modellen voor email\n                return availableModels.find((m)=>m.maxTokens >= 2048) || availableModels[0];\n            default:\n                return availableModels[0];\n        }\n    }\n    // Universele query functie\n    async query(prompt, task) {\n        const selectedModel = this.selectModel(task);\n        if (!selectedModel) {\n            throw new Error(`Geen geschikt model gevonden voor taak: ${task.type}`);\n        }\n        try {\n            let response;\n            let provider;\n            if (selectedModel.type === \"local\") {\n                // Gebruik LM Studio\n                response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, selectedModel.name);\n                provider = \"LM Studio\";\n            } else {\n                // Gebruik OpenAI als fallback\n                response = await this.queryOpenAI(prompt, selectedModel, task);\n                provider = \"OpenAI\";\n            }\n            return {\n                response,\n                model: selectedModel.name,\n                provider,\n                timestamp: new Date().toISOString()\n            };\n        } catch (error) {\n            console.error(`Model ${selectedModel.name} failed:`, error);\n            // Fallback naar volgende model\n            return this.fallbackQuery(prompt, task, selectedModel);\n        }\n    }\n    // Fallback systeem\n    async fallbackQuery(prompt, task, failedModel) {\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type) && model.name !== failedModel.name).sort((a, b)=>a.priority - b.priority);\n        for (const model of availableModels){\n            try {\n                let response;\n                let provider;\n                if (model.type === \"local\") {\n                    response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, model.name);\n                    provider = \"LM Studio (Fallback)\";\n                } else {\n                    response = await this.queryOpenAI(prompt, model, task);\n                    provider = \"OpenAI (Fallback)\";\n                }\n                return {\n                    response,\n                    model: model.name,\n                    provider,\n                    timestamp: new Date().toISOString()\n                };\n            } catch (error) {\n                console.error(`Fallback model ${model.name} failed:`, error);\n                continue;\n            }\n        }\n        throw new Error(\"Alle modellen zijn niet beschikbaar\");\n    }\n    // OpenAI query helper\n    async queryOpenAI(prompt, model, task) {\n        switch(task.type){\n            case \"email\":\n                return await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.generateEmailReply(prompt, task.context);\n            case \"analysis\":\n                const sentiment = await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.analyzeSentiment(prompt);\n                return `Sentiment: ${sentiment}`;\n            default:\n                // Voor andere taken, gebruik generieke OpenAI call\n                // Dit zou je kunnen uitbreiden met een generieke OpenAI service\n                throw new Error(`OpenAI fallback niet geïmplementeerd voor taak: ${task.type}`);\n        }\n    }\n    // Speciale methoden voor verschillende taken\n    async codingAssistant(codePrompt, language = \"typescript\") {\n        const enhancedPrompt = `\nJe bent een expert ${language} developer. Analyseer deze code en geef concrete, bruikbare feedback:\n\n${codePrompt}\n\nGeef antwoord in het Nederlands met:\n1. Code review\n2. Verbeteringsvoorstellen\n3. Potentiële bugs\n4. Best practices\n`;\n        return this.query(enhancedPrompt, {\n            type: \"coding\",\n            priority: \"high\",\n            language\n        });\n    }\n    async crmAnalysis(data, analysisType) {\n        const prompt = `\nAnalyseer deze CRM data voor ${analysisType}:\n\n${data}\n\nGeef een Nederlandse analyse met:\n1. Belangrijkste inzichten\n2. Aanbevelingen\n3. Actie items\n`;\n        return this.query(prompt, {\n            type: \"analysis\",\n            priority: \"medium\",\n            context: analysisType\n        });\n    }\n    async planningAssistant(projectDetails) {\n        const prompt = `\nMaak een ontwikkelingsplan voor dit project:\n\n${projectDetails}\n\nGeef een gestructureerd plan met:\n1. Technische architectuur\n2. Implementatie stappen\n3. Tijdsinschattingen\n4. Risico's en mitigaties\n`;\n        return this.query(prompt, {\n            type: \"planning\",\n            priority: \"high\"\n        });\n    }\n    // Model status checker\n    async checkModelStatus() {\n        const status = {};\n        const modelKeys = Array.from(this.models.keys());\n        for (const key of modelKeys){\n            const model = this.models.get(key);\n            try {\n                if (model.type === \"local\") {\n                    await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(\"test\", model.name);\n                    status[key] = true;\n                } else {\n                    // Voor API modellen, check of de key beschikbaar is\n                    status[key] = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== \"dummy-key-for-lm-studio\";\n                }\n            } catch (error) {\n                status[key] = false;\n            }\n        }\n        return status;\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/ai-orchestrator.ts\n");

/***/ }),

/***/ "(api)/./lib/lmstudio-service.ts":
/*!*********************************!*\
  !*** ./lib/lmstudio-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryLMStudio: () => (/* binding */ queryLMStudio)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function queryLMStudio(prompt, model = \"mistral-7b-instruct-v0.1\") {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"http://127.0.0.1:1234/v1/chat/completions\", {\n            model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        }, {\n            headers: {\n                \"Authorization\": \"Bearer lm-studio\"\n            },\n            timeout: 30000 // 30 seconden timeout\n        });\n        return response.data.choices[0]?.message?.content || \"Geen antwoord ontvangen\";\n    } catch (error) {\n        console.error(\"LM Studio fout:\", error.response?.data || error.message);\n        return \"Fout bij verwerken verzoek\";\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUVuQixlQUFlQyxjQUFjQyxNQUFjLEVBQUVDLFFBQVEsMEJBQTBCO0lBQ3BGLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1KLGtEQUFVLENBQUMsNkNBQTZDO1lBQzdFRztZQUNBRyxVQUFVO2dCQUFDO29CQUFFQyxNQUFNO29CQUFRQyxTQUFTTjtnQkFBTzthQUFFO1lBQzdDTyxhQUFhO1lBQ2JDLFlBQVk7UUFDZCxHQUFHO1lBQ0RDLFNBQVM7Z0JBQUUsaUJBQWlCO1lBQW1CO1lBQy9DQyxTQUFTLE1BQU8sc0JBQXNCO1FBQ3hDO1FBRUEsT0FBT1IsU0FBU1MsSUFBSSxDQUFDQyxPQUFPLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxXQUFXO0lBQ3ZELEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQSxNQUFNWixRQUFRLEVBQUVTLFFBQVFHLE1BQU1ELE9BQU87UUFDdEUsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cz80NzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeUxNU3R1ZGlvKHByb21wdDogc3RyaW5nLCBtb2RlbCA9ICdtaXN0cmFsLTdiLWluc3RydWN0LXYwLjEnKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjEyMzQvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogcHJvbXB0IH1dLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heF90b2tlbnM6IDUwMFxuICAgIH0sIHtcbiAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIGxtLXN0dWRpbycgfSxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwICAvLyAzMCBzZWNvbmRlbiB0aW1lb3V0XG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnR2VlbiBhbnR3b29yZCBvbnR2YW5nZW4nO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xNIFN0dWRpbyBmb3V0OicsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiAnRm91dCBiaWogdmVyd2Vya2VuIHZlcnpvZWsnO1xuICB9XG59ICJdLCJuYW1lcyI6WyJheGlvcyIsInF1ZXJ5TE1TdHVkaW8iLCJwcm9tcHQiLCJtb2RlbCIsInJlc3BvbnNlIiwicG9zdCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJoZWFkZXJzIiwidGltZW91dCIsImRhdGEiLCJjaG9pY2VzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/lmstudio-service.ts\n");

/***/ }),

/***/ "(api)/./lib/openai-service.ts":
/*!*******************************!*\
  !*** ./lib/openai-service.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAIService: () => (/* binding */ OpenAIService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__]);\n([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Helper functie om te bepalen of LM Studio gebruikt moet worden\nconst shouldUseLMStudio = ()=>process.env.USE_LM_STUDIO === \"true\";\nclass OpenAIService {\n    // Generate smart email reply\n    static async generateEmailReply(originalEmail, context) {\n        try {\n            const systemPrompt = `Je bent Leon van Yannova, een vriendelijke Nederlandse AI developer en bot specialist. \n            Je antwoordt professioneel maar toegankelijk in het Nederlands.\n            Specialisaties: Telegram/WhatsApp bots, trading bots, CRM-systemen, apps.\n            Houd antwoorden kort en actionable.`;\n            const userPrompt = `Schrijf een antwoord op deze email:\\n\\n${originalEmail}\\n\\n${context ? `Context: ${context}` : \"\"}`;\n            if (shouldUseLMStudio()) {\n                const fullPrompt = `${systemPrompt}\\n\\n${userPrompt}`;\n                return await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(fullPrompt);\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-4\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: systemPrompt\n                    },\n                    {\n                        role: \"user\",\n                        content: userPrompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 500\n            });\n            return completion.choices[0].message.content || \"Kon geen antwoord genereren.\";\n        } catch (error) {\n            console.error(\"AI error:\", error);\n            return \"Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.\";\n        }\n    }\n    // Analyze email sentiment\n    static async analyzeSentiment(text) {\n        try {\n            if (shouldUseLMStudio()) {\n                const prompt = `Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\\n\\nTekst: ${text}`;\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const sentiment = parseFloat(result);\n                return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: text\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const sentiment = parseFloat(completion.choices[0].message.content || \"0\");\n            return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n        } catch (error) {\n            console.error(\"Sentiment analysis error:\", error);\n            return 0;\n        }\n    }\n    // Categorize email\n    static async categorizeEmail(subject, content) {\n        try {\n            const prompt = `Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\\n\\nOnderwerp: ${subject}\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const category = result.trim().toUpperCase();\n                return [\n                    \"QUOTE\",\n                    \"BOT\",\n                    \"URGENT\",\n                    \"GENERAL\"\n                ].includes(category) ? category : \"GENERAL\";\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Categoriseer deze email in \\xe9\\xe9n van deze categorie\\xebn: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: `Onderwerp: ${subject}\\n\\nInhoud: ${content}`\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const category = completion.choices[0].message.content?.trim().toUpperCase();\n            return [\n                \"QUOTE\",\n                \"BOT\",\n                \"URGENT\",\n                \"GENERAL\"\n            ].includes(category || \"\") ? category : \"GENERAL\";\n        } catch (error) {\n            console.error(\"Categorization error:\", error);\n            return \"GENERAL\";\n        }\n    }\n    // Generate email subject suggestions\n    static async generateSubjectSuggestions(content) {\n        try {\n            const prompt = `Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                return result.split(\"\\n\").filter((s)=>s.trim()) || [];\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: content\n                    }\n                ],\n                temperature: 0.8,\n                max_tokens: 100\n            });\n            return completion.choices[0].message.content?.split(\"\\n\").filter((s)=>s.trim()) || [];\n        } catch (error) {\n            console.error(\"Subject generation error:\", error);\n            return [\n                \"Uw aanvraag\",\n                \"Informatie verzoek\",\n                \"Contact\"\n            ];\n        }\n    }\n    // Extract key information from email\n    static async extractKeyInfo(emailContent) {\n        try {\n            const prompt = `Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\\n\\nEmail: ${emailContent}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                try {\n                    return JSON.parse(result);\n                } catch  {\n                    return {};\n                }\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: emailContent\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 200\n            });\n            return JSON.parse(completion.choices[0].message.content || \"{}\");\n        } catch (error) {\n            console.error(\"Info extraction error:\", error);\n            return {};\n        }\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvb3BlbmFpLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRCO0FBQ3VCO0FBRW5ELE1BQU1FLFNBQVMsSUFBSUYsOENBQU1BLENBQUM7SUFDeEJHLFFBQVFDLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYztBQUNwQztBQUVBLGlFQUFpRTtBQUNqRSxNQUFNQyxvQkFBb0IsSUFBTUgsUUFBUUMsR0FBRyxDQUFDRyxhQUFhLEtBQUs7QUFFdkQsTUFBTUM7SUFDWCw2QkFBNkI7SUFDN0IsYUFBYUMsbUJBQ1hDLGFBQXFCLEVBQ3JCQyxPQUFnQixFQUNDO1FBQ2pCLElBQUk7WUFDRixNQUFNQyxlQUFlLENBQUM7OzsrQ0FHbUIsQ0FBQztZQUUxQyxNQUFNQyxhQUFhLENBQUMsdUNBQXVDLEVBQUVILGNBQWMsSUFBSSxFQUFFQyxVQUFVLENBQUMsU0FBUyxFQUFFQSxRQUFRLENBQUMsR0FBRyxHQUFHLENBQUM7WUFFdkgsSUFBSUwscUJBQXFCO2dCQUN2QixNQUFNUSxhQUFhLENBQUMsRUFBRUYsYUFBYSxJQUFJLEVBQUVDLFdBQVcsQ0FBQztnQkFDckQsT0FBTyxNQUFNYixnRUFBYUEsQ0FBQ2M7WUFDN0I7WUFFQSxNQUFNQyxhQUFhLE1BQU1kLE9BQU9lLElBQUksQ0FBQ0MsV0FBVyxDQUFDQyxNQUFNLENBQUM7Z0JBQ3REQyxPQUFPO2dCQUNQQyxVQUFVO29CQUNSO3dCQUNFQyxNQUFNO3dCQUNOQyxTQUFTVjtvQkFDWDtvQkFDQTt3QkFDRVMsTUFBTTt3QkFDTkMsU0FBU1Q7b0JBQ1g7aUJBQ0Q7Z0JBQ0RVLGFBQWE7Z0JBQ2JDLFlBQVk7WUFDZDtZQUVBLE9BQU9ULFdBQVdVLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ0osT0FBTyxJQUFJO1FBQ2xELEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsYUFBYUE7WUFDM0IsT0FBTztRQUNUO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsYUFBYUUsaUJBQWlCQyxJQUFZLEVBQW1CO1FBQzNELElBQUk7WUFDRixJQUFJeEIscUJBQXFCO2dCQUN2QixNQUFNeUIsU0FBUyxDQUFDLGdJQUFnSSxFQUFFRCxLQUFLLENBQUM7Z0JBQ3hKLE1BQU1FLFNBQVMsTUFBTWhDLGdFQUFhQSxDQUFDK0I7Z0JBQ25DLE1BQU1FLFlBQVlDLFdBQVdGO2dCQUM3QixPQUFPRyxNQUFNRixhQUFhLElBQUlHLEtBQUtDLEdBQUcsQ0FBQyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxHQUFHTDtZQUN6RDtZQUVBLE1BQU1sQixhQUFhLE1BQU1kLE9BQU9lLElBQUksQ0FBQ0MsV0FBVyxDQUFDQyxNQUFNLENBQUM7Z0JBQ3REQyxPQUFPO2dCQUNQQyxVQUFVO29CQUNSO3dCQUNFQyxNQUFNO3dCQUNOQyxTQUFTO29CQUNYO29CQUNBO3dCQUNFRCxNQUFNO3dCQUNOQyxTQUFTUTtvQkFDWDtpQkFDRDtnQkFDRFAsYUFBYTtnQkFDYkMsWUFBWTtZQUNkO1lBRUEsTUFBTVMsWUFBWUMsV0FBV25CLFdBQVdVLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ0osT0FBTyxJQUFJO1lBQ3RFLE9BQU9hLE1BQU1GLGFBQWEsSUFBSUcsS0FBS0MsR0FBRyxDQUFDLENBQUMsR0FBR0QsS0FBS0UsR0FBRyxDQUFDLEdBQUdMO1FBQ3pELEVBQUUsT0FBT04sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixhQUFhWSxnQkFBZ0JDLE9BQWUsRUFBRWxCLE9BQWUsRUFBbUI7UUFDOUUsSUFBSTtZQUNGLE1BQU1TLFNBQVMsQ0FBQyxrSUFBa0ksRUFBRVMsUUFBUSxZQUFZLEVBQUVsQixRQUFRLENBQUM7WUFFbkwsSUFBSWhCLHFCQUFxQjtnQkFDdkIsTUFBTTBCLFNBQVMsTUFBTWhDLGdFQUFhQSxDQUFDK0I7Z0JBQ25DLE1BQU1VLFdBQVdULE9BQU9VLElBQUksR0FBR0MsV0FBVztnQkFDMUMsT0FBTztvQkFBQztvQkFBUztvQkFBTztvQkFBVTtpQkFBVSxDQUFDQyxRQUFRLENBQUNILFlBQVlBLFdBQVc7WUFDL0U7WUFFQSxNQUFNMUIsYUFBYSxNQUFNZCxPQUFPZSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO2dCQUN0REMsT0FBTztnQkFDUEMsVUFBVTtvQkFDUjt3QkFDRUMsTUFBTTt3QkFDTkMsU0FBUztvQkFDWDtvQkFDQTt3QkFDRUQsTUFBTTt3QkFDTkMsU0FBUyxDQUFDLFdBQVcsRUFBRWtCLFFBQVEsWUFBWSxFQUFFbEIsUUFBUSxDQUFDO29CQUN4RDtpQkFDRDtnQkFDREMsYUFBYTtnQkFDYkMsWUFBWTtZQUNkO1lBRUEsTUFBTWlCLFdBQVcxQixXQUFXVSxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLENBQUNKLE9BQU8sRUFBRW9CLE9BQU9DO1lBQy9ELE9BQU87Z0JBQUM7Z0JBQVM7Z0JBQU87Z0JBQVU7YUFBVSxDQUFDQyxRQUFRLENBQUNILFlBQVksTUFBTUEsV0FBWTtRQUN0RixFQUFFLE9BQU9kLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsT0FBTztRQUNUO0lBQ0Y7SUFFQSxxQ0FBcUM7SUFDckMsYUFBYWtCLDJCQUEyQnZCLE9BQWUsRUFBcUI7UUFDMUUsSUFBSTtZQUNGLE1BQU1TLFNBQVMsQ0FBQyx3S0FBd0ssRUFBRVQsUUFBUSxDQUFDO1lBRW5NLElBQUloQixxQkFBcUI7Z0JBQ3ZCLE1BQU0wQixTQUFTLE1BQU1oQyxnRUFBYUEsQ0FBQytCO2dCQUNuQyxPQUFPQyxPQUFPYyxLQUFLLENBQUMsTUFBTUMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFTixJQUFJLE9BQU8sRUFBRTtZQUN2RDtZQUVBLE1BQU0zQixhQUFhLE1BQU1kLE9BQU9lLElBQUksQ0FBQ0MsV0FBVyxDQUFDQyxNQUFNLENBQUM7Z0JBQ3REQyxPQUFPO2dCQUNQQyxVQUFVO29CQUNSO3dCQUNFQyxNQUFNO3dCQUNOQyxTQUFTO29CQUNYO29CQUNBO3dCQUNFRCxNQUFNO3dCQUNOQyxTQUFTQTtvQkFDWDtpQkFDRDtnQkFDREMsYUFBYTtnQkFDYkMsWUFBWTtZQUNkO1lBRUEsT0FBT1QsV0FBV1UsT0FBTyxDQUFDLEVBQUUsQ0FBQ0MsT0FBTyxDQUFDSixPQUFPLEVBQUV3QixNQUFNLE1BQU1DLE9BQU9DLENBQUFBLElBQUtBLEVBQUVOLElBQUksT0FBTyxFQUFFO1FBQ3ZGLEVBQUUsT0FBT2YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPO2dCQUFDO2dCQUFlO2dCQUFzQjthQUFVO1FBQ3pEO0lBQ0Y7SUFFQSxxQ0FBcUM7SUFDckMsYUFBYXNCLGVBQWVDLFlBQW9CLEVBTTdDO1FBQ0QsSUFBSTtZQUNGLE1BQU1uQixTQUFTLENBQUMsMEpBQTBKLEVBQUVtQixhQUFhLENBQUM7WUFFMUwsSUFBSTVDLHFCQUFxQjtnQkFDdkIsTUFBTTBCLFNBQVMsTUFBTWhDLGdFQUFhQSxDQUFDK0I7Z0JBQ25DLElBQUk7b0JBQ0YsT0FBT29CLEtBQUtDLEtBQUssQ0FBQ3BCO2dCQUNwQixFQUFFLE9BQU07b0JBQ04sT0FBTyxDQUFDO2dCQUNWO1lBQ0Y7WUFFQSxNQUFNakIsYUFBYSxNQUFNZCxPQUFPZSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO2dCQUN0REMsT0FBTztnQkFDUEMsVUFBVTtvQkFDUjt3QkFDRUMsTUFBTTt3QkFDTkMsU0FBUztvQkFDWDtvQkFDQTt3QkFDRUQsTUFBTTt3QkFDTkMsU0FBUzRCO29CQUNYO2lCQUNEO2dCQUNEM0IsYUFBYTtnQkFDYkMsWUFBWTtZQUNkO1lBRUEsT0FBTzJCLEtBQUtDLEtBQUssQ0FBQ3JDLFdBQVdVLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ0osT0FBTyxJQUFJO1FBQzdELEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4QyxPQUFPLENBQUM7UUFDVjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvb3BlbmFpLXNlcnZpY2UudHM/NjI0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgT3BlbkFJIGZyb20gJ29wZW5haSc7XG5pbXBvcnQgeyBxdWVyeUxNU3R1ZGlvIH0gZnJvbSAnLi9sbXN0dWRpby1zZXJ2aWNlJztcblxuY29uc3Qgb3BlbmFpID0gbmV3IE9wZW5BSSh7XG4gIGFwaUtleTogcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVksXG59KTtcblxuLy8gSGVscGVyIGZ1bmN0aWUgb20gdGUgYmVwYWxlbiBvZiBMTSBTdHVkaW8gZ2VicnVpa3QgbW9ldCB3b3JkZW5cbmNvbnN0IHNob3VsZFVzZUxNU3R1ZGlvID0gKCkgPT4gcHJvY2Vzcy5lbnYuVVNFX0xNX1NUVURJTyA9PT0gJ3RydWUnO1xuXG5leHBvcnQgY2xhc3MgT3BlbkFJU2VydmljZSB7XG4gIC8vIEdlbmVyYXRlIHNtYXJ0IGVtYWlsIHJlcGx5XG4gIHN0YXRpYyBhc3luYyBnZW5lcmF0ZUVtYWlsUmVwbHkoXG4gICAgb3JpZ2luYWxFbWFpbDogc3RyaW5nLFxuICAgIGNvbnRleHQ/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3lzdGVtUHJvbXB0ID0gYEplIGJlbnQgTGVvbiB2YW4gWWFubm92YSwgZWVuIHZyaWVuZGVsaWprZSBOZWRlcmxhbmRzZSBBSSBkZXZlbG9wZXIgZW4gYm90IHNwZWNpYWxpc3QuIFxuICAgICAgICAgICAgSmUgYW50d29vcmR0IHByb2Zlc3Npb25lZWwgbWFhciB0b2VnYW5rZWxpamsgaW4gaGV0IE5lZGVybGFuZHMuXG4gICAgICAgICAgICBTcGVjaWFsaXNhdGllczogVGVsZWdyYW0vV2hhdHNBcHAgYm90cywgdHJhZGluZyBib3RzLCBDUk0tc3lzdGVtZW4sIGFwcHMuXG4gICAgICAgICAgICBIb3VkIGFudHdvb3JkZW4ga29ydCBlbiBhY3Rpb25hYmxlLmA7XG4gICAgICBcbiAgICAgIGNvbnN0IHVzZXJQcm9tcHQgPSBgU2NocmlqZiBlZW4gYW50d29vcmQgb3AgZGV6ZSBlbWFpbDpcXG5cXG4ke29yaWdpbmFsRW1haWx9XFxuXFxuJHtjb250ZXh0ID8gYENvbnRleHQ6ICR7Y29udGV4dH1gIDogJyd9YDtcblxuICAgICAgaWYgKHNob3VsZFVzZUxNU3R1ZGlvKCkpIHtcbiAgICAgICAgY29uc3QgZnVsbFByb21wdCA9IGAke3N5c3RlbVByb21wdH1cXG5cXG4ke3VzZXJQcm9tcHR9YDtcbiAgICAgICAgcmV0dXJuIGF3YWl0IHF1ZXJ5TE1TdHVkaW8oZnVsbFByb21wdCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNvbXBsZXRpb24gPSBhd2FpdCBvcGVuYWkuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoe1xuICAgICAgICBtb2RlbDogXCJncHQtNFwiLFxuICAgICAgICBtZXNzYWdlczogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIHJvbGU6IFwic3lzdGVtXCIsXG4gICAgICAgICAgICBjb250ZW50OiBzeXN0ZW1Qcm9tcHRcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHJvbGU6IFwidXNlclwiLFxuICAgICAgICAgICAgY29udGVudDogdXNlclByb21wdFxuICAgICAgICAgIH1cbiAgICAgICAgXSxcbiAgICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgICAgbWF4X3Rva2VuczogNTAwLFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiBjb21wbGV0aW9uLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50IHx8ICdLb24gZ2VlbiBhbnR3b29yZCBnZW5lcmVyZW4uJztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQUkgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuICdCZWRhbmt0IHZvb3IgamUgYmVyaWNodCEgSWsgbmVlbSB6byBzbmVsIG1vZ2VsaWprIGNvbnRhY3QgbWV0IGplIG9wLic7XG4gICAgfVxuICB9XG5cbiAgLy8gQW5hbHl6ZSBlbWFpbCBzZW50aW1lbnRcbiAgc3RhdGljIGFzeW5jIGFuYWx5emVTZW50aW1lbnQodGV4dDogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKHNob3VsZFVzZUxNU3R1ZGlvKCkpIHtcbiAgICAgICAgY29uc3QgcHJvbXB0ID0gYEFuYWx5c2VlciBoZXQgc2VudGltZW50IHZhbiBkZXplIHRla3N0LiBBbnR3b29yZCBhbGxlZW4gbWV0IGVlbiBnZXRhbCB0dXNzZW4gLTEgKHplZXIgbmVnYXRpZWYpIGVuIDEgKHplZXIgcG9zaXRpZWYpLlxcblxcblRla3N0OiAke3RleHR9YDtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnlMTVN0dWRpbyhwcm9tcHQpO1xuICAgICAgICBjb25zdCBzZW50aW1lbnQgPSBwYXJzZUZsb2F0KHJlc3VsdCk7XG4gICAgICAgIHJldHVybiBpc05hTihzZW50aW1lbnQpID8gMCA6IE1hdGgubWF4KC0xLCBNYXRoLm1pbigxLCBzZW50aW1lbnQpKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY29tcGxldGlvbiA9IGF3YWl0IG9wZW5haS5jaGF0LmNvbXBsZXRpb25zLmNyZWF0ZSh7XG4gICAgICAgIG1vZGVsOiBcImdwdC0zLjUtdHVyYm9cIixcbiAgICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICByb2xlOiBcInN5c3RlbVwiLFxuICAgICAgICAgICAgY29udGVudDogXCJBbmFseXNlZXIgaGV0IHNlbnRpbWVudCB2YW4gZGV6ZSB0ZWtzdC4gQW50d29vcmQgYWxsZWVuIG1ldCBlZW4gZ2V0YWwgdHVzc2VuIC0xICh6ZWVyIG5lZ2F0aWVmKSBlbiAxICh6ZWVyIHBvc2l0aWVmKS5cIlxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgcm9sZTogXCJ1c2VyXCIsXG4gICAgICAgICAgICBjb250ZW50OiB0ZXh0XG4gICAgICAgICAgfVxuICAgICAgICBdLFxuICAgICAgICB0ZW1wZXJhdHVyZTogMCxcbiAgICAgICAgbWF4X3Rva2VuczogMTAsXG4gICAgICB9KTtcblxuICAgICAgY29uc3Qgc2VudGltZW50ID0gcGFyc2VGbG9hdChjb21wbGV0aW9uLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50IHx8ICcwJyk7XG4gICAgICByZXR1cm4gaXNOYU4oc2VudGltZW50KSA/IDAgOiBNYXRoLm1heCgtMSwgTWF0aC5taW4oMSwgc2VudGltZW50KSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1NlbnRpbWVudCBhbmFseXNpcyBlcnJvcjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG4gIH1cblxuICAvLyBDYXRlZ29yaXplIGVtYWlsXG4gIHN0YXRpYyBhc3luYyBjYXRlZ29yaXplRW1haWwoc3ViamVjdDogc3RyaW5nLCBjb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9tcHQgPSBgQ2F0ZWdvcmlzZWVyIGRlemUgZW1haWwgaW4gw6nDqW4gdmFuIGRlemUgY2F0ZWdvcmllw6tuOiBRVU9URSwgQk9ULCBVUkdFTlQsIEdFTkVSQUwuIEFudHdvb3JkIGFsbGVlbiBtZXQgZGUgY2F0ZWdvcmllLlxcblxcbk9uZGVyd2VycDogJHtzdWJqZWN0fVxcblxcbkluaG91ZDogJHtjb250ZW50fWA7XG4gICAgICBcbiAgICAgIGlmIChzaG91bGRVc2VMTVN0dWRpbygpKSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5TE1TdHVkaW8ocHJvbXB0KTtcbiAgICAgICAgY29uc3QgY2F0ZWdvcnkgPSByZXN1bHQudHJpbSgpLnRvVXBwZXJDYXNlKCk7XG4gICAgICAgIHJldHVybiBbJ1FVT1RFJywgJ0JPVCcsICdVUkdFTlQnLCAnR0VORVJBTCddLmluY2x1ZGVzKGNhdGVnb3J5KSA/IGNhdGVnb3J5IDogJ0dFTkVSQUwnO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjb21wbGV0aW9uID0gYXdhaXQgb3BlbmFpLmNoYXQuY29tcGxldGlvbnMuY3JlYXRlKHtcbiAgICAgICAgbW9kZWw6IFwiZ3B0LTMuNS10dXJib1wiLFxuICAgICAgICBtZXNzYWdlczogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIHJvbGU6IFwic3lzdGVtXCIsXG4gICAgICAgICAgICBjb250ZW50OiBcIkNhdGVnb3Jpc2VlciBkZXplIGVtYWlsIGluIMOpw6luIHZhbiBkZXplIGNhdGVnb3JpZcOrbjogUVVPVEUsIEJPVCwgVVJHRU5ULCBHRU5FUkFMLiBBbnR3b29yZCBhbGxlZW4gbWV0IGRlIGNhdGVnb3JpZS5cIlxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgcm9sZTogXCJ1c2VyXCIsXG4gICAgICAgICAgICBjb250ZW50OiBgT25kZXJ3ZXJwOiAke3N1YmplY3R9XFxuXFxuSW5ob3VkOiAke2NvbnRlbnR9YFxuICAgICAgICAgIH1cbiAgICAgICAgXSxcbiAgICAgICAgdGVtcGVyYXR1cmU6IDAsXG4gICAgICAgIG1heF90b2tlbnM6IDEwLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gY29tcGxldGlvbi5jaG9pY2VzWzBdLm1lc3NhZ2UuY29udGVudD8udHJpbSgpLnRvVXBwZXJDYXNlKCk7XG4gICAgICByZXR1cm4gWydRVU9URScsICdCT1QnLCAnVVJHRU5UJywgJ0dFTkVSQUwnXS5pbmNsdWRlcyhjYXRlZ29yeSB8fCAnJykgPyBjYXRlZ29yeSEgOiAnR0VORVJBTCc7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NhdGVnb3JpemF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiAnR0VORVJBTCc7XG4gICAgfVxuICB9XG5cbiAgLy8gR2VuZXJhdGUgZW1haWwgc3ViamVjdCBzdWdnZXN0aW9uc1xuICBzdGF0aWMgYXN5bmMgZ2VuZXJhdGVTdWJqZWN0U3VnZ2VzdGlvbnMoY29udGVudDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9tcHQgPSBgR2VuZXJlZXIgMyBrb3J0ZSwgcHJvZmVzc2lvbmVsZSBlbWFpbCBvbmRlcndlcnByZWdlbHMgaW4gaGV0IE5lZGVybGFuZHMgdm9vciBkZXplIGVtYWlsIGluaG91ZC4gR2VlZiBhbGxlZW4gZGUgMyBvbmRlcndlcnBlbiwgZ2VzY2hlaWRlbiBkb29yIG5pZXV3ZSByZWdlbHMuXFxuXFxuSW5ob3VkOiAke2NvbnRlbnR9YDtcbiAgICAgIFxuICAgICAgaWYgKHNob3VsZFVzZUxNU3R1ZGlvKCkpIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnlMTVN0dWRpbyhwcm9tcHQpO1xuICAgICAgICByZXR1cm4gcmVzdWx0LnNwbGl0KCdcXG4nKS5maWx0ZXIocyA9PiBzLnRyaW0oKSkgfHwgW107XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNvbXBsZXRpb24gPSBhd2FpdCBvcGVuYWkuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoe1xuICAgICAgICBtb2RlbDogXCJncHQtMy41LXR1cmJvXCIsXG4gICAgICAgIG1lc3NhZ2VzOiBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgcm9sZTogXCJzeXN0ZW1cIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwiR2VuZXJlZXIgMyBrb3J0ZSwgcHJvZmVzc2lvbmVsZSBlbWFpbCBvbmRlcndlcnByZWdlbHMgaW4gaGV0IE5lZGVybGFuZHMgdm9vciBkZXplIGVtYWlsIGluaG91ZC4gR2VlZiBhbGxlZW4gZGUgMyBvbmRlcndlcnBlbiwgZ2VzY2hlaWRlbiBkb29yIG5pZXV3ZSByZWdlbHMuXCJcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHJvbGU6IFwidXNlclwiLFxuICAgICAgICAgICAgY29udGVudDogY29udGVudFxuICAgICAgICAgIH1cbiAgICAgICAgXSxcbiAgICAgICAgdGVtcGVyYXR1cmU6IDAuOCxcbiAgICAgICAgbWF4X3Rva2VuczogMTAwLFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiBjb21wbGV0aW9uLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50Py5zcGxpdCgnXFxuJykuZmlsdGVyKHMgPT4gcy50cmltKCkpIHx8IFtdO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTdWJqZWN0IGdlbmVyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFsnVXcgYWFudnJhYWcnLCAnSW5mb3JtYXRpZSB2ZXJ6b2VrJywgJ0NvbnRhY3QnXTtcbiAgICB9XG4gIH1cblxuICAvLyBFeHRyYWN0IGtleSBpbmZvcm1hdGlvbiBmcm9tIGVtYWlsXG4gIHN0YXRpYyBhc3luYyBleHRyYWN0S2V5SW5mbyhlbWFpbENvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8e1xuICAgIG5hbWU/OiBzdHJpbmc7XG4gICAgY29tcGFueT86IHN0cmluZztcbiAgICBwaG9uZT86IHN0cmluZztcbiAgICBpbnRlbnQ/OiBzdHJpbmc7XG4gICAgdXJnZW5jeT86ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XG4gIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcHJvbXB0ID0gYEV4dHJhY3Qga2V5IGluZm9ybWF0aW9uIGZyb20gdGhpcyBlbWFpbC4gUmV0dXJuIGEgSlNPTiBvYmplY3Qgd2l0aDogbmFtZSwgY29tcGFueSwgcGhvbmUsIGludGVudCAod2F0IHdpbCBkZSBrbGFudCksIHVyZ2VuY3kgKGxvdy9tZWRpdW0vaGlnaCkuXFxuXFxuRW1haWw6ICR7ZW1haWxDb250ZW50fWA7XG4gICAgICBcbiAgICAgIGlmIChzaG91bGRVc2VMTVN0dWRpbygpKSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5TE1TdHVkaW8ocHJvbXB0KTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShyZXN1bHQpO1xuICAgICAgICB9IGNhdGNoIHtcbiAgICAgICAgICByZXR1cm4ge307XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgY29tcGxldGlvbiA9IGF3YWl0IG9wZW5haS5jaGF0LmNvbXBsZXRpb25zLmNyZWF0ZSh7XG4gICAgICAgIG1vZGVsOiBcImdwdC0zLjUtdHVyYm9cIixcbiAgICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICByb2xlOiBcInN5c3RlbVwiLFxuICAgICAgICAgICAgY29udGVudDogXCJFeHRyYWN0IGtleSBpbmZvcm1hdGlvbiBmcm9tIHRoaXMgZW1haWwuIFJldHVybiBhIEpTT04gb2JqZWN0IHdpdGg6IG5hbWUsIGNvbXBhbnksIHBob25lLCBpbnRlbnQgKHdhdCB3aWwgZGUga2xhbnQpLCB1cmdlbmN5IChsb3cvbWVkaXVtL2hpZ2gpLlwiXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IGVtYWlsQ29udGVudFxuICAgICAgICAgIH1cbiAgICAgICAgXSxcbiAgICAgICAgdGVtcGVyYXR1cmU6IDAsXG4gICAgICAgIG1heF90b2tlbnM6IDIwMCxcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShjb21wbGV0aW9uLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50IHx8ICd7fScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdJbmZvIGV4dHJhY3Rpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgfVxufSAiXSwibmFtZXMiOlsiT3BlbkFJIiwicXVlcnlMTVN0dWRpbyIsIm9wZW5haSIsImFwaUtleSIsInByb2Nlc3MiLCJlbnYiLCJPUEVOQUlfQVBJX0tFWSIsInNob3VsZFVzZUxNU3R1ZGlvIiwiVVNFX0xNX1NUVURJTyIsIk9wZW5BSVNlcnZpY2UiLCJnZW5lcmF0ZUVtYWlsUmVwbHkiLCJvcmlnaW5hbEVtYWlsIiwiY29udGV4dCIsInN5c3RlbVByb21wdCIsInVzZXJQcm9tcHQiLCJmdWxsUHJvbXB0IiwiY29tcGxldGlvbiIsImNoYXQiLCJjb21wbGV0aW9ucyIsImNyZWF0ZSIsIm1vZGVsIiwibWVzc2FnZXMiLCJyb2xlIiwiY29udGVudCIsInRlbXBlcmF0dXJlIiwibWF4X3Rva2VucyIsImNob2ljZXMiLCJtZXNzYWdlIiwiZXJyb3IiLCJjb25zb2xlIiwiYW5hbHl6ZVNlbnRpbWVudCIsInRleHQiLCJwcm9tcHQiLCJyZXN1bHQiLCJzZW50aW1lbnQiLCJwYXJzZUZsb2F0IiwiaXNOYU4iLCJNYXRoIiwibWF4IiwibWluIiwiY2F0ZWdvcml6ZUVtYWlsIiwic3ViamVjdCIsImNhdGVnb3J5IiwidHJpbSIsInRvVXBwZXJDYXNlIiwiaW5jbHVkZXMiLCJnZW5lcmF0ZVN1YmplY3RTdWdnZXN0aW9ucyIsInNwbGl0IiwiZmlsdGVyIiwicyIsImV4dHJhY3RLZXlJbmZvIiwiZW1haWxDb250ZW50IiwiSlNPTiIsInBhcnNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./lib/openai-service.ts\n");

/***/ }),

/***/ "(api)/./pages/api/ai/coding-assistant.ts":
/*!******************************************!*\
  !*** ./pages/api/ai/coding-assistant.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/ai-orchestrator */ \"(api)/./lib/ai-orchestrator.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst orchestrator = new _lib_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__.AIOrchestrator();\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { code, language = \"typescript\", action = \"review\" } = req.body;\n        if (!code) {\n            return res.status(400).json({\n                error: \"Code is required\"\n            });\n        }\n        let result;\n        switch(action){\n            case \"review\":\n                result = await orchestrator.codingAssistant(`Review deze ${language} code en geef feedback:\\n\\n${code}`, language);\n                break;\n            case \"optimize\":\n                result = await orchestrator.codingAssistant(`Optimaliseer deze ${language} code voor performance en leesbaarheid:\\n\\n${code}`, language);\n                break;\n            case \"debug\":\n                result = await orchestrator.codingAssistant(`Debug deze ${language} code en vind potentiële bugs:\\n\\n${code}`, language);\n                break;\n            case \"explain\":\n                result = await orchestrator.codingAssistant(`Leg deze ${language} code uit in eenvoudige Nederlandse termen:\\n\\n${code}`, language);\n                break;\n            case \"refactor\":\n                result = await orchestrator.codingAssistant(`Refactor deze ${language} code volgens best practices:\\n\\n${code}`, language);\n                break;\n            default:\n                return res.status(400).json({\n                    error: \"Invalid action. Use: review, optimize, debug, explain, refactor\"\n                });\n        }\n        res.status(200).json({\n            ...result,\n            action,\n            language,\n            originalCode: code\n        });\n    } catch (error) {\n        console.error(\"Coding assistant error:\", error);\n        res.status(500).json({\n            error: \"Failed to process code\",\n            details: error.message\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/ai/coding-assistant.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fcoding-assistant&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fcoding-assistant.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();