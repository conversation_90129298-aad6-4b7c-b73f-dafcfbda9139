"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/automation/bot-enhance";
exports.ids = ["pages/api/automation/bot-enhance"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fbot-enhance&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fautomation%2Fbot-enhance.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fbot-enhance&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fautomation%2Fbot-enhance.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/automation/bot-enhance.ts */ \"(api)/./pages/api/automation/bot-enhance.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/automation/bot-enhance\",\n        pathname: \"/api/automation/bot-enhance\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_automation_bot_enhance_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fbot-enhance&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fautomation%2Fbot-enhance.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/ai-model-configs.ts":
/*!*********************************!*\
  !*** ./lib/ai-model-configs.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MODEL_CONFIGS: () => (/* binding */ MODEL_CONFIGS),\n/* harmony export */   TASK_PREFERENCES: () => (/* binding */ TASK_PREFERENCES)\n/* harmony export */ });\nconst MODEL_CONFIGS = {\n    // Lokale modellen (LM Studio)\n    \"mistral-code\": {\n        name: \"mistral-7b-instruct-v0.1\",\n        displayName: \"Mistral 7B (Code)\",\n        type: \"local\",\n        endpoint: \"http://127.0.0.1:1234/v1\",\n        capabilities: [\n            \"coding\",\n            \"analysis\",\n            \"planning\",\n            \"chat\"\n        ],\n        priority: 1,\n        maxTokens: 4096,\n        description: \"Snelle lokale AI voor coding en algemene taken\",\n        systemPrompt: \"Je bent een Nederlandse AI developer assistant. Antwoord altijd in het Nederlands.\"\n    },\n    \"deepseek-coder\": {\n        name: \"deepseek-coder-6.7b-instruct\",\n        displayName: \"DeepSeek Coder\",\n        type: \"local\",\n        endpoint: \"http://127.0.0.1:1234/v1\",\n        capabilities: [\n            \"coding\",\n            \"debugging\",\n            \"refactoring\"\n        ],\n        priority: 2,\n        maxTokens: 4096,\n        description: \"Gespecialiseerd model voor code review en debugging\",\n        systemPrompt: \"Je bent een expert code reviewer. Geef concrete feedback in het Nederlands.\"\n    },\n    \"llama-chat\": {\n        name: \"llama-2-7b-chat\",\n        displayName: \"Llama 2 Chat\",\n        type: \"local\",\n        endpoint: \"http://127.0.0.1:1234/v1\",\n        capabilities: [\n            \"chat\",\n            \"analysis\",\n            \"planning\"\n        ],\n        priority: 3,\n        maxTokens: 4096,\n        description: \"Conversationele AI voor algemene vragen en planning\"\n    },\n    // API modellen (fallback)\n    \"openai-gpt4\": {\n        name: \"gpt-4\",\n        displayName: \"GPT-4\",\n        type: \"api\",\n        capabilities: [\n            \"coding\",\n            \"analysis\",\n            \"email\",\n            \"chat\",\n            \"planning\"\n        ],\n        priority: 4,\n        maxTokens: 8192,\n        description: \"Krachtige API model voor complexe taken\",\n        systemPrompt: \"Je bent Leon van Yannova, Nederlandse AI developer. Antwoord professioneel in het Nederlands.\"\n    },\n    \"openai-gpt3\": {\n        name: \"gpt-3.5-turbo\",\n        displayName: \"GPT-3.5 Turbo\",\n        type: \"api\",\n        capabilities: [\n            \"chat\",\n            \"email\",\n            \"analysis\"\n        ],\n        priority: 5,\n        maxTokens: 4096,\n        description: \"Snelle API model voor eenvoudige taken\"\n    },\n    \"claude-3\": {\n        name: \"claude-3-sonnet\",\n        displayName: \"Claude 3 Sonnet\",\n        type: \"api\",\n        capabilities: [\n            \"coding\",\n            \"analysis\",\n            \"planning\",\n            \"chat\"\n        ],\n        priority: 6,\n        maxTokens: 8192,\n        description: \"Anthropic model voor analytische taken\"\n    }\n};\nconst TASK_PREFERENCES = {\n    \"coding\": [\n        \"mistral-code\",\n        \"deepseek-coder\",\n        \"openai-gpt4\"\n    ],\n    \"analysis\": [\n        \"mistral-code\",\n        \"llama-chat\",\n        \"openai-gpt4\"\n    ],\n    \"planning\": [\n        \"mistral-code\",\n        \"llama-chat\",\n        \"openai-gpt4\"\n    ],\n    \"chat\": [\n        \"llama-chat\",\n        \"mistral-code\",\n        \"openai-gpt3\"\n    ],\n    \"email\": [\n        \"mistral-code\",\n        \"openai-gpt4\",\n        \"openai-gpt3\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/ai-model-configs.ts\n");

/***/ }),

/***/ "(api)/./lib/ai-orchestrator.ts":
/*!********************************!*\
  !*** ./lib/ai-orchestrator.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIOrchestrator: () => (/* binding */ AIOrchestrator)\n/* harmony export */ });\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\n/* harmony import */ var _openai_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./openai-service */ \"(api)/./lib/openai-service.ts\");\n/* harmony import */ var _ai_model_configs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ai-model-configs */ \"(api)/./lib/ai-model-configs.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__]);\n([_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nclass AIOrchestrator {\n    constructor(){\n        this.models = new Map();\n        this.initializeModels();\n    }\n    initializeModels() {\n        // LM Studio Models\n        this.models.set(\"mistral-code\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"planning\"\n            ],\n            priority: 1,\n            maxTokens: 4096\n        });\n        this.models.set(\"deepseek-coder\", {\n            name: \"mistral-7b-instruct-v0.1\",\n            type: \"local\",\n            endpoint: \"http://127.0.0.1:1234/v1\",\n            capabilities: [\n                \"coding\",\n                \"debugging\",\n                \"refactoring\"\n            ],\n            priority: 2,\n            maxTokens: 4096\n        });\n        // API Models (fallback)\n        this.models.set(\"openai-gpt4\", {\n            name: \"gpt-4\",\n            type: \"api\",\n            capabilities: [\n                \"coding\",\n                \"analysis\",\n                \"email\",\n                \"chat\",\n                \"planning\"\n            ],\n            priority: 3,\n            maxTokens: 8192\n        });\n        this.models.set(\"openai-gpt3\", {\n            name: \"gpt-3.5-turbo\",\n            type: \"api\",\n            capabilities: [\n                \"chat\",\n                \"email\",\n                \"analysis\"\n            ],\n            priority: 4,\n            maxTokens: 4096\n        });\n    }\n    // Intelligente model selectie met configuratie\n    selectModel(task) {\n        // Gebruik task preferences voor intelligente routing\n        const preferredModelIds = _ai_model_configs__WEBPACK_IMPORTED_MODULE_2__.TASK_PREFERENCES[task.type] || Object.keys(this.models);\n        // Zoek het eerste beschikbare model uit de voorkeurlijst\n        for (const modelId of preferredModelIds){\n            const model = this.models.get(modelId);\n            if (model && model.capabilities.includes(task.type)) {\n                return model;\n            }\n        }\n        // Fallback: alle modellen die de taak kunnen uitvoeren\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type)).sort((a, b)=>a.priority - b.priority);\n        return availableModels[0] || null;\n    }\n    // Universele query functie\n    async query(prompt, task) {\n        const selectedModel = this.selectModel(task);\n        if (!selectedModel) {\n            throw new Error(`Geen geschikt model gevonden voor taak: ${task.type}`);\n        }\n        try {\n            let response;\n            let provider;\n            if (selectedModel.type === \"local\") {\n                // Gebruik LM Studio\n                response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, selectedModel.name);\n                provider = \"LM Studio\";\n            } else {\n                // Gebruik OpenAI als fallback\n                response = await this.queryOpenAI(prompt, selectedModel, task);\n                provider = \"OpenAI\";\n            }\n            return {\n                response,\n                model: selectedModel.name,\n                provider,\n                timestamp: new Date().toISOString()\n            };\n        } catch (error) {\n            console.error(`Model ${selectedModel.name} failed:`, error);\n            // Fallback naar volgende model\n            return this.fallbackQuery(prompt, task, selectedModel);\n        }\n    }\n    // Fallback systeem\n    async fallbackQuery(prompt, task, failedModel) {\n        const availableModels = Array.from(this.models.values()).filter((model)=>model.capabilities.includes(task.type) && model.name !== failedModel.name).sort((a, b)=>a.priority - b.priority);\n        for (const model of availableModels){\n            try {\n                let response;\n                let provider;\n                if (model.type === \"local\") {\n                    response = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(prompt, model.name);\n                    provider = \"LM Studio (Fallback)\";\n                } else {\n                    response = await this.queryOpenAI(prompt, model, task);\n                    provider = \"OpenAI (Fallback)\";\n                }\n                return {\n                    response,\n                    model: model.name,\n                    provider,\n                    timestamp: new Date().toISOString()\n                };\n            } catch (error) {\n                console.error(`Fallback model ${model.name} failed:`, error);\n                continue;\n            }\n        }\n        throw new Error(\"Alle modellen zijn niet beschikbaar\");\n    }\n    // OpenAI query helper\n    async queryOpenAI(prompt, model, task) {\n        switch(task.type){\n            case \"email\":\n                return await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.generateEmailReply(prompt, task.context);\n            case \"analysis\":\n                const sentiment = await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.analyzeSentiment(prompt);\n                return `Sentiment: ${sentiment}`;\n            case \"planning\":\n            case \"coding\":\n            case \"chat\":\n                // Voor deze taken gebruiken we een generieke fallback\n                return `OpenAI fallback niet beschikbaar voor ${task.type}. Gebruik LM Studio voor deze functionaliteit.`;\n            default:\n                throw new Error(`OpenAI fallback niet geïmplementeerd voor taak: ${task.type}`);\n        }\n    }\n    // Speciale methoden voor verschillende taken\n    async codingAssistant(codePrompt, language = \"typescript\") {\n        const enhancedPrompt = `\nJe bent een expert ${language} developer. Analyseer deze code en geef concrete, bruikbare feedback:\n\n${codePrompt}\n\nGeef antwoord in het Nederlands met:\n1. Code review\n2. Verbeteringsvoorstellen\n3. Potentiële bugs\n4. Best practices\n`;\n        return this.query(enhancedPrompt, {\n            type: \"coding\",\n            priority: \"high\",\n            language\n        });\n    }\n    async crmAnalysis(data, analysisType) {\n        const prompt = `\nAnalyseer deze CRM data voor ${analysisType}:\n\n${data}\n\nGeef een Nederlandse analyse met:\n1. Belangrijkste inzichten\n2. Aanbevelingen\n3. Actie items\n`;\n        return this.query(prompt, {\n            type: \"analysis\",\n            priority: \"medium\",\n            context: analysisType\n        });\n    }\n    async planningAssistant(projectDetails) {\n        const prompt = `\nMaak een ontwikkelingsplan voor dit project:\n\n${projectDetails}\n\nGeef een gestructureerd plan met:\n1. Technische architectuur\n2. Implementatie stappen\n3. Tijdsinschattingen\n4. Risico's en mitigaties\n`;\n        return this.query(prompt, {\n            type: \"planning\",\n            priority: \"high\"\n        });\n    }\n    // Model status checker\n    async checkModelStatus() {\n        const status = {};\n        const modelKeys = Array.from(this.models.keys());\n        for (const key of modelKeys){\n            const model = this.models.get(key);\n            try {\n                if (model.type === \"local\") {\n                    await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(\"test\", model.name);\n                    status[key] = true;\n                } else {\n                    // Voor API modellen, check of de key beschikbaar is\n                    status[key] = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== \"dummy-key-for-lm-studio\";\n                }\n            } catch (error) {\n                status[key] = false;\n            }\n        }\n        return status;\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvYWktb3JjaGVzdHJhdG9yLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBbUQ7QUFDRjtBQUNpQztBQWtCM0UsTUFBTUc7SUFHWEMsYUFBYzthQUZOQyxTQUErQixJQUFJQztRQUd6QyxJQUFJLENBQUNDLGdCQUFnQjtJQUN2QjtJQUVRQSxtQkFBbUI7UUFDekIsbUJBQW1CO1FBQ25CLElBQUksQ0FBQ0YsTUFBTSxDQUFDRyxHQUFHLENBQUMsZ0JBQWdCO1lBQzlCQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxjQUFjO2dCQUFDO2dCQUFVO2dCQUFZO2FBQVc7WUFDaERDLFVBQVU7WUFDVkMsV0FBVztRQUNiO1FBRUEsSUFBSSxDQUFDVCxNQUFNLENBQUNHLEdBQUcsQ0FBQyxrQkFBa0I7WUFDaENDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGNBQWM7Z0JBQUM7Z0JBQVU7Z0JBQWE7YUFBYztZQUNwREMsVUFBVTtZQUNWQyxXQUFXO1FBQ2I7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSSxDQUFDVCxNQUFNLENBQUNHLEdBQUcsQ0FBQyxlQUFlO1lBQzdCQyxNQUFNO1lBQ05DLE1BQU07WUFDTkUsY0FBYztnQkFBQztnQkFBVTtnQkFBWTtnQkFBUztnQkFBUTthQUFXO1lBQ2pFQyxVQUFVO1lBQ1ZDLFdBQVc7UUFDYjtRQUVBLElBQUksQ0FBQ1QsTUFBTSxDQUFDRyxHQUFHLENBQUMsZUFBZTtZQUM3QkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05FLGNBQWM7Z0JBQUM7Z0JBQVE7Z0JBQVM7YUFBVztZQUMzQ0MsVUFBVTtZQUNWQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLCtDQUErQztJQUMvQ0MsWUFBWUMsSUFBWSxFQUFrQjtRQUN4QyxxREFBcUQ7UUFDckQsTUFBTUMsb0JBQW9CZiwrREFBZ0IsQ0FBQ2MsS0FBS04sSUFBSSxDQUFDLElBQUlRLE9BQU9DLElBQUksQ0FBQyxJQUFJLENBQUNkLE1BQU07UUFFaEYseURBQXlEO1FBQ3pELEtBQUssTUFBTWUsV0FBV0gsa0JBQW1CO1lBQ3ZDLE1BQU1JLFFBQVEsSUFBSSxDQUFDaEIsTUFBTSxDQUFDaUIsR0FBRyxDQUFDRjtZQUM5QixJQUFJQyxTQUFTQSxNQUFNVCxZQUFZLENBQUNXLFFBQVEsQ0FBQ1AsS0FBS04sSUFBSSxHQUFHO2dCQUNuRCxPQUFPVztZQUNUO1FBQ0Y7UUFFQSx1REFBdUQ7UUFDdkQsTUFBTUcsa0JBQWtCQyxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDckIsTUFBTSxDQUFDc0IsTUFBTSxJQUNsREMsTUFBTSxDQUFDUCxDQUFBQSxRQUFTQSxNQUFNVCxZQUFZLENBQUNXLFFBQVEsQ0FBQ1AsS0FBS04sSUFBSSxHQUNyRG1CLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFakIsUUFBUSxHQUFHa0IsRUFBRWxCLFFBQVE7UUFFekMsT0FBT1csZUFBZSxDQUFDLEVBQUUsSUFBSTtJQUMvQjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNUSxNQUFNQyxNQUFjLEVBQUVqQixJQUFZLEVBS3JDO1FBQ0QsTUFBTWtCLGdCQUFnQixJQUFJLENBQUNuQixXQUFXLENBQUNDO1FBRXZDLElBQUksQ0FBQ2tCLGVBQWU7WUFDbEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsd0NBQXdDLEVBQUVuQixLQUFLTixJQUFJLENBQUMsQ0FBQztRQUN4RTtRQUVBLElBQUk7WUFDRixJQUFJMEI7WUFDSixJQUFJQztZQUVKLElBQUlILGNBQWN4QixJQUFJLEtBQUssU0FBUztnQkFDbEMsb0JBQW9CO2dCQUNwQjBCLFdBQVcsTUFBTXBDLGdFQUFhQSxDQUFDaUMsUUFBUUMsY0FBY3pCLElBQUk7Z0JBQ3pENEIsV0FBVztZQUNiLE9BQU87Z0JBQ0wsOEJBQThCO2dCQUM5QkQsV0FBVyxNQUFNLElBQUksQ0FBQ0UsV0FBVyxDQUFDTCxRQUFRQyxlQUFlbEI7Z0JBQ3pEcUIsV0FBVztZQUNiO1lBRUEsT0FBTztnQkFDTEQ7Z0JBQ0FmLE9BQU9hLGNBQWN6QixJQUFJO2dCQUN6QjRCO2dCQUNBRSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsTUFBTSxFQUFFUixjQUFjekIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFaUM7WUFFckQsK0JBQStCO1lBQy9CLE9BQU8sSUFBSSxDQUFDRSxhQUFhLENBQUNYLFFBQVFqQixNQUFNa0I7UUFDMUM7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixNQUFjVSxjQUFjWCxNQUFjLEVBQUVqQixJQUFZLEVBQUU2QixXQUFvQixFQUszRTtRQUNELE1BQU1yQixrQkFBa0JDLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUNyQixNQUFNLENBQUNzQixNQUFNLElBQ2xEQyxNQUFNLENBQUNQLENBQUFBLFFBQ05BLE1BQU1ULFlBQVksQ0FBQ1csUUFBUSxDQUFDUCxLQUFLTixJQUFJLEtBQ3JDVyxNQUFNWixJQUFJLEtBQUtvQyxZQUFZcEMsSUFBSSxFQUVoQ29CLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFakIsUUFBUSxHQUFHa0IsRUFBRWxCLFFBQVE7UUFFekMsS0FBSyxNQUFNUSxTQUFTRyxnQkFBaUI7WUFDbkMsSUFBSTtnQkFDRixJQUFJWTtnQkFDSixJQUFJQztnQkFFSixJQUFJaEIsTUFBTVgsSUFBSSxLQUFLLFNBQVM7b0JBQzFCMEIsV0FBVyxNQUFNcEMsZ0VBQWFBLENBQUNpQyxRQUFRWixNQUFNWixJQUFJO29CQUNqRDRCLFdBQVc7Z0JBQ2IsT0FBTztvQkFDTEQsV0FBVyxNQUFNLElBQUksQ0FBQ0UsV0FBVyxDQUFDTCxRQUFRWixPQUFPTDtvQkFDakRxQixXQUFXO2dCQUNiO2dCQUVBLE9BQU87b0JBQ0xEO29CQUNBZixPQUFPQSxNQUFNWixJQUFJO29CQUNqQjRCO29CQUNBRSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ25DO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxlQUFlLEVBQUVyQixNQUFNWixJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUVpQztnQkFDdEQ7WUFDRjtRQUNGO1FBRUEsTUFBTSxJQUFJUCxNQUFNO0lBQ2xCO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQWNHLFlBQVlMLE1BQWMsRUFBRVosS0FBYyxFQUFFTCxJQUFZLEVBQW1CO1FBQ3ZGLE9BQVFBLEtBQUtOLElBQUk7WUFDZixLQUFLO2dCQUNILE9BQU8sTUFBTVQsMERBQWFBLENBQUM2QyxrQkFBa0IsQ0FBQ2IsUUFBUWpCLEtBQUsrQixPQUFPO1lBQ3BFLEtBQUs7Z0JBQ0gsTUFBTUMsWUFBWSxNQUFNL0MsMERBQWFBLENBQUNnRCxnQkFBZ0IsQ0FBQ2hCO2dCQUN2RCxPQUFPLENBQUMsV0FBVyxFQUFFZSxVQUFVLENBQUM7WUFDbEMsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO2dCQUNILHNEQUFzRDtnQkFDdEQsT0FBTyxDQUFDLHNDQUFzQyxFQUFFaEMsS0FBS04sSUFBSSxDQUFDLDhDQUE4QyxDQUFDO1lBQzNHO2dCQUNFLE1BQU0sSUFBSXlCLE1BQU0sQ0FBQyxnREFBZ0QsRUFBRW5CLEtBQUtOLElBQUksQ0FBQyxDQUFDO1FBQ2xGO0lBQ0Y7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTXdDLGdCQUFnQkMsVUFBa0IsRUFBRUMsV0FBbUIsWUFBWSxFQUt0RTtRQUNELE1BQU1DLGlCQUFpQixDQUFDO21CQUNULEVBQUVELFNBQVM7O0FBRTlCLEVBQUVELFdBQVc7Ozs7Ozs7QUFPYixDQUFDO1FBRUcsT0FBTyxJQUFJLENBQUNuQixLQUFLLENBQUNxQixnQkFBZ0I7WUFDaEMzQyxNQUFNO1lBQ05HLFVBQVU7WUFDVnVDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLFlBQVlDLElBQVksRUFBRUMsWUFBb0IsRUFLakQ7UUFDRCxNQUFNdkIsU0FBUyxDQUFDOzZCQUNTLEVBQUV1QixhQUFhOztBQUU1QyxFQUFFRCxLQUFLOzs7Ozs7QUFNUCxDQUFDO1FBRUcsT0FBTyxJQUFJLENBQUN2QixLQUFLLENBQUNDLFFBQVE7WUFDeEJ2QixNQUFNO1lBQ05HLFVBQVU7WUFDVmtDLFNBQVNTO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLGtCQUFrQkMsY0FBc0IsRUFLM0M7UUFDRCxNQUFNekIsU0FBUyxDQUFDOzs7QUFHcEIsRUFBRXlCLGVBQWU7Ozs7Ozs7QUFPakIsQ0FBQztRQUVHLE9BQU8sSUFBSSxDQUFDMUIsS0FBSyxDQUFDQyxRQUFRO1lBQ3hCdkIsTUFBTTtZQUNORyxVQUFVO1FBQ1o7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNOEMsbUJBQXNEO1FBQzFELE1BQU1DLFNBQW1DLENBQUM7UUFFMUMsTUFBTUMsWUFBWXBDLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUNyQixNQUFNLENBQUNjLElBQUk7UUFDN0MsS0FBSyxNQUFNMkMsT0FBT0QsVUFBVztZQUMzQixNQUFNeEMsUUFBUSxJQUFJLENBQUNoQixNQUFNLENBQUNpQixHQUFHLENBQUN3QztZQUM5QixJQUFJO2dCQUNGLElBQUl6QyxNQUFNWCxJQUFJLEtBQUssU0FBUztvQkFDMUIsTUFBTVYsZ0VBQWFBLENBQUMsUUFBUXFCLE1BQU1aLElBQUk7b0JBQ3RDbUQsTUFBTSxDQUFDRSxJQUFJLEdBQUc7Z0JBQ2hCLE9BQU87b0JBQ0wsb0RBQW9EO29CQUNwREYsTUFBTSxDQUFDRSxJQUFJLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQyxHQUFHLENBQUNDLGNBQWMsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxjQUFjLEtBQUs7Z0JBQy9FO1lBQ0YsRUFBRSxPQUFPdkIsT0FBTztnQkFDZGtCLE1BQU0sQ0FBQ0UsSUFBSSxHQUFHO1lBQ2hCO1FBQ0Y7UUFFQSxPQUFPRjtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvYWktb3JjaGVzdHJhdG9yLnRzPzEyNjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcXVlcnlMTVN0dWRpbyB9IGZyb20gJy4vbG1zdHVkaW8tc2VydmljZSc7XG5pbXBvcnQgeyBPcGVuQUlTZXJ2aWNlIH0gZnJvbSAnLi9vcGVuYWktc2VydmljZSc7XG5pbXBvcnQgeyBNT0RFTF9DT05GSUdTLCBUQVNLX1BSRUZFUkVOQ0VTLCBNb2RlbENvbmZpZyB9IGZyb20gJy4vYWktbW9kZWwtY29uZmlncyc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQUlNb2RlbCB7XG4gIG5hbWU6IHN0cmluZztcbiAgdHlwZTogJ2xvY2FsJyB8ICdhcGknO1xuICBlbmRwb2ludD86IHN0cmluZztcbiAgY2FwYWJpbGl0aWVzOiBzdHJpbmdbXTtcbiAgcHJpb3JpdHk6IG51bWJlcjtcbiAgbWF4VG9rZW5zOiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQUlUYXNrIHtcbiAgdHlwZTogJ2NvZGluZycgfCAnYW5hbHlzaXMnIHwgJ2VtYWlsJyB8ICdjaGF0JyB8ICdwbGFubmluZyc7XG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xuICBjb250ZXh0Pzogc3RyaW5nO1xuICBsYW5ndWFnZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGNsYXNzIEFJT3JjaGVzdHJhdG9yIHtcbiAgcHJpdmF0ZSBtb2RlbHM6IE1hcDxzdHJpbmcsIEFJTW9kZWw+ID0gbmV3IE1hcCgpO1xuICBcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5pbml0aWFsaXplTW9kZWxzKCk7XG4gIH1cbiAgXG4gIHByaXZhdGUgaW5pdGlhbGl6ZU1vZGVscygpIHtcbiAgICAvLyBMTSBTdHVkaW8gTW9kZWxzXG4gICAgdGhpcy5tb2RlbHMuc2V0KCdtaXN0cmFsLWNvZGUnLCB7XG4gICAgICBuYW1lOiAnbWlzdHJhbC03Yi1pbnN0cnVjdC12MC4xJyxcbiAgICAgIHR5cGU6ICdsb2NhbCcsXG4gICAgICBlbmRwb2ludDogJ2h0dHA6Ly8xMjcuMC4wLjE6MTIzNC92MScsXG4gICAgICBjYXBhYmlsaXRpZXM6IFsnY29kaW5nJywgJ2FuYWx5c2lzJywgJ3BsYW5uaW5nJ10sXG4gICAgICBwcmlvcml0eTogMSxcbiAgICAgIG1heFRva2VuczogNDA5NlxuICAgIH0pO1xuICAgIFxuICAgIHRoaXMubW9kZWxzLnNldCgnZGVlcHNlZWstY29kZXInLCB7XG4gICAgICBuYW1lOiAnbWlzdHJhbC03Yi1pbnN0cnVjdC12MC4xJyxcbiAgICAgIHR5cGU6ICdsb2NhbCcsXG4gICAgICBlbmRwb2ludDogJ2h0dHA6Ly8xMjcuMC4wLjE6MTIzNC92MScsXG4gICAgICBjYXBhYmlsaXRpZXM6IFsnY29kaW5nJywgJ2RlYnVnZ2luZycsICdyZWZhY3RvcmluZyddLFxuICAgICAgcHJpb3JpdHk6IDIsXG4gICAgICBtYXhUb2tlbnM6IDQwOTZcbiAgICB9KTtcbiAgICBcbiAgICAvLyBBUEkgTW9kZWxzIChmYWxsYmFjaylcbiAgICB0aGlzLm1vZGVscy5zZXQoJ29wZW5haS1ncHQ0Jywge1xuICAgICAgbmFtZTogJ2dwdC00JyxcbiAgICAgIHR5cGU6ICdhcGknLFxuICAgICAgY2FwYWJpbGl0aWVzOiBbJ2NvZGluZycsICdhbmFseXNpcycsICdlbWFpbCcsICdjaGF0JywgJ3BsYW5uaW5nJ10sXG4gICAgICBwcmlvcml0eTogMyxcbiAgICAgIG1heFRva2VuczogODE5MlxuICAgIH0pO1xuICAgIFxuICAgIHRoaXMubW9kZWxzLnNldCgnb3BlbmFpLWdwdDMnLCB7XG4gICAgICBuYW1lOiAnZ3B0LTMuNS10dXJibycsXG4gICAgICB0eXBlOiAnYXBpJyxcbiAgICAgIGNhcGFiaWxpdGllczogWydjaGF0JywgJ2VtYWlsJywgJ2FuYWx5c2lzJ10sXG4gICAgICBwcmlvcml0eTogNCxcbiAgICAgIG1heFRva2VuczogNDA5NlxuICAgIH0pO1xuICB9XG4gIFxuICAvLyBJbnRlbGxpZ2VudGUgbW9kZWwgc2VsZWN0aWUgbWV0IGNvbmZpZ3VyYXRpZVxuICBzZWxlY3RNb2RlbCh0YXNrOiBBSVRhc2spOiBBSU1vZGVsIHwgbnVsbCB7XG4gICAgLy8gR2VicnVpayB0YXNrIHByZWZlcmVuY2VzIHZvb3IgaW50ZWxsaWdlbnRlIHJvdXRpbmdcbiAgICBjb25zdCBwcmVmZXJyZWRNb2RlbElkcyA9IFRBU0tfUFJFRkVSRU5DRVNbdGFzay50eXBlXSB8fCBPYmplY3Qua2V5cyh0aGlzLm1vZGVscyk7XG4gICAgXG4gICAgLy8gWm9layBoZXQgZWVyc3RlIGJlc2NoaWtiYXJlIG1vZGVsIHVpdCBkZSB2b29ya2V1cmxpanN0XG4gICAgZm9yIChjb25zdCBtb2RlbElkIG9mIHByZWZlcnJlZE1vZGVsSWRzKSB7XG4gICAgICBjb25zdCBtb2RlbCA9IHRoaXMubW9kZWxzLmdldChtb2RlbElkKTtcbiAgICAgIGlmIChtb2RlbCAmJiBtb2RlbC5jYXBhYmlsaXRpZXMuaW5jbHVkZXModGFzay50eXBlKSkge1xuICAgICAgICByZXR1cm4gbW9kZWw7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIEZhbGxiYWNrOiBhbGxlIG1vZGVsbGVuIGRpZSBkZSB0YWFrIGt1bm5lbiB1aXR2b2VyZW5cbiAgICBjb25zdCBhdmFpbGFibGVNb2RlbHMgPSBBcnJheS5mcm9tKHRoaXMubW9kZWxzLnZhbHVlcygpKVxuICAgICAgLmZpbHRlcihtb2RlbCA9PiBtb2RlbC5jYXBhYmlsaXRpZXMuaW5jbHVkZXModGFzay50eXBlKSlcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBhLnByaW9yaXR5IC0gYi5wcmlvcml0eSk7XG4gICAgXG4gICAgcmV0dXJuIGF2YWlsYWJsZU1vZGVsc1swXSB8fCBudWxsO1xuICB9XG4gIFxuICAvLyBVbml2ZXJzZWxlIHF1ZXJ5IGZ1bmN0aWVcbiAgYXN5bmMgcXVlcnkocHJvbXB0OiBzdHJpbmcsIHRhc2s6IEFJVGFzayk6IFByb21pc2U8e1xuICAgIHJlc3BvbnNlOiBzdHJpbmc7XG4gICAgbW9kZWw6IHN0cmluZztcbiAgICBwcm92aWRlcjogc3RyaW5nO1xuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xuICB9PiB7XG4gICAgY29uc3Qgc2VsZWN0ZWRNb2RlbCA9IHRoaXMuc2VsZWN0TW9kZWwodGFzayk7XG4gICAgXG4gICAgaWYgKCFzZWxlY3RlZE1vZGVsKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEdlZW4gZ2VzY2hpa3QgbW9kZWwgZ2V2b25kZW4gdm9vciB0YWFrOiAke3Rhc2sudHlwZX1gKTtcbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGxldCByZXNwb25zZTogc3RyaW5nO1xuICAgICAgbGV0IHByb3ZpZGVyOiBzdHJpbmc7XG4gICAgICBcbiAgICAgIGlmIChzZWxlY3RlZE1vZGVsLnR5cGUgPT09ICdsb2NhbCcpIHtcbiAgICAgICAgLy8gR2VicnVpayBMTSBTdHVkaW9cbiAgICAgICAgcmVzcG9uc2UgPSBhd2FpdCBxdWVyeUxNU3R1ZGlvKHByb21wdCwgc2VsZWN0ZWRNb2RlbC5uYW1lKTtcbiAgICAgICAgcHJvdmlkZXIgPSAnTE0gU3R1ZGlvJztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEdlYnJ1aWsgT3BlbkFJIGFscyBmYWxsYmFja1xuICAgICAgICByZXNwb25zZSA9IGF3YWl0IHRoaXMucXVlcnlPcGVuQUkocHJvbXB0LCBzZWxlY3RlZE1vZGVsLCB0YXNrKTtcbiAgICAgICAgcHJvdmlkZXIgPSAnT3BlbkFJJztcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcmVzcG9uc2UsXG4gICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLm5hbWUsXG4gICAgICAgIHByb3ZpZGVyLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgTW9kZWwgJHtzZWxlY3RlZE1vZGVsLm5hbWV9IGZhaWxlZDpgLCBlcnJvcik7XG4gICAgICBcbiAgICAgIC8vIEZhbGxiYWNrIG5hYXIgdm9sZ2VuZGUgbW9kZWxcbiAgICAgIHJldHVybiB0aGlzLmZhbGxiYWNrUXVlcnkocHJvbXB0LCB0YXNrLCBzZWxlY3RlZE1vZGVsKTtcbiAgICB9XG4gIH1cbiAgXG4gIC8vIEZhbGxiYWNrIHN5c3RlZW1cbiAgcHJpdmF0ZSBhc3luYyBmYWxsYmFja1F1ZXJ5KHByb21wdDogc3RyaW5nLCB0YXNrOiBBSVRhc2ssIGZhaWxlZE1vZGVsOiBBSU1vZGVsKTogUHJvbWlzZTx7XG4gICAgcmVzcG9uc2U6IHN0cmluZztcbiAgICBtb2RlbDogc3RyaW5nO1xuICAgIHByb3ZpZGVyOiBzdHJpbmc7XG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIH0+IHtcbiAgICBjb25zdCBhdmFpbGFibGVNb2RlbHMgPSBBcnJheS5mcm9tKHRoaXMubW9kZWxzLnZhbHVlcygpKVxuICAgICAgLmZpbHRlcihtb2RlbCA9PiBcbiAgICAgICAgbW9kZWwuY2FwYWJpbGl0aWVzLmluY2x1ZGVzKHRhc2sudHlwZSkgJiYgXG4gICAgICAgIG1vZGVsLm5hbWUgIT09IGZhaWxlZE1vZGVsLm5hbWVcbiAgICAgIClcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBhLnByaW9yaXR5IC0gYi5wcmlvcml0eSk7XG4gICAgXG4gICAgZm9yIChjb25zdCBtb2RlbCBvZiBhdmFpbGFibGVNb2RlbHMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGxldCByZXNwb25zZTogc3RyaW5nO1xuICAgICAgICBsZXQgcHJvdmlkZXI6IHN0cmluZztcbiAgICAgICAgXG4gICAgICAgIGlmIChtb2RlbC50eXBlID09PSAnbG9jYWwnKSB7XG4gICAgICAgICAgcmVzcG9uc2UgPSBhd2FpdCBxdWVyeUxNU3R1ZGlvKHByb21wdCwgbW9kZWwubmFtZSk7XG4gICAgICAgICAgcHJvdmlkZXIgPSAnTE0gU3R1ZGlvIChGYWxsYmFjayknO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlc3BvbnNlID0gYXdhaXQgdGhpcy5xdWVyeU9wZW5BSShwcm9tcHQsIG1vZGVsLCB0YXNrKTtcbiAgICAgICAgICBwcm92aWRlciA9ICdPcGVuQUkgKEZhbGxiYWNrKSc7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcmVzcG9uc2UsXG4gICAgICAgICAgbW9kZWw6IG1vZGVsLm5hbWUsXG4gICAgICAgICAgcHJvdmlkZXIsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhbGxiYWNrIG1vZGVsICR7bW9kZWwubmFtZX0gZmFpbGVkOmAsIGVycm9yKTtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHRocm93IG5ldyBFcnJvcignQWxsZSBtb2RlbGxlbiB6aWpuIG5pZXQgYmVzY2hpa2JhYXInKTtcbiAgfVxuICBcbiAgLy8gT3BlbkFJIHF1ZXJ5IGhlbHBlclxuICBwcml2YXRlIGFzeW5jIHF1ZXJ5T3BlbkFJKHByb21wdDogc3RyaW5nLCBtb2RlbDogQUlNb2RlbCwgdGFzazogQUlUYXNrKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICBzd2l0Y2ggKHRhc2sudHlwZSkge1xuICAgICAgY2FzZSAnZW1haWwnOlxuICAgICAgICByZXR1cm4gYXdhaXQgT3BlbkFJU2VydmljZS5nZW5lcmF0ZUVtYWlsUmVwbHkocHJvbXB0LCB0YXNrLmNvbnRleHQpO1xuICAgICAgY2FzZSAnYW5hbHlzaXMnOlxuICAgICAgICBjb25zdCBzZW50aW1lbnQgPSBhd2FpdCBPcGVuQUlTZXJ2aWNlLmFuYWx5emVTZW50aW1lbnQocHJvbXB0KTtcbiAgICAgICAgcmV0dXJuIGBTZW50aW1lbnQ6ICR7c2VudGltZW50fWA7XG4gICAgICBjYXNlICdwbGFubmluZyc6XG4gICAgICBjYXNlICdjb2RpbmcnOlxuICAgICAgY2FzZSAnY2hhdCc6XG4gICAgICAgIC8vIFZvb3IgZGV6ZSB0YWtlbiBnZWJydWlrZW4gd2UgZWVuIGdlbmVyaWVrZSBmYWxsYmFja1xuICAgICAgICByZXR1cm4gYE9wZW5BSSBmYWxsYmFjayBuaWV0IGJlc2NoaWtiYWFyIHZvb3IgJHt0YXNrLnR5cGV9LiBHZWJydWlrIExNIFN0dWRpbyB2b29yIGRlemUgZnVuY3Rpb25hbGl0ZWl0LmA7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYE9wZW5BSSBmYWxsYmFjayBuaWV0IGdlw69tcGxlbWVudGVlcmQgdm9vciB0YWFrOiAke3Rhc2sudHlwZX1gKTtcbiAgICB9XG4gIH1cbiAgXG4gIC8vIFNwZWNpYWxlIG1ldGhvZGVuIHZvb3IgdmVyc2NoaWxsZW5kZSB0YWtlblxuICBhc3luYyBjb2RpbmdBc3Npc3RhbnQoY29kZVByb21wdDogc3RyaW5nLCBsYW5ndWFnZTogc3RyaW5nID0gJ3R5cGVzY3JpcHQnKTogUHJvbWlzZTx7XG4gICAgcmVzcG9uc2U6IHN0cmluZztcbiAgICBtb2RlbDogc3RyaW5nO1xuICAgIHByb3ZpZGVyOiBzdHJpbmc7XG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIH0+IHtcbiAgICBjb25zdCBlbmhhbmNlZFByb21wdCA9IGBcbkplIGJlbnQgZWVuIGV4cGVydCAke2xhbmd1YWdlfSBkZXZlbG9wZXIuIEFuYWx5c2VlciBkZXplIGNvZGUgZW4gZ2VlZiBjb25jcmV0ZSwgYnJ1aWtiYXJlIGZlZWRiYWNrOlxuXG4ke2NvZGVQcm9tcHR9XG5cbkdlZWYgYW50d29vcmQgaW4gaGV0IE5lZGVybGFuZHMgbWV0OlxuMS4gQ29kZSByZXZpZXdcbjIuIFZlcmJldGVyaW5nc3Zvb3JzdGVsbGVuXG4zLiBQb3RlbnRpw6tsZSBidWdzXG40LiBCZXN0IHByYWN0aWNlc1xuYDtcbiAgICBcbiAgICByZXR1cm4gdGhpcy5xdWVyeShlbmhhbmNlZFByb21wdCwge1xuICAgICAgdHlwZTogJ2NvZGluZycsXG4gICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgbGFuZ3VhZ2VcbiAgICB9KTtcbiAgfVxuICBcbiAgYXN5bmMgY3JtQW5hbHlzaXMoZGF0YTogc3RyaW5nLCBhbmFseXNpc1R5cGU6IHN0cmluZyk6IFByb21pc2U8e1xuICAgIHJlc3BvbnNlOiBzdHJpbmc7XG4gICAgbW9kZWw6IHN0cmluZztcbiAgICBwcm92aWRlcjogc3RyaW5nO1xuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xuICB9PiB7XG4gICAgY29uc3QgcHJvbXB0ID0gYFxuQW5hbHlzZWVyIGRlemUgQ1JNIGRhdGEgdm9vciAke2FuYWx5c2lzVHlwZX06XG5cbiR7ZGF0YX1cblxuR2VlZiBlZW4gTmVkZXJsYW5kc2UgYW5hbHlzZSBtZXQ6XG4xLiBCZWxhbmdyaWprc3RlIGluemljaHRlblxuMi4gQWFuYmV2ZWxpbmdlblxuMy4gQWN0aWUgaXRlbXNcbmA7XG4gICAgXG4gICAgcmV0dXJuIHRoaXMucXVlcnkocHJvbXB0LCB7XG4gICAgICB0eXBlOiAnYW5hbHlzaXMnLFxuICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nLFxuICAgICAgY29udGV4dDogYW5hbHlzaXNUeXBlXG4gICAgfSk7XG4gIH1cbiAgXG4gIGFzeW5jIHBsYW5uaW5nQXNzaXN0YW50KHByb2plY3REZXRhaWxzOiBzdHJpbmcpOiBQcm9taXNlPHtcbiAgICByZXNwb25zZTogc3RyaW5nO1xuICAgIG1vZGVsOiBzdHJpbmc7XG4gICAgcHJvdmlkZXI6IHN0cmluZztcbiAgICB0aW1lc3RhbXA6IHN0cmluZztcbiAgfT4ge1xuICAgIGNvbnN0IHByb21wdCA9IGBcbk1hYWsgZWVuIG9udHdpa2tlbGluZ3NwbGFuIHZvb3IgZGl0IHByb2plY3Q6XG5cbiR7cHJvamVjdERldGFpbHN9XG5cbkdlZWYgZWVuIGdlc3RydWN0dXJlZXJkIHBsYW4gbWV0OlxuMS4gVGVjaG5pc2NoZSBhcmNoaXRlY3R1dXJcbjIuIEltcGxlbWVudGF0aWUgc3RhcHBlblxuMy4gVGlqZHNpbnNjaGF0dGluZ2VuXG40LiBSaXNpY28ncyBlbiBtaXRpZ2F0aWVzXG5gO1xuICAgIFxuICAgIHJldHVybiB0aGlzLnF1ZXJ5KHByb21wdCwge1xuICAgICAgdHlwZTogJ3BsYW5uaW5nJyxcbiAgICAgIHByaW9yaXR5OiAnaGlnaCdcbiAgICB9KTtcbiAgfVxuICBcbiAgLy8gTW9kZWwgc3RhdHVzIGNoZWNrZXJcbiAgYXN5bmMgY2hlY2tNb2RlbFN0YXR1cygpOiBQcm9taXNlPHtba2V5OiBzdHJpbmddOiBib29sZWFufT4ge1xuICAgIGNvbnN0IHN0YXR1czoge1trZXk6IHN0cmluZ106IGJvb2xlYW59ID0ge307XG4gICAgXG4gICAgY29uc3QgbW9kZWxLZXlzID0gQXJyYXkuZnJvbSh0aGlzLm1vZGVscy5rZXlzKCkpO1xuICAgIGZvciAoY29uc3Qga2V5IG9mIG1vZGVsS2V5cykge1xuICAgICAgY29uc3QgbW9kZWwgPSB0aGlzLm1vZGVscy5nZXQoa2V5KSE7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAobW9kZWwudHlwZSA9PT0gJ2xvY2FsJykge1xuICAgICAgICAgIGF3YWl0IHF1ZXJ5TE1TdHVkaW8oJ3Rlc3QnLCBtb2RlbC5uYW1lKTtcbiAgICAgICAgICBzdGF0dXNba2V5XSA9IHRydWU7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gVm9vciBBUEkgbW9kZWxsZW4sIGNoZWNrIG9mIGRlIGtleSBiZXNjaGlrYmFhciBpc1xuICAgICAgICAgIHN0YXR1c1trZXldID0gISFwcm9jZXNzLmVudi5PUEVOQUlfQVBJX0tFWSAmJiBwcm9jZXNzLmVudi5PUEVOQUlfQVBJX0tFWSAhPT0gJ2R1bW15LWtleS1mb3ItbG0tc3R1ZGlvJztcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgc3RhdHVzW2tleV0gPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHN0YXR1cztcbiAgfVxufSAiXSwibmFtZXMiOlsicXVlcnlMTVN0dWRpbyIsIk9wZW5BSVNlcnZpY2UiLCJUQVNLX1BSRUZFUkVOQ0VTIiwiQUlPcmNoZXN0cmF0b3IiLCJjb25zdHJ1Y3RvciIsIm1vZGVscyIsIk1hcCIsImluaXRpYWxpemVNb2RlbHMiLCJzZXQiLCJuYW1lIiwidHlwZSIsImVuZHBvaW50IiwiY2FwYWJpbGl0aWVzIiwicHJpb3JpdHkiLCJtYXhUb2tlbnMiLCJzZWxlY3RNb2RlbCIsInRhc2siLCJwcmVmZXJyZWRNb2RlbElkcyIsIk9iamVjdCIsImtleXMiLCJtb2RlbElkIiwibW9kZWwiLCJnZXQiLCJpbmNsdWRlcyIsImF2YWlsYWJsZU1vZGVscyIsIkFycmF5IiwiZnJvbSIsInZhbHVlcyIsImZpbHRlciIsInNvcnQiLCJhIiwiYiIsInF1ZXJ5IiwicHJvbXB0Iiwic2VsZWN0ZWRNb2RlbCIsIkVycm9yIiwicmVzcG9uc2UiLCJwcm92aWRlciIsInF1ZXJ5T3BlbkFJIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZXJyb3IiLCJjb25zb2xlIiwiZmFsbGJhY2tRdWVyeSIsImZhaWxlZE1vZGVsIiwiZ2VuZXJhdGVFbWFpbFJlcGx5IiwiY29udGV4dCIsInNlbnRpbWVudCIsImFuYWx5emVTZW50aW1lbnQiLCJjb2RpbmdBc3Npc3RhbnQiLCJjb2RlUHJvbXB0IiwibGFuZ3VhZ2UiLCJlbmhhbmNlZFByb21wdCIsImNybUFuYWx5c2lzIiwiZGF0YSIsImFuYWx5c2lzVHlwZSIsInBsYW5uaW5nQXNzaXN0YW50IiwicHJvamVjdERldGFpbHMiLCJjaGVja01vZGVsU3RhdHVzIiwic3RhdHVzIiwibW9kZWxLZXlzIiwia2V5IiwicHJvY2VzcyIsImVudiIsIk9QRU5BSV9BUElfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./lib/ai-orchestrator.ts\n");

/***/ }),

/***/ "(api)/./lib/auto-ai-workflows.ts":
/*!**********************************!*\
  !*** ./lib/auto-ai-workflows.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoAIWorkflows: () => (/* binding */ AutoAIWorkflows)\n/* harmony export */ });\n/* harmony import */ var _ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ai-orchestrator */ \"(api)/./lib/ai-orchestrator.ts\");\n/* harmony import */ var _openai_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./openai-service */ \"(api)/./lib/openai-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__]);\n([_ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__, _openai_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nclass AutoAIWorkflows {\n    constructor(){\n        this.orchestrator = new _ai_orchestrator__WEBPACK_IMPORTED_MODULE_0__.AIOrchestrator();\n    }\n    // 📧 Automatische email verwerking\n    async processIncomingEmail(emailData) {\n        console.log(`🤖 Auto-verwerking email van ${emailData.from}`);\n        try {\n            // 1. Automatische sentiment analyse\n            const sentiment = await this.orchestrator.crmAnalysis(`Email van: ${emailData.from}\\nOnderwerp: ${emailData.subject}\\nInhoud: ${emailData.content}`, \"sentiment-analysis\");\n            // 2. Automatische categorisatie\n            const category = await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.categorizeEmail(emailData.subject, emailData.content);\n            // 3. Automatische urgentie bepaling\n            const isUrgent = this.detectUrgency(emailData.subject, emailData.content);\n            // 4. Automatisch antwoord genereren (maar niet versturen)\n            const suggestedReply = await _openai_service__WEBPACK_IMPORTED_MODULE_1__.OpenAIService.generateEmailReply(emailData.content, `Klant: ${emailData.from}, Categorie: ${category}`);\n            // 5. Automatische acties\n            const actions = this.determineActions(category, isUrgent, sentiment.response);\n            return {\n                processed: true,\n                analysis: {\n                    sentiment: sentiment.response,\n                    category,\n                    urgency: isUrgent ? \"high\" : \"normal\",\n                    confidence: 0.85\n                },\n                suggestedReply,\n                actions,\n                aiProvider: sentiment.provider,\n                timestamp: new Date().toISOString()\n            };\n        } catch (error) {\n            console.error(\"Auto email processing failed:\", error);\n            return {\n                processed: false,\n                error: error.message,\n                fallbackActions: [\n                    \"manual_review_required\"\n                ]\n            };\n        }\n    }\n    // 🤖 Automatische bot response verbetering\n    async enhanceBotResponse(originalMessage, userContext) {\n        console.log(`🤖 Auto-verbetering bot response`);\n        const enhancedPrompt = `\nVerbeter dit bot antwoord om het persoonlijker en nuttiger te maken:\n\nOrigineel bericht: \"${originalMessage}\"\nGebruiker context: ${userContext}\n\nMaak het antwoord:\n1. Persoonlijker en vriendelijker\n2. Meer actionable\n3. Specifiek voor Yannova CRM services\n4. In het Nederlands\n`;\n        const result = await this.orchestrator.query(enhancedPrompt, {\n            type: \"chat\",\n            priority: \"high\",\n            context: \"bot-enhancement\"\n        });\n        return {\n            originalMessage,\n            enhancedMessage: result.response,\n            improvement: \"auto-enhanced\",\n            model: result.model,\n            timestamp: result.timestamp\n        };\n    }\n    // 📊 Automatische CRM data analyse (dagelijks)\n    async runDailyAnalysis(crmData) {\n        console.log(`🤖 Auto-dagelijkse CRM analyse`);\n        const dataString = JSON.stringify(crmData.slice(0, 10)); // Eerste 10 records\n        const analysis = await this.orchestrator.crmAnalysis(dataString, \"daily-performance\");\n        // Automatische insights genereren\n        const insights = this.generateInsights(crmData);\n        return {\n            type: \"daily-analysis\",\n            analysis: analysis.response,\n            insights,\n            recommendations: this.generateRecommendations(insights),\n            model: analysis.model,\n            timestamp: analysis.timestamp,\n            nextAnalysis: this.getNextAnalysisTime()\n        };\n    }\n    // 🔍 Automatische code review (bij commits)\n    async autoCodeReview(changedFiles) {\n        console.log(`🤖 Auto-code review voor ${changedFiles.length} bestanden`);\n        const reviews = [];\n        for (const file of changedFiles){\n            if (this.shouldReviewFile(file.path)) {\n                const review = await this.orchestrator.codingAssistant(file.content, this.getLanguageFromPath(file.path));\n                reviews.push({\n                    file: file.path,\n                    review: review.response,\n                    model: review.model,\n                    issues: this.extractIssues(review.response),\n                    suggestions: this.extractSuggestions(review.response)\n                });\n            }\n        }\n        return {\n            type: \"auto-code-review\",\n            totalFiles: changedFiles.length,\n            reviewedFiles: reviews.length,\n            reviews,\n            summary: this.generateReviewSummary(reviews),\n            timestamp: new Date().toISOString()\n        };\n    }\n    // 🎯 Automatische project planning updates\n    async updateProjectProgress(projectId, completedTasks) {\n        console.log(`🤖 Auto-update project ${projectId}`);\n        const progressAnalysis = await this.orchestrator.planningAssistant(`\nProject ID: ${projectId}\nVoltooide taken: ${completedTasks.join(\", \")}\n\nAnalyseer de voortgang en geef:\n1. Percentage voltooid\n2. Volgende prioriteiten\n3. Potentiële risico's\n4. Tijdsinschatting voor resterende werk\n`);\n        return {\n            projectId,\n            analysis: progressAnalysis.response,\n            autoUpdates: {\n                status: this.calculateProjectStatus(completedTasks),\n                nextMilestone: this.getNextMilestone(completedTasks),\n                riskLevel: this.assessRiskLevel(progressAnalysis.response)\n            },\n            model: progressAnalysis.model,\n            timestamp: progressAnalysis.timestamp\n        };\n    }\n    // Helper methods\n    detectUrgency(subject, content) {\n        const urgentKeywords = [\n            \"urgent\",\n            \"asap\",\n            \"spoedig\",\n            \"dringend\",\n            \"belangrijk\",\n            \"kritiek\"\n        ];\n        const text = (subject + \" \" + content).toLowerCase();\n        return urgentKeywords.some((keyword)=>text.includes(keyword));\n    }\n    determineActions(category, isUrgent, sentiment) {\n        const actions = [];\n        if (isUrgent) actions.push(\"priority_flag\");\n        if (category === \"QUOTE\") actions.push(\"auto_quote_generation\");\n        if (category === \"BOT\") actions.push(\"bot_enhancement\");\n        if (sentiment.includes(\"negatief\")) actions.push(\"escalate_to_human\");\n        actions.push(\"auto_categorize\", \"update_crm\");\n        return actions;\n    }\n    generateInsights(data) {\n        return [\n            `Totaal ${data.length} CRM records verwerkt`,\n            \"Meeste activiteit in email categorie\",\n            \"Positieve sentiment trend waargenomen\",\n            \"Bot response rate: 85% succesvol\"\n        ];\n    }\n    generateRecommendations(insights) {\n        return [\n            \"\\uD83C\\uDFAF Focus op bot optimalisatie deze week\",\n            \"\\uD83D\\uDCE7 Email templates updaten voor betere response\",\n            \"\\uD83D\\uDCCA Meer automatisering implementeren\"\n        ];\n    }\n    shouldReviewFile(path) {\n        const reviewableExtensions = [\n            \".ts\",\n            \".js\",\n            \".vue\",\n            \".tsx\",\n            \".jsx\"\n        ];\n        return reviewableExtensions.some((ext)=>path.endsWith(ext));\n    }\n    getLanguageFromPath(path) {\n        if (path.endsWith(\".vue\")) return \"vue\";\n        if (path.endsWith(\".ts\") || path.endsWith(\".tsx\")) return \"typescript\";\n        if (path.endsWith(\".js\") || path.endsWith(\".jsx\")) return \"javascript\";\n        return \"typescript\";\n    }\n    extractIssues(review) {\n        // Simpele extractie van issues uit review text\n        return review.split(\"\\n\").filter((line)=>line.includes(\"bug\") || line.includes(\"probleem\") || line.includes(\"fout\")).slice(0, 3);\n    }\n    extractSuggestions(review) {\n        return review.split(\"\\n\").filter((line)=>line.includes(\"suggestie\") || line.includes(\"verbetering\") || line.includes(\"overweeg\")).slice(0, 3);\n    }\n    generateReviewSummary(reviews) {\n        const totalIssues = reviews.reduce((sum, r)=>sum + r.issues.length, 0);\n        const totalSuggestions = reviews.reduce((sum, r)=>sum + r.suggestions.length, 0);\n        return `${reviews.length} bestanden gereviewed, ${totalIssues} issues gevonden, ${totalSuggestions} verbeteringen voorgesteld`;\n    }\n    calculateProjectStatus(completedTasks) {\n        if (completedTasks.length < 3) return \"starting\";\n        if (completedTasks.length < 8) return \"in-progress\";\n        return \"nearing-completion\";\n    }\n    getNextMilestone(completedTasks) {\n        const milestones = [\n            \"MVP\",\n            \"Beta\",\n            \"Production\",\n            \"Optimization\"\n        ];\n        return milestones[Math.min(Math.floor(completedTasks.length / 3), 3)];\n    }\n    assessRiskLevel(analysis) {\n        const riskKeywords = [\n            \"risico\",\n            \"probleem\",\n            \"vertraging\",\n            \"uitdaging\"\n        ];\n        const riskCount = riskKeywords.filter((keyword)=>analysis.toLowerCase().includes(keyword)).length;\n        if (riskCount >= 2) return \"high\";\n        if (riskCount === 1) return \"medium\";\n        return \"low\";\n    }\n    getNextAnalysisTime() {\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        tomorrow.setHours(9, 0, 0, 0); // 9:00 AM volgende dag\n        return tomorrow.toISOString();\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auto-ai-workflows.ts\n");

/***/ }),

/***/ "(api)/./lib/lmstudio-service.ts":
/*!*********************************!*\
  !*** ./lib/lmstudio-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryLMStudio: () => (/* binding */ queryLMStudio)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function queryLMStudio(prompt, model = \"mistral-7b-instruct-v0.1\") {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"http://127.0.0.1:1234/v1/chat/completions\", {\n            model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        }, {\n            headers: {\n                \"Authorization\": \"Bearer lm-studio\"\n            },\n            timeout: 30000 // 30 seconden timeout\n        });\n        return response.data.choices[0]?.message?.content || \"Geen antwoord ontvangen\";\n    } catch (error) {\n        console.error(\"LM Studio fout:\", error.response?.data || error.message);\n        return \"Fout bij verwerken verzoek\";\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUVuQixlQUFlQyxjQUFjQyxNQUFjLEVBQUVDLFFBQVEsMEJBQTBCO0lBQ3BGLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1KLGtEQUFVLENBQUMsNkNBQTZDO1lBQzdFRztZQUNBRyxVQUFVO2dCQUFDO29CQUFFQyxNQUFNO29CQUFRQyxTQUFTTjtnQkFBTzthQUFFO1lBQzdDTyxhQUFhO1lBQ2JDLFlBQVk7UUFDZCxHQUFHO1lBQ0RDLFNBQVM7Z0JBQUUsaUJBQWlCO1lBQW1CO1lBQy9DQyxTQUFTLE1BQU8sc0JBQXNCO1FBQ3hDO1FBRUEsT0FBT1IsU0FBU1MsSUFBSSxDQUFDQyxPQUFPLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxXQUFXO0lBQ3ZELEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQSxNQUFNWixRQUFRLEVBQUVTLFFBQVFHLE1BQU1ELE9BQU87UUFDdEUsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cz80NzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeUxNU3R1ZGlvKHByb21wdDogc3RyaW5nLCBtb2RlbCA9ICdtaXN0cmFsLTdiLWluc3RydWN0LXYwLjEnKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjEyMzQvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogcHJvbXB0IH1dLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heF90b2tlbnM6IDUwMFxuICAgIH0sIHtcbiAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIGxtLXN0dWRpbycgfSxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwICAvLyAzMCBzZWNvbmRlbiB0aW1lb3V0XG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnR2VlbiBhbnR3b29yZCBvbnR2YW5nZW4nO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xNIFN0dWRpbyBmb3V0OicsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiAnRm91dCBiaWogdmVyd2Vya2VuIHZlcnpvZWsnO1xuICB9XG59ICJdLCJuYW1lcyI6WyJheGlvcyIsInF1ZXJ5TE1TdHVkaW8iLCJwcm9tcHQiLCJtb2RlbCIsInJlc3BvbnNlIiwicG9zdCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJoZWFkZXJzIiwidGltZW91dCIsImRhdGEiLCJjaG9pY2VzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/lmstudio-service.ts\n");

/***/ }),

/***/ "(api)/./lib/openai-service.ts":
/*!*******************************!*\
  !*** ./lib/openai-service.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAIService: () => (/* binding */ OpenAIService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__]);\n([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Helper functie om te bepalen of LM Studio gebruikt moet worden\nconst shouldUseLMStudio = ()=>process.env.USE_LM_STUDIO === \"true\";\nclass OpenAIService {\n    // Generate smart email reply\n    static async generateEmailReply(originalEmail, context) {\n        try {\n            const systemPrompt = `Je bent Leon van Yannova, een vriendelijke Nederlandse AI developer en bot specialist. \n            Je antwoordt professioneel maar toegankelijk in het Nederlands.\n            Specialisaties: Telegram/WhatsApp bots, trading bots, CRM-systemen, apps.\n            Houd antwoorden kort en actionable.`;\n            const userPrompt = `Schrijf een antwoord op deze email:\\n\\n${originalEmail}\\n\\n${context ? `Context: ${context}` : \"\"}`;\n            if (shouldUseLMStudio()) {\n                const fullPrompt = `${systemPrompt}\\n\\n${userPrompt}`;\n                return await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(fullPrompt);\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-4\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: systemPrompt\n                    },\n                    {\n                        role: \"user\",\n                        content: userPrompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 500\n            });\n            return completion.choices[0].message.content || \"Kon geen antwoord genereren.\";\n        } catch (error) {\n            console.error(\"AI error:\", error);\n            return \"Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.\";\n        }\n    }\n    // Analyze email sentiment\n    static async analyzeSentiment(text) {\n        try {\n            if (shouldUseLMStudio()) {\n                const prompt = `Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\\n\\nTekst: ${text}`;\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const sentiment = parseFloat(result);\n                return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: text\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const sentiment = parseFloat(completion.choices[0].message.content || \"0\");\n            return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n        } catch (error) {\n            console.error(\"Sentiment analysis error:\", error);\n            return 0;\n        }\n    }\n    // Categorize email\n    static async categorizeEmail(subject, content) {\n        try {\n            const prompt = `Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\\n\\nOnderwerp: ${subject}\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const category = result.trim().toUpperCase();\n                return [\n                    \"QUOTE\",\n                    \"BOT\",\n                    \"URGENT\",\n                    \"GENERAL\"\n                ].includes(category) ? category : \"GENERAL\";\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Categoriseer deze email in \\xe9\\xe9n van deze categorie\\xebn: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: `Onderwerp: ${subject}\\n\\nInhoud: ${content}`\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const category = completion.choices[0].message.content?.trim().toUpperCase();\n            return [\n                \"QUOTE\",\n                \"BOT\",\n                \"URGENT\",\n                \"GENERAL\"\n            ].includes(category || \"\") ? category : \"GENERAL\";\n        } catch (error) {\n            console.error(\"Categorization error:\", error);\n            return \"GENERAL\";\n        }\n    }\n    // Generate email subject suggestions\n    static async generateSubjectSuggestions(content) {\n        try {\n            const prompt = `Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                return result.split(\"\\n\").filter((s)=>s.trim()) || [];\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: content\n                    }\n                ],\n                temperature: 0.8,\n                max_tokens: 100\n            });\n            return completion.choices[0].message.content?.split(\"\\n\").filter((s)=>s.trim()) || [];\n        } catch (error) {\n            console.error(\"Subject generation error:\", error);\n            return [\n                \"Uw aanvraag\",\n                \"Informatie verzoek\",\n                \"Contact\"\n            ];\n        }\n    }\n    // Extract key information from email\n    static async extractKeyInfo(emailContent) {\n        try {\n            const prompt = `Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\\n\\nEmail: ${emailContent}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                try {\n                    return JSON.parse(result);\n                } catch  {\n                    return {};\n                }\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: emailContent\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 200\n            });\n            return JSON.parse(completion.choices[0].message.content || \"{}\");\n        } catch (error) {\n            console.error(\"Info extraction error:\", error);\n            return {};\n        }\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/openai-service.ts\n");

/***/ }),

/***/ "(api)/./pages/api/automation/bot-enhance.ts":
/*!*********************************************!*\
  !*** ./pages/api/automation/bot-enhance.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auto_ai_workflows__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/auto-ai-workflows */ \"(api)/./lib/auto-ai-workflows.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auto_ai_workflows__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_auto_ai_workflows__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst autoAI = new _lib_auto_ai_workflows__WEBPACK_IMPORTED_MODULE_0__.AutoAIWorkflows();\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { originalMessage, userContext } = req.body;\n        if (!originalMessage) {\n            return res.status(400).json({\n                error: \"Original message is required\"\n            });\n        }\n        console.log(`🤖 Auto-verbetering bot response`);\n        // Automatische response verbetering\n        const result = await autoAI.enhanceBotResponse(originalMessage, userContext || \"Algemene gebruiker\");\n        console.log(`✨ Bot response verbeterd van ${originalMessage.length} naar ${result.enhancedMessage.length} karakters`);\n        res.status(200).json({\n            message: \"Bot response automatisch verbeterd\",\n            ...result\n        });\n    } catch (error) {\n        console.error(\"Bot enhancement error:\", error);\n        res.status(500).json({\n            error: \"Failed to enhance bot response\",\n            details: error.message\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/automation/bot-enhance.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fbot-enhance&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fautomation%2Fbot-enhance.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();