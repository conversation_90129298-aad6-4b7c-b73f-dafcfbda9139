"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/simple-agents";
exports.ids = ["pages/api/ai/simple-agents"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fsimple-agents&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fsimple-agents.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fsimple-agents&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fsimple-agents.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_ai_simple_agents_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/ai/simple-agents.ts */ \"(api)/./pages/api/ai/simple-agents.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_simple_agents_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_simple_agents_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/simple-agents\",\n        pathname: \"/api/ai/simple-agents\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_ai_simple_agents_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fsimple-agents&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fsimple-agents.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/ai/simple-agents.ts":
/*!***************************************!*\
  !*** ./pages/api/ai/simple-agents.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// Eenvoudige AI Agents API die direct werkt\n// Agent simulatie klassen\nclass YannovaAgent {\n    constructor(role, goal, backstory){\n        this.role = role;\n        this.goal = goal;\n        this.backstory = backstory;\n    }\n    executeTask(taskDescription) {\n        const now = new Date();\n        const timeStr = now.toLocaleString(\"nl-NL\");\n        const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(\"nl-NL\");\n        if (this.role.includes(\"Analyst\")) {\n            return this.generateAnalysis(taskDescription, timeStr, nextWeek);\n        } else if (this.role.includes(\"Developer\")) {\n            return this.generateCode(taskDescription, timeStr);\n        } else if (this.role.includes(\"Manager\")) {\n            return this.generatePlan(taskDescription, timeStr, nextWeek);\n        } else {\n            return this.generateAIStrategy(taskDescription, timeStr);\n        }\n    }\n    generateAnalysis(task, timeStr, nextWeek) {\n        return `# 📊 CRM Performance Analyse - ${timeStr}\n\n## Huidige Status Yannova CRM\n- **Actieve Gebruikers**: 147 (↑15% deze maand)\n- **Lead Conversie**: 23.4% (↑3.2%)\n- **Gemiddelde Response Tijd**: 1.8 seconden\n- **Database Queries**: 2,341 per dag\n\n## Geïdentificeerde Bottlenecks\n1. **Email Processing**: 45% van server load\n2. **Bot Response Time**: Gemiddeld 3.2 sec (te traag)\n3. **Database Sync**: Dagelijkse sync duurt 23 minuten\n4. **Mobile App**: 67% bounce rate op mobiel\n\n## AI Integratie Kansen\n- **Automatische Lead Scoring**: Potentieel +40% conversie\n- **Chatbot Verbetering**: LM Studio integratie kan response tijd halveren\n- **Predictive Analytics**: Voorspel klant churn met 89% accuracy\n- **Workflow Automation**: 60% minder handmatige taken\n\n## ROI Projectie\n- **Implementatie Kosten**: €12,000\n- **Jaarlijkse Besparingen**: €45,000\n- **Break-even**: 3.2 maanden\n- **5-jaar ROI**: 374%\n\n## Aanbevelingen (Prioriteit)\n1. 🔥 **Hoog**: LM Studio bot integratie (2 weken)\n2. 🔥 **Hoog**: Database optimalisatie (1 week)\n3. 📊 **Medium**: Mobile app redesign (4 weken)\n4. 🤖 **Medium**: AI lead scoring (3 weken)\n\n*Analyse uitgevoerd door: CRM Data Analyst*\n*Volgende review: ${nextWeek}*`;\n    }\n    generateCode(task, timeStr) {\n        return `# 💻 TypeScript Code Implementatie - ${timeStr}\n\n## Vue.js CRM Dashboard Component\n\n\\`\\`\\`typescript\n// components/CRMDashboard.vue\n<template>\n  <div class=\"crm-dashboard\">\n    <div class=\"stats-grid\">\n      <StatCard \n        v-for=\"stat in stats\" \n        :key=\"stat.id\"\n        :title=\"stat.title\"\n        :value=\"stat.value\"\n        :trend=\"stat.trend\"\n        :icon=\"stat.icon\"\n      />\n    </div>\n    \n    <div class=\"ai-insights\">\n      <h3>🤖 AI Insights</h3>\n      <AIInsightCard \n        v-for=\"insight in aiInsights\"\n        :key=\"insight.id\"\n        :insight=\"insight\"\n        @action=\"handleAIAction\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, computed } from 'vue'\nimport { useCRMStore } from '@/stores/crm'\nimport { useAIStore } from '@/stores/ai'\n\ninterface CRMStat {\n  id: string\n  title: string\n  value: number | string\n  trend: 'up' | 'down' | 'stable'\n  icon: string\n}\n\nconst crmStore = useCRMStore()\nconst aiStore = useAIStore()\n\nconst stats = ref<CRMStat[]>([\n  { id: '1', title: 'Leads', value: 147, trend: 'up', icon: '📈' },\n  { id: '2', title: 'Conversie', value: '23.4%', trend: 'up', icon: '🎯' },\n  { id: '3', title: 'Response', value: '1.8s', trend: 'down', icon: '⚡' }\n])\n\nconst aiInsights = computed(() => aiStore.getLatestInsights())\n\nconst handleAIAction = async (action: string, data: any) => {\n  await aiStore.executeAction(action, data)\n  await crmStore.refreshData()\n}\n\nonMounted(async () => {\n  await Promise.all([\n    crmStore.loadDashboardData(),\n    aiStore.generateInsights()\n  ])\n})\n</script>\n\\`\\`\\`\n\n## API Route - AI Integration\n\n\\`\\`\\`typescript\n// pages/api/crm/ai-insights.ts\nimport { NextApiRequest, NextApiResponse } from 'next'\nimport { prisma } from '@/lib/prisma'\nimport { aiOrchestrator } from '@/lib/ai-orchestrator'\n\nexport default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse\n) {\n  if (req.method === 'POST') {\n    try {\n      const { action, data } = req.body\n      \n      // Route naar juiste AI model\n      const result = await aiOrchestrator.route({\n        task: action,\n        data: data,\n        preferLocal: true // LM Studio eerst\n      })\n      \n      // Save insight to database\n      const insight = await prisma.aiInsight.create({\n        data: {\n          type: action,\n          result: result.content,\n          confidence: result.confidence,\n          model: result.model,\n          createdAt: new Date()\n        }\n      })\n      \n      res.status(200).json({ \n        success: true, \n        insight,\n        model: result.model \n      })\n      \n    } catch (error) {\n      console.error('AI Insight Error:', error)\n      res.status(500).json({ \n        success: false, \n        error: error.message \n      })\n    }\n  }\n}\n\\`\\`\\`\n\n*Code gegenereerd door: Lead CRM Developer*\n*Framework: Vue 3 + TypeScript + Pinia*`;\n    }\n    generatePlan(task, timeStr, nextWeek) {\n        const startDate = new Date().toLocaleDateString(\"nl-NL\");\n        return `# 📋 Project Implementation Plan - ${timeStr}\n\n## Project: Yannova CRM AI Enhancement\n\n### 🎯 Project Overview\n- **Doel**: Integreer LM Studio AI in CRM workflows\n- **Timeline**: 6 weken (Start: ${startDate})\n- **Budget**: €18,500\n- **Team**: 4 developers + 1 designer\n\n### 📅 Gedetailleerde Planning\n\n#### **Week 1: Foundation & Setup**\n- **Maandag-Dinsdag**: Requirements finaliseren\n  - Stakeholder interviews\n  - Technical requirements document\n  - UI/UX wireframes\n- **Woensdag-Donderdag**: Architecture design\n  - Database schema updates\n  - API endpoint planning\n  - AI model integration strategy\n- **Vrijdag**: Sprint planning & team setup\n\n#### **Week 2-3: Core Development**\n- **Backend Development** (2 developers):\n  - AI orchestrator service\n  - New API endpoints\n  - Database migrations\n  - LM Studio integration\n- **Frontend Development** (1 developer):\n  - Vue.js components\n  - State management (Pinia)\n  - Real-time updates\n- **Design** (1 designer):\n  - UI components\n  - User flow optimization\n\n### 👥 Team & Resources\n\n| Role | Person | Uren/week | Kosten |\n|------|---------|-----------|---------|\n| Tech Lead | Senior Dev | 40h | €4,000 |\n| Backend Dev | Mid-level | 40h | €3,200 |\n| Frontend Dev | Senior | 40h | €3,800 |\n| AI Specialist | Expert | 30h | €3,500 |\n| UI/UX Designer | Senior | 25h | €2,500 |\n| **Totaal** | | **175h** | **€17,000** |\n\n### 🎯 Success Metrics\n\n| Metric | Current | Target | Method |\n|--------|---------|---------|---------|\n| Response Time | 3.2s | <1.5s | Performance monitoring |\n| Lead Conversion | 23.4% | >30% | Analytics tracking |\n| User Satisfaction | 7.2/10 | >8.5/10 | User surveys |\n| AI Accuracy | N/A | >85% | Model validation |\n\n*Plan opgesteld door: Technical Project Manager*\n*Volgende review: ${nextWeek}*`;\n    }\n    generateAIStrategy(task, timeStr) {\n        return `# 🤖 AI Integration Strategy - ${timeStr}\n\n## Yannova CRM AI Transformation\n\n### 🎯 AI Vision\nTransform Yannova CRM into een intelligent, self-optimizing platform die:\n- **Voorspelt** klantgedrag met 90%+ accuracy\n- **Automatiseert** repetitieve taken (60% reductie)\n- **Personaliseert** elke klantinteractie\n- **Optimaliseert** workflows in real-time\n\n### 🏗️ AI Architecture Stack\n\n#### **Lokale AI (LM Studio)**\n- **Model**: Mistral-7B-Instruct\n- **Use Cases**: \n  - Chatbot responses\n  - Email classification\n  - Lead scoring\n  - Content generation\n- **Voordelen**: Privacy, geen API kosten, snelle response\n\n#### **Cloud AI (Fallback)**\n- **OpenAI GPT-4**: Complex reasoning tasks\n- **Claude**: Long-form analysis\n- **Gemini**: Multimodal processing\n\n### 📊 AI Implementation Roadmap\n\n#### **Phase 1: Foundation (Week 1-2)**\n- ✅ LM Studio setup & integration\n- ✅ AI orchestrator service\n- ✅ Fallback mechanisms\n- ✅ Basic monitoring\n\n#### **Phase 2: Core Features (Week 3-4)**\n- 🔄 Intelligent chatbot\n- 🔄 Automated lead scoring\n- 🔄 Email sentiment analysis\n- 🔄 Performance optimization\n\n### 📈 Expected Impact\n\n| Area | Current | With AI | Improvement |\n|------|---------|---------|-------------|\n| Lead Response Time | 4.2 hours | 15 minutes | -94% |\n| Conversion Rate | 23% | 35% | +52% |\n| Customer Satisfaction | 7.2/10 | 9.1/10 | +26% |\n| Manual Tasks | 60% | 25% | -58% |\n| Support Resolution | 2.3 days | 4.2 hours | -82% |\n\n*Strategie ontwikkeld door: AI Integration Specialist*`;\n    }\n}\n// Agent instanties\nconst agents = {\n    analyst: new YannovaAgent(\"CRM Data Analyst\", \"Analyseer CRM data en identificeer trends en kansen\", \"Expert in data analyse voor Yannova CRM systemen\"),\n    developer: new YannovaAgent(\"Lead CRM Developer\", \"Ontwikkel hoogwaardige CRM features\", \"Senior TypeScript/Vue.js developer\"),\n    manager: new YannovaAgent(\"Technical Project Manager\", \"Plan en co\\xf6rdineer CRM projecten\", \"Ervaren technical project manager\"),\n    ai_specialist: new YannovaAgent(\"AI Integration Specialist\", \"Integreer AI in CRM workflows\", \"Expert in AI/ML integratie\")\n};\nfunction selectAgent(task, type) {\n    if (type && type !== \"auto\") {\n        switch(type){\n            case \"analysis\":\n                return agents.analyst;\n            case \"development\":\n                return agents.developer;\n            case \"planning\":\n                return agents.manager;\n            case \"ai_strategy\":\n                return agents.ai_specialist;\n        }\n    }\n    // Automatische selectie\n    const taskLower = task.toLowerCase();\n    if ([\n        \"analys\",\n        \"data\",\n        \"performance\",\n        \"insight\"\n    ].some((word)=>taskLower.includes(word))) {\n        return agents.analyst;\n    } else if ([\n        \"code\",\n        \"ontwikkel\",\n        \"implement\",\n        \"build\"\n    ].some((word)=>taskLower.includes(word))) {\n        return agents.developer;\n    } else if ([\n        \"plan\",\n        \"project\",\n        \"roadmap\",\n        \"timeline\"\n    ].some((word)=>taskLower.includes(word))) {\n        return agents.manager;\n    } else {\n        return agents.ai_specialist;\n    }\n}\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            success: false,\n            agent: \"\",\n            task: \"\",\n            result: \"\",\n            execution_time: \"0s\",\n            timestamp: new Date().toISOString(),\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { task, type } = req.body;\n        if (!task) {\n            return res.status(400).json({\n                success: false,\n                agent: \"\",\n                task: \"\",\n                result: \"\",\n                execution_time: \"0s\",\n                timestamp: new Date().toISOString(),\n                error: \"Task is required\"\n            });\n        }\n        const startTime = Date.now();\n        const agent = selectAgent(task, type);\n        const result = agent.executeTask(task);\n        const executionTime = Date.now() - startTime;\n        console.log(`🤖 Agent: ${agent.role}`);\n        console.log(`📋 Task: ${task}`);\n        console.log(`⏱️ Time: ${executionTime}ms`);\n        return res.status(200).json({\n            success: true,\n            agent: agent.role,\n            task: task,\n            result: result,\n            execution_time: `${executionTime}ms`,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"❌ Agent Error:\", error);\n        return res.status(500).json({\n            success: false,\n            agent: \"\",\n            task: \"\",\n            result: \"\",\n            execution_time: \"0s\",\n            timestamp: new Date().toISOString(),\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/ai/simple-agents.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fsimple-agents&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Fsimple-agents.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();