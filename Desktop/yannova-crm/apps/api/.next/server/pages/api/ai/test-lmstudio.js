"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/test-lmstudio";
exports.ids = ["pages/api/ai/test-lmstudio"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Ftest-lmstudio&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Ftest-lmstudio.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Ftest-lmstudio&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Ftest-lmstudio.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/ai/test-lmstudio.ts */ \"(api)/./pages/api/ai/test-lmstudio.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/test-lmstudio\",\n        pathname: \"/api/ai/test-lmstudio\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_ai_test_lmstudio_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Ftest-lmstudio&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Ftest-lmstudio.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/lmstudio-service.ts":
/*!*********************************!*\
  !*** ./lib/lmstudio-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryLMStudio: () => (/* binding */ queryLMStudio)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function queryLMStudio(prompt, model = \"mistral-7b-instruct-v0.1\") {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"http://127.0.0.1:1234/v1/chat/completions\", {\n            model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        }, {\n            headers: {\n                \"Authorization\": \"Bearer lm-studio\"\n            },\n            timeout: 30000 // 30 seconden timeout\n        });\n        return response.data.choices[0]?.message?.content || \"Geen antwoord ontvangen\";\n    } catch (error) {\n        console.error(\"LM Studio fout:\", error.response?.data || error.message);\n        return \"Fout bij verwerken verzoek\";\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUVuQixlQUFlQyxjQUFjQyxNQUFjLEVBQUVDLFFBQVEsMEJBQTBCO0lBQ3BGLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1KLGtEQUFVLENBQUMsNkNBQTZDO1lBQzdFRztZQUNBRyxVQUFVO2dCQUFDO29CQUFFQyxNQUFNO29CQUFRQyxTQUFTTjtnQkFBTzthQUFFO1lBQzdDTyxhQUFhO1lBQ2JDLFlBQVk7UUFDZCxHQUFHO1lBQ0RDLFNBQVM7Z0JBQUUsaUJBQWlCO1lBQW1CO1lBQy9DQyxTQUFTLE1BQU8sc0JBQXNCO1FBQ3hDO1FBRUEsT0FBT1IsU0FBU1MsSUFBSSxDQUFDQyxPQUFPLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxXQUFXO0lBQ3ZELEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQSxNQUFNWixRQUFRLEVBQUVTLFFBQVFHLE1BQU1ELE9BQU87UUFDdEUsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cz80NzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeUxNU3R1ZGlvKHByb21wdDogc3RyaW5nLCBtb2RlbCA9ICdtaXN0cmFsLTdiLWluc3RydWN0LXYwLjEnKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjEyMzQvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogcHJvbXB0IH1dLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heF90b2tlbnM6IDUwMFxuICAgIH0sIHtcbiAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIGxtLXN0dWRpbycgfSxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwICAvLyAzMCBzZWNvbmRlbiB0aW1lb3V0XG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnR2VlbiBhbnR3b29yZCBvbnR2YW5nZW4nO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xNIFN0dWRpbyBmb3V0OicsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiAnRm91dCBiaWogdmVyd2Vya2VuIHZlcnpvZWsnO1xuICB9XG59ICJdLCJuYW1lcyI6WyJheGlvcyIsInF1ZXJ5TE1TdHVkaW8iLCJwcm9tcHQiLCJtb2RlbCIsInJlc3BvbnNlIiwicG9zdCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJoZWFkZXJzIiwidGltZW91dCIsImRhdGEiLCJjaG9pY2VzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/lmstudio-service.ts\n");

/***/ }),

/***/ "(api)/./pages/api/ai/test-lmstudio.ts":
/*!***************************************!*\
  !*** ./pages/api/ai/test-lmstudio.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Test basic connectivity\n        const testPrompt = \"Hallo, kun je antwoorden in het Nederlands?\";\n        const response = await (0,_lib_lmstudio_service__WEBPACK_IMPORTED_MODULE_0__.queryLMStudio)(testPrompt);\n        res.status(200).json({\n            success: true,\n            message: \"LM Studio is werkend!\",\n            testPrompt,\n            response,\n            timestamp: new Date().toISOString(),\n            model: \"mistral-7b-instruct-v0.1\",\n            endpoint: \"http://127.0.0.1:1234/v1\"\n        });\n    } catch (error) {\n        console.error(\"LM Studio test error:\", error);\n        res.status(500).json({\n            success: false,\n            error: \"LM Studio niet bereikbaar\",\n            details: error.message,\n            endpoint: \"http://127.0.0.1:1234/v1\"\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/ai/test-lmstudio.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Ftest-lmstudio&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fai%2Ftest-lmstudio.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();