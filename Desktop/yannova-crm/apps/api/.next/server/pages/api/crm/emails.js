"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/crm/emails";
exports.ids = ["pages/api/crm/emails"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Femails&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Femails.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Femails&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Femails.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_crm_emails_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/crm/emails.ts */ \"(api)/./pages/api/crm/emails.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_emails_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_emails_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/crm/emails\",\n        pathname: \"/api/crm/emails\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_crm_emails_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Femails&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Femails.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/crm/emails.ts":
/*!*********************************!*\
  !*** ./pages/api/crm/emails.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    // Mock data for demo\n    const mockEmails = [\n        {\n            id: \"1\",\n            subject: \"Vraag over CRM systeem\",\n            content: \"Hallo, ik ben ge\\xefnteresseerd in jullie CRM oplossing...\",\n            createdAt: new Date(),\n            readAt: null,\n            contact: {\n                id: \"1\",\n                email: \"<EMAIL>\",\n                name: \"Jan de Vries\",\n                company: \"Tech Solutions B.V.\"\n            }\n        },\n        {\n            id: \"2\",\n            subject: \"Offerte aanvraag Telegram bot\",\n            content: \"Kunnen jullie een offerte sturen voor een Telegram bot?\",\n            createdAt: new Date(Date.now() - 86400000),\n            readAt: new Date(),\n            contact: {\n                id: \"2\",\n                email: \"<EMAIL>\",\n                name: \"Lisa Jansen\",\n                company: \"Marketing Pro\"\n            }\n        }\n    ];\n    try {\n        const limit = parseInt(req.query.limit) || 10;\n        const offset = parseInt(req.query.offset) || 0;\n        const conversations = await prisma.conversation.findMany({\n            where: {\n                type: \"EMAIL\"\n            },\n            include: {\n                contact: {\n                    select: {\n                        id: true,\n                        email: true,\n                        name: true,\n                        company: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: limit,\n            skip: offset\n        });\n        res.json(conversations);\n    } catch (error) {\n        console.error(\"Emails error:\", error);\n        // Return mock data if database fails\n        const requestLimit = parseInt(req.query.limit) || 10;\n        res.json(mockEmails.slice(0, requestLimit));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/crm/emails.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Femails&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Femails.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();