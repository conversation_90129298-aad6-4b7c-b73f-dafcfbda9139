#!/usr/bin/env node

// ⚠️ BELANGRIJK: Locatie van .env bestand
// De .env file moet in de apps/api/ folder staan, NIET in de root directory!

console.log('🔧 .env Setup Helper voor Yannova CRM');
console.log('');
console.log('⚠️  BELANGRIJK: Locatie van .env bestand');
console.log('De .env file moet in de apps/api/ folder staan, NIET in de root directory!');
console.log('');
console.log('yannova-crm/');
console.log('├── apps/');
console.log('│   └── api/');
console.log('│       └── .env  ← HIER moet je .env bestand staan!');
console.log('├── package.json');
console.log('└── README.md');
console.log('');
```

## Waarom?

- De API draait vanuit `apps/api/` 
- Next.js zoekt daar naar de `.env` file
- Root `.env` wordt NIET gebruikt door de applicatie

## Setup Instructies

1. **Navigeer naar de juiste folder:**
   ```bash
cd apps/api
```

2. **Check of .env bestaat:**
   ```bash
ls -la | grep .env
```

3. **Als .env niet bestaat, maak hem aan:**
   ```bash
touch .env
```

4. **Voeg je configuratie toe:**
   ```env
# Database
   DATABASE_URL="file:./dev.db"

   # Email (Resend)
   RESEND_API_KEY="re_JOUW_API_KEY_HIER"
   FROM_EMAIL="<EMAIL>"

   # AI (OpenAI)
   OPENAI_API_KEY="sk-proj-JOUW_KEY_HIER"

   # Telegram Bot (optioneel)
   TELEGRAM_BOT_TOKEN=JOUW_BOT_TOKEN

   # Development
   NODE_ENV=development
   NEXT_PUBLIC_APP_URL=http://localhost:3001
```

## Verifieer Setup

Run vanuit de `apps/api` folder:
```bash
npm run dev
```

De server start op http://localhost:3001 
 const fs = require('fs');
const path = require('path');

console.log(`
🚀 Yannova CRM API - Environment Setup
=====================================
Maakt .env file voor de API app
`);

function createApiEnv() {
  const envPath = path.join(__dirname, '.env');
  
  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    console.log('⚠️  .env bestaat al in apps/api/');
    console.log('Verwijder deze eerst of gebruik setup-env.js in root');
    return;
  }

  const envContent = `# Database
DATABASE_URL="file:../../dev.db"

# Email (Resend)
RESEND_API_KEY="re_JOUW_KEY_HIER"
FROM_EMAIL="<EMAIL>"

# Development
NODE_ENV="development"
PORT=3001

# AI (optioneel)
OPENAI_API_KEY=""

# Telegram Bot (optioneel)
TELEGRAM_BOT_TOKEN=""

# WhatsApp/Twilio (optioneel)
TWILIO_ACCOUNT_SID=""
TWILIO_AUTH_TOKEN=""
TWILIO_WHATSAPP_NUMBER=""
`;

  fs.writeFileSync(envPath, envContent);
  
  console.log('✅ .env file aangemaakt in apps/api/');
  console.log('');
  console.log('📝 Volgende stappen:');
  console.log('1. Vul je Resend API key in');
  console.log('2. Run: npm run dev');
  console.log('3. Test: http://localhost:3001/api/email/config-test');
  console.log('');
  console.log('💡 Tip: Gebruik de root setup-env.js voor interactieve setup');
}

createApiEnv();
