<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LM Studio Dashboard - Yannova CRM</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .card h3 { color: #333; margin-bottom: 15px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; color: #555; }
        .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-group textarea { min-height: 80px; resize: vertical; }
        .btn { background: #667eea; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; font-weight: 600; transition: background 0.2s; }
        .btn:hover { background: #5a6fd8; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #667eea; }
        .result.success { border-left-color: #28a745; background: #d4edda; }
        .result.error { border-left-color: #dc3545; background: #f8d7da; }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .json { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; font-family: 'Monaco', 'Courier New', monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 LM Studio Dashboard</h1>
            <p>Test je lokale AI integratie voor Yannova CRM</p>
            <div style="margin-top: 15px;">
                <span>LM Studio Status: </span>
                <span id="lm-status" class="status offline">Offline</span>
                <span style="margin-left: 20px;">API Server: </span>
                <span id="api-status" class="status offline">Offline</span>
            </div>
        </div>

        <div class="grid">
            <!-- LM Studio Test -->
            <div class="card">
                <h3>🔧 LM Studio Test</h3>
                <button class="btn" onclick="testLMStudio()">Test Verbinding</button>
                <div id="lm-result" class="result" style="display: none;"></div>
            </div>

            <!-- AI Vraag -->
            <div class="card">
                <h3>💭 AI Vraag</h3>
                <div class="form-group">
                    <label>Vraag:</label>
                    <textarea id="ai-question" placeholder="Bijv: Hoe maak ik een goede Telegram bot?"></textarea>
                </div>
                <button class="btn" onclick="askAI()">Vraag Stellen</button>
                <div id="ai-result" class="result" style="display: none;"></div>
            </div>

            <!-- Email Reply -->
            <div class="card">
                <h3>📧 Email Antwoord</h3>
                <div class="form-group">
                    <label>Originele Email:</label>
                    <textarea id="email-content" placeholder="Bijv: Hallo Leon, ik wil graag een offerte..."></textarea>
                </div>
                <div class="form-group">
                    <label>Context (optioneel):</label>
                    <input type="text" id="email-context" placeholder="Bijv: Potentiële klant voor bot ontwikkeling">
                </div>
                <button class="btn" onclick="generateEmailReply()">Genereer Antwoord</button>
                <div id="email-result" class="result" style="display: none;"></div>
            </div>

            <!-- Sentiment Analyse -->
            <div class="card">
                <h3>😊 Sentiment Analyse</h3>
                <div class="form-group">
                    <label>Tekst om te analyseren:</label>
                    <textarea id="sentiment-text" placeholder="Bijv: Ik ben heel tevreden met jullie service!"></textarea>
                </div>
                <button class="btn" onclick="analyzeSentiment()">Analyseer Sentiment</button>
                <div id="sentiment-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // Check status on load
        window.onload = function() {
            checkStatus();
        };

        async function checkStatus() {
            try {
                const response = await fetch('/api/ai/test-lmstudio');
                const data = await response.json();
                
                document.getElementById('lm-status').textContent = data.success ? 'Online' : 'Offline';
                document.getElementById('lm-status').className = data.success ? 'status online' : 'status offline';
                
                document.getElementById('api-status').textContent = 'Online';
                document.getElementById('api-status').className = 'status online';
            } catch (error) {
                document.getElementById('api-status').textContent = 'Offline';
                document.getElementById('api-status').className = 'status offline';
            }
        }

        async function testLMStudio() {
            const resultDiv = document.getElementById('lm-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 Testen...';
            
            try {
                const response = await fetch('/api/ai/test-lmstudio');
                const data = await response.json();
                
                resultDiv.className = data.success ? 'result success' : 'result error';
                resultDiv.innerHTML = `
                    <strong>${data.success ? '✅ Succes!' : '❌ Fout!'}</strong><br>
                    <div class="json">${JSON.stringify(data, null, 2)}</div>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Fout:</strong> ${error.message}`;
            }
        }

        async function askAI() {
            const question = document.getElementById('ai-question').value;
            const resultDiv = document.getElementById('ai-result');
            
            if (!question) {
                alert('Voer een vraag in');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🤖 Aan het denken...';
            
            try {
                const response = await fetch('/api/ai/ask', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question })
                });
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>🤖 AI Antwoord:</strong><br>
                    <p style="margin: 10px 0; line-height: 1.5;">${data.response}</p>
                    <small>Model: ${data.model} | ${data.timestamp}</small>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Fout:</strong> ${error.message}`;
            }
        }

        async function generateEmailReply() {
            const email = document.getElementById('email-content').value;
            const context = document.getElementById('email-context').value;
            const resultDiv = document.getElementById('email-result');
            
            if (!email) {
                alert('Voer email inhoud in');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '✍️ Antwoord genereren...';
            
            try {
                const response = await fetch('/api/ai/test-email-reply', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, context })
                });
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>📧 Gegenereerd Antwoord:</strong><br>
                    <p style="margin: 10px 0; line-height: 1.5; background: white; padding: 10px; border-radius: 5px;">${data.reply}</p>
                    <small>AI Provider: ${data.aiProvider} | ${data.timestamp}</small>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Fout:</strong> ${error.message}`;
            }
        }

        async function analyzeSentiment() {
            const text = document.getElementById('sentiment-text').value;
            const resultDiv = document.getElementById('sentiment-result');
            
            if (!text) {
                alert('Voer tekst in om te analyseren');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔍 Analyseren...';
            
            try {
                const response = await fetch('/api/ai/test-sentiment', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text })
                });
                const data = await response.json();
                
                const sentimentEmoji = data.sentiment > 0.3 ? '😊' : data.sentiment < -0.3 ? '😞' : '😐';
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>📊 Sentiment Analyse:</strong><br>
                    <p style="margin: 10px 0;">
                        ${sentimentEmoji} <strong>${data.sentimentLabel}</strong> (${data.sentiment})<br>
                        📂 Categorie: ${data.category}
                    </p>
                    <small>AI Provider: ${data.aiProvider} | ${data.timestamp}</small>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Fout:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html> 