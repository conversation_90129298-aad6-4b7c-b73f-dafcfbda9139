<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Setup - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0D1117;
            color: #c9d1d9;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            color: #7AA2F7;
            margin-bottom: 40px;
            text-align: center;
            font-size: 36px;
        }

        .api-section {
            background: #161B22;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #30363d;
        }

        .api-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .api-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
        }

        .api-icon {
            font-size: 28px;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status.active {
            background: #2EA043;
            color: white;
        }

        .status.inactive {
            background: #30363d;
            color: #8898aa;
        }

        .status.error {
            background: #f85149;
            color: white;
        }

        .form-group {
            margin-bottom: 16px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #c9d1d9;
            font-weight: 500;
        }

        input {
            width: 100%;
            padding: 10px 14px;
            background: #0D1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #c9d1d9;
            font-size: 14px;
            font-family: 'Monaco', 'Courier New', monospace;
        }

        input:focus {
            outline: none;
            border-color: #7AA2F7;
            box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.1);
        }

        .help-text {
            font-size: 12px;
            color: #8898aa;
            margin-top: 4px;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        button {
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #7AA2F7;
            color: #0D1117;
        }

        .btn-primary:hover {
            background: #5a82d7;
        }

        .btn-secondary {
            background: #30363d;
            color: #c9d1d9;
        }

        .btn-secondary:hover {
            background: #484f58;
        }

        .btn-test {
            background: #2EA043;
            color: white;
        }

        .btn-test:hover {
            background: #238636;
        }

        .instructions {
            background: #0D1117;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
            font-size: 14px;
            line-height: 1.6;
        }

        .instructions ol {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .instructions a {
            color: #7AA2F7;
            text-decoration: none;
        }

        .instructions a:hover {
            text-decoration: underline;
        }

        .code {
            background: #1F2937;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Courier New', monospace;
            font-size: 13px;
        }

        .message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 6px;
            display: none;
        }

        .message.success {
            background: rgba(46, 160, 67, 0.1);
            border: 1px solid #238636;
            color: #3fb950;
        }

        .message.error {
            background: rgba(248, 81, 73, 0.1);
            border: 1px solid #f85149;
            color: #f85149;
        }

        .progress {
            margin-top: 40px;
            text-align: center;
        }

        .progress-bar {
            background: #30363d;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: #7AA2F7;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API Setup Wizard</h1>

        <!-- Resend -->
        <div class="api-section">
            <div class="api-header">
                <div class="api-title">
                    <span class="api-icon">📧</span>
                    <span>Resend (Email)</span>
                </div>
                <span id="resend-status" class="status inactive">Niet geconfigureerd</span>
            </div>

            <div class="form-group">
                <label for="resend-key">API Key:</label>
                <input type="password" id="resend-key" placeholder="re_xxxxxxxxxx">
                <div class="help-text">Je hebt deze key nodig om emails te versturen</div>
            </div>

            <div class="instructions">
                <strong>Instructies:</strong>
                <ol>
                    <li>Ga naar <a href="https://resend.com/api-keys" target="_blank">Resend API Keys</a></li>
                    <li>Klik op "Create API Key"</li>
                    <li>Geef het een naam (bijv. "Yannova CRM")</li>
                    <li>Selecteer "Full Access" of "Sending Access"</li>
                    <li>Kopieer de key en plak hierboven</li>
                </ol>
            </div>

            <div class="button-group">
                <button class="btn-primary" onclick="saveResendKey()">Opslaan</button>
                <button class="btn-test" onclick="testResend()">Test</button>
            </div>

            <div id="resend-message" class="message"></div>
        </div>

        <!-- Telegram -->
        <div class="api-section">
            <div class="api-header">
                <div class="api-title">
                    <span class="api-icon">🤖</span>
                    <span>Telegram Bot</span>
                </div>
                <span id="telegram-status" class="status inactive">Niet geconfigureerd</span>
            </div>

            <div class="form-group">
                <label for="telegram-token">Bot Token:</label>
                <input type="password" id="telegram-token" placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz">
                <div class="help-text">Voor bot notificaties en commands</div>
            </div>

            <div class="instructions">
                <strong>Instructies:</strong>
                <ol>
                    <li>Open Telegram en zoek <a href="https://t.me/BotFather" target="_blank">@BotFather</a></li>
                    <li>Stuur <code>/newbot</code></li>
                    <li>Kies een naam voor je bot</li>
                    <li>Kies een username (moet eindigen op 'bot')</li>
                    <li>Kopieer de token en plak hierboven</li>
                </ol>
            </div>

            <div class="button-group">
                <button class="btn-primary" onclick="saveTelegramToken()">Opslaan</button>
                <button class="btn-test" onclick="testTelegram()">Test</button>
            </div>

            <div id="telegram-message" class="message"></div>
        </div>

        <!-- WhatsApp -->
        <div class="api-section">
            <div class="api-header">
                <div class="api-title">
                    <span class="api-icon">💬</span>
                    <span>WhatsApp (Twilio)</span>
                </div>
                <span id="whatsapp-status" class="status inactive">Niet geconfigureerd</span>
            </div>

            <div class="form-group">
                <label for="twilio-sid">Account SID:</label>
                <input type="text" id="twilio-sid" placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
            </div>

            <div class="form-group">
                <label for="twilio-auth">Auth Token:</label>
                <input type="password" id="twilio-auth" placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
            </div>

            <div class="form-group">
                <label for="twilio-number">WhatsApp Number:</label>
                <input type="text" id="twilio-number" placeholder="whatsapp:+***********" value="whatsapp:+***********">
                <div class="help-text">Sandbox nummer voor testen</div>
            </div>

            <div class="instructions">
                <strong>Instructies:</strong>
                <ol>
                    <li>Ga naar <a href="https://console.twilio.com" target="_blank">Twilio Console</a></li>
                    <li>Maak een gratis account aan</li>
                    <li>Ga naar Account Info voor je SID en Auth Token</li>
                    <li>Voor WhatsApp: Messaging → Try it out → WhatsApp</li>
                    <li>Volg de sandbox setup instructies</li>
                </ol>
            </div>

            <div class="button-group">
                <button class="btn-primary" onclick="saveTwilio()">Opslaan</button>
                <button class="btn-test" onclick="testWhatsApp()">Test</button>
            </div>

            <div id="whatsapp-message" class="message"></div>
        </div>

        <!-- Progress -->
        <div class="progress">
            <h3>Setup Progress</h3>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <p id="progress-text">0% compleet</p>
        </div>

        <!-- Final Actions -->
        <div style="text-align: center; margin-top: 40px;">
            <button class="btn-primary" style="font-size: 18px; padding: 14px 32px;" onclick="generateEnvFile()">
                📄 Genereer .env File
            </button>
        </div>
    </div>

    <script>
        // Check current status
        async function checkStatus() {
            try {
                const response = await fetch('/api/email/config-test');
                const data = await response.json();
                
                // Update status indicators
                if (data.config.resendSet) {
                    document.getElementById('resend-status').className = 'status active';
                    document.getElementById('resend-status').textContent = 'Actief';
                }
                
                updateProgress();
            } catch (error) {
                console.error('Status check error:', error);
            }
        }

        // Update progress
        function updateProgress() {
            const apis = ['resend', 'telegram', 'whatsapp'];
            let configured = 0;
            
            apis.forEach(api => {
                const status = document.getElementById(`${api}-status`);
                if (status.classList.contains('active')) {
                    configured++;
                }
            });
            
            const percentage = Math.round((configured / apis.length) * 100);
            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = percentage + '% compleet';
        }

        // Save functions
        async function saveResendKey() {
            const key = document.getElementById('resend-key').value;
            if (!key) {
                showMessage('resend', 'error', 'Vul eerst een API key in');
                return;
            }
            
            localStorage.setItem('resend_api_key', key);
            document.getElementById('resend-status').className = 'status active';
            document.getElementById('resend-status').textContent = 'Opgeslagen';
            showMessage('resend', 'success', 'API key opgeslagen! Test nu de verbinding.');
            updateProgress();
        }

        async function saveTelegramToken() {
            const token = document.getElementById('telegram-token').value;
            if (!token) {
                showMessage('telegram', 'error', 'Vul eerst een bot token in');
                return;
            }
            
            localStorage.setItem('telegram_bot_token', token);
            document.getElementById('telegram-status').className = 'status active';
            document.getElementById('telegram-status').textContent = 'Opgeslagen';
            showMessage('telegram', 'success', 'Bot token opgeslagen!');
            updateProgress();
        }

        async function saveTwilio() {
            const sid = document.getElementById('twilio-sid').value;
            const auth = document.getElementById('twilio-auth').value;
            const number = document.getElementById('twilio-number').value;
            
            if (!sid || !auth) {
                showMessage('whatsapp', 'error', 'Vul alle velden in');
                return;
            }
            
            localStorage.setItem('twilio_account_sid', sid);
            localStorage.setItem('twilio_auth_token', auth);
            localStorage.setItem('twilio_whatsapp_number', number);
            
            document.getElementById('whatsapp-status').className = 'status active';
            document.getElementById('whatsapp-status').textContent = 'Opgeslagen';
            showMessage('whatsapp', 'success', 'Twilio credentials opgeslagen!');
            updateProgress();
        }

        // Test functions
        async function testResend() {
            showMessage('resend', 'success', 'Test email wordt verstuurd...');
            
            try {
                const response = await fetch('/api/email/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        to: '<EMAIL>',
                        subject: 'Test vanuit Yannova CRM',
                        content: 'Dit is een test email!',
                        testType: 'direct'
                    })
                });
                
                const result = await response.json();
                showMessage('resend', result.success ? 'success' : 'error', result.message || 'Test compleet');
            } catch (error) {
                showMessage('resend', 'error', 'Test mislukt: ' + error.message);
            }
        }

        async function testTelegram() {
            showMessage('telegram', 'success', 'Bot wordt getest...');
            // Implement Telegram test
        }

        async function testWhatsApp() {
            showMessage('whatsapp', 'success', 'WhatsApp wordt getest...');
            // Implement WhatsApp test
        }

        // Generate .env file content
        function generateEnvFile() {
            const resendKey = localStorage.getItem('resend_api_key') || 're_YOUR_KEY';
            const telegramToken = localStorage.getItem('telegram_bot_token') || '';
            const twilioSid = localStorage.getItem('twilio_account_sid') || '';
            const twilioAuth = localStorage.getItem('twilio_auth_token') || '';
            const twilioNumber = localStorage.getItem('twilio_whatsapp_number') || 'whatsapp:+***********';
            
            const envContent = `# Database
DATABASE_URL="file:./dev.db"

# Email (Resend)
RESEND_API_KEY="${resendKey}"
FROM_EMAIL="<EMAIL>"

# AI (OpenAI)
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Telegram Bot
TELEGRAM_BOT_TOKEN="${telegramToken}"

# WhatsApp (Twilio)
TWILIO_ACCOUNT_SID="${twilioSid}"
TWILIO_AUTH_TOKEN="${twilioAuth}"
TWILIO_WHATSAPP_NUMBER="${twilioNumber}"

# Development
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3001"`;

            // Create download
            const blob = new Blob([envContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '.env';
            a.click();
            
            alert('✅ .env file gedownload!\n\nPlaats dit bestand in de root directory van je project.');
        }

        // Show message
        function showMessage(api, type, text) {
            const message = document.getElementById(`${api}-message`);
            message.className = `message ${type}`;
            message.textContent = text;
            message.style.display = 'block';
            
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        // Load saved values
        window.onload = () => {
            checkStatus();
            
            // Load from localStorage
            const resendKey = localStorage.getItem('resend_api_key');
            if (resendKey) document.getElementById('resend-key').value = resendKey;
            
            const telegramToken = localStorage.getItem('telegram_bot_token');
            if (telegramToken) document.getElementById('telegram-token').value = telegramToken;
            
            const twilioSid = localStorage.getItem('twilio_account_sid');
            if (twilioSid) document.getElementById('twilio-sid').value = twilioSid;
            
            const twilioAuth = localStorage.getItem('twilio_auth_token');
            if (twilioAuth) document.getElementById('twilio-auth').value = twilioAuth;
            
            const twilioNumber = localStorage.getItem('twilio_whatsapp_number');
            if (twilioNumber) document.getElementById('twilio-number').value = twilioNumber;
        };
    </script>
</body>
</html> 