<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Orchestrator Dashboard - Yannova CRM</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f7; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1rem; }
        .status-bar { display: flex; gap: 15px; margin-top: 20px; flex-wrap: wrap; }
        .status-item { background: rgba(255,255,255,0.1); padding: 10px 15px; border-radius: 8px; }
        .status-item.online { background: rgba(52,199,89,0.2); }
        .status-item.offline { background: rgba(255,59,48,0.2); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid #e5e5e7; }
        .card h3 { color: #1d1d1f; margin-bottom: 20px; font-size: 1.3rem; display: flex; align-items: center; gap: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #1d1d1f; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 12px; border: 1px solid #d2d2d7; border-radius: 8px; font-size: 14px; background: #fbfbfd; }
        .form-group textarea { min-height: 120px; resize: vertical; font-family: 'Monaco', 'Courier New', monospace; }
        .form-group select { cursor: pointer; }
        .btn { background: #007AFF; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600; transition: all 0.2s; }
        .btn:hover { background: #0056CC; transform: translateY(-1px); }
        .btn:disabled { background: #d2d2d7; cursor: not-allowed; transform: none; }
        .btn.secondary { background: #8E8E93; }
        .btn.secondary:hover { background: #6D6D70; }
        .result { margin-top: 20px; padding: 20px; background: #f2f2f7; border-radius: 8px; border-left: 4px solid #007AFF; }
        .result.success { border-left-color: #34C759; background: #f0fff4; }
        .result.error { border-left-color: #FF3B30; background: #fff5f5; }
        .result.warning { border-left-color: #FF9500; background: #fff9f0; }
        .code-block { background: #1d1d1f; color: #ffffff; padding: 20px; border-radius: 8px; overflow-x: auto; font-family: 'Monaco', 'Courier New', monospace; font-size: 13px; line-height: 1.5; }
        .model-status { display: flex; gap: 10px; flex-wrap: wrap; margin: 15px 0; }
        .model-badge { padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .model-badge.online { background: #d4edda; color: #155724; }
        .model-badge.offline { background: #f8d7da; color: #721c24; }
        .tabs { display: flex; gap: 2px; margin-bottom: 20px; background: #f2f2f7; border-radius: 8px; padding: 4px; }
        .tab { padding: 8px 16px; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s; }
        .tab.active { background: white; color: #007AFF; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .loading { display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #007AFF; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .recommendations { background: #e8f4fd; border: 1px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-top: 15px; }
        .recommendations h4 { color: #0056cc; margin-bottom: 10px; }
        .recommendations ul { list-style: none; }
        .recommendations li { margin-bottom: 8px; padding-left: 20px; position: relative; }
        .recommendations li:before { content: "💡"; position: absolute; left: 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Orchestrator</h1>
            <p>Intelligente AI routing voor Yannova CRM - Lokale modellen + API fallback</p>
            <div class="status-bar" id="status-bar">
                <div class="status-item offline">🔄 Loading...</div>
            </div>
        </div>

        <!-- Model Status Card -->
        <div class="card" style="grid-column: 1 / -1;">
            <h3>📊 Model Status & Health</h3>
            <button class="btn secondary" onclick="refreshStatus()">🔄 Refresh Status</button>
            <div id="model-status" class="model-status">
                <div class="loading"></div>
            </div>
            <div id="recommendations" class="recommendations" style="display: none;"></div>
        </div>

        <div class="grid">
            <!-- Coding Assistant -->
            <div class="card">
                <h3>💻 Coding Assistant</h3>
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('code', 'review')">Review</div>
                    <div class="tab" onclick="switchTab('code', 'optimize')">Optimize</div>
                    <div class="tab" onclick="switchTab('code', 'debug')">Debug</div>
                    <div class="tab" onclick="switchTab('code', 'explain')">Explain</div>
                </div>
                
                <div class="form-group">
                    <label>Programming Language:</label>
                    <select id="code-language">
                        <option value="typescript">TypeScript</option>
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="vue">Vue.js</option>
                        <option value="sql">SQL</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Code:</label>
                    <textarea id="code-input" placeholder="Plak hier je code..."></textarea>
                </div>
                
                <button class="btn" onclick="runCodingAssistant()">🚀 Analyseer Code</button>
                <div id="code-result" class="result" style="display: none;"></div>
            </div>

            <!-- Project Planning -->
            <div class="card">
                <h3>📋 Project Planning</h3>
                <div class="form-group">
                    <label>Project Type:</label>
                    <select id="project-type">
                        <option value="crm-feature">CRM Feature</option>
                        <option value="bot-integration">Bot Integration</option>
                        <option value="ai-enhancement">AI Enhancement</option>
                        <option value="mobile-feature">Mobile Feature</option>
                        <option value="api-endpoint">API Endpoint</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Project Description:</label>
                    <textarea id="project-description" placeholder="Beschrijf wat je wilt bouwen..."></textarea>
                </div>
                
                <div class="form-group">
                    <label>Requirements (comma separated):</label>
                    <input type="text" id="project-requirements" placeholder="API, Database, Authentication">
                </div>
                
                <div class="form-group">
                    <label>Timeline:</label>
                    <select id="project-timeline">
                        <option value="short">Kort (1-3 dagen)</option>
                        <option value="medium">Medium (1-2 weken)</option>
                        <option value="long">Lang (1+ maanden)</option>
                    </select>
                </div>
                
                <button class="btn" onclick="generateProjectPlan()">📈 Genereer Plan</button>
                <div id="project-result" class="result" style="display: none;"></div>
            </div>

            <!-- CRM Analysis -->
            <div class="card">
                <h3>📊 CRM Analysis</h3>
                <div class="form-group">
                    <label>Analysis Type:</label>
                    <select id="analysis-type">
                        <option value="customer-behavior">Customer Behavior</option>
                        <option value="sales-performance">Sales Performance</option>
                        <option value="email-effectiveness">Email Effectiveness</option>
                        <option value="bot-interactions">Bot Interactions</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Data (JSON/CSV/Text):</label>
                    <textarea id="analysis-data" placeholder="Plak hier je CRM data..."></textarea>
                </div>
                
                <button class="btn" onclick="runCRMAnalysis()">🔍 Analyseer Data</button>
                <div id="analysis-result" class="result" style="display: none;"></div>
            </div>

            <!-- Quick AI Chat -->
            <div class="card">
                <h3>💬 Quick AI Chat</h3>
                <div class="form-group">
                    <label>Vraag:</label>
                    <textarea id="chat-question" placeholder="Stel een vraag over development, CRM, of AI..."></textarea>
                </div>
                
                <button class="btn" onclick="quickChat()">💭 Vraag AI</button>
                <div id="chat-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentCodeAction = 'review';
        let modelStatus = {};

        // Initialize
        window.onload = function() {
            refreshStatus();
        };

        function switchTab(category, action) {
            currentCodeAction = action;
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        async function refreshStatus() {
            const statusBar = document.getElementById('status-bar');
            const modelStatusDiv = document.getElementById('model-status');
            const recommendationsDiv = document.getElementById('recommendations');
            
            statusBar.innerHTML = '<div class="status-item offline">🔄 Checking...</div>';
            modelStatusDiv.innerHTML = '<div class="loading"></div>';
            
            try {
                const response = await fetch('/api/ai/orchestrator-status');
                const data = await response.json();
                
                modelStatus = data.models;
                
                // Update status bar
                statusBar.innerHTML = `
                    <div class="status-item ${data.summary.online > 0 ? 'online' : 'offline'}">
                        📡 ${data.summary.online}/${data.summary.total} Models Online
                    </div>
                    <div class="status-item ${data.summary.healthScore >= 50 ? 'online' : 'offline'}">
                        💚 Health: ${data.summary.healthScore}%
                    </div>
                `;
                
                // Update model badges
                const modelBadges = Object.entries(data.models).map(([key, status]) => 
                    `<div class="model-badge ${status ? 'online' : 'offline'}">
                        ${key}: ${status ? '✅ Online' : '❌ Offline'}
                    </div>`
                ).join('');
                
                modelStatusDiv.innerHTML = modelBadges;
                
                // Show recommendations
                if (data.recommendations && data.recommendations.length > 0) {
                    recommendationsDiv.style.display = 'block';
                    recommendationsDiv.innerHTML = `
                        <h4>Aanbevelingen:</h4>
                        <ul>
                            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    `;
                } else {
                    recommendationsDiv.style.display = 'none';
                }
                
            } catch (error) {
                statusBar.innerHTML = '<div class="status-item offline">❌ Connection Error</div>';
                modelStatusDiv.innerHTML = '<div class="result error">Kan status niet ophalen</div>';
            }
        }

        async function runCodingAssistant() {
            const code = document.getElementById('code-input').value;
            const language = document.getElementById('code-language').value;
            const resultDiv = document.getElementById('code-result');
            
            if (!code.trim()) {
                alert('Voer code in om te analyseren');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🤖 Code wordt geanalyseerd...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('/api/ai/coding-assistant', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        code, 
                        language, 
                        action: currentCodeAction 
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>🎯 ${currentCodeAction.toUpperCase()} - ${language}</h4>
                        <div style="white-space: pre-wrap; margin: 15px 0;">${data.response}</div>
                        <div class="code-block">
                            <strong>Model:</strong> ${data.model}<br>
                            <strong>Provider:</strong> ${data.provider}<br>
                            <strong>Timestamp:</strong> ${data.timestamp}
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }

        async function generateProjectPlan() {
            const projectType = document.getElementById('project-type').value;
            const description = document.getElementById('project-description').value;
            const requirements = document.getElementById('project-requirements').value.split(',').map(r => r.trim()).filter(r => r);
            const timeline = document.getElementById('project-timeline').value;
            const resultDiv = document.getElementById('project-result');
            
            if (!description.trim()) {
                alert('Voer een project beschrijving in');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '📋 Project plan wordt gegenereerd...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('/api/ai/project-planning', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        projectType, 
                        description, 
                        requirements, 
                        timeline,
                        complexity: 'medium'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>📈 Project Plan: ${projectType}</h4>
                        <div style="white-space: pre-wrap; margin: 15px 0;">${data.response}</div>
                        <div class="recommendations">
                            <h4>Suggesties:</h4>
                            <ul>
                                ${data.suggestions.map(s => `<li>${s}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="code-block">
                            <strong>Model:</strong> ${data.model} | <strong>Provider:</strong> ${data.provider}
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }

        async function runCRMAnalysis() {
            const analysisType = document.getElementById('analysis-type').value;
            const data = document.getElementById('analysis-data').value;
            const resultDiv = document.getElementById('analysis-result');
            
            if (!data.trim()) {
                alert('Voer data in om te analyseren');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔍 Data wordt geanalyseerd...';
            resultDiv.className = 'result';
            
            // Simulate CRM analysis (you would create this endpoint)
            try {
                const response = await fetch('/api/ai/ask', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        question: `Analyseer deze CRM data voor ${analysisType}:\n\n${data}\n\nGeef Nederlandse analyse met inzichten en aanbevelingen.`
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>📊 CRM Analysis: ${analysisType}</h4>
                        <div style="white-space: pre-wrap; margin: 15px 0;">${result.response}</div>
                        <div class="code-block">
                            <strong>Model:</strong> ${result.model} | <strong>Timestamp:</strong> ${result.timestamp}
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }

        async function quickChat() {
            const question = document.getElementById('chat-question').value;
            const resultDiv = document.getElementById('chat-result');
            
            if (!question.trim()) {
                alert('Stel een vraag');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '💭 AI denkt na...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('/api/ai/ask', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>💬 AI Antwoord:</h4>
                        <div style="white-space: pre-wrap; margin: 15px 0;">${data.response}</div>
                        <div class="code-block">
                            <strong>Model:</strong> ${data.model} | <strong>Timestamp:</strong> ${data.timestamp}
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html> 