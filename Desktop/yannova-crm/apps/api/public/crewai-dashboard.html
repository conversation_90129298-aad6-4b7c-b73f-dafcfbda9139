<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CrewAI Multi-Agent Dashboard - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .agent-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .agent-card h4 {
            margin-bottom: 8px;
        }

        .agent-card p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .workflow-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .workflow-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .workflow-btn:hover {
            transform: translateY(-2px);
        }

        .custom-task {
            margin-top: 20px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .input-group input,
        .input-group textarea,
        .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .input-group input:focus,
        .input-group textarea:focus,
        .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .textarea {
            min-height: 100px;
            resize: vertical;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results {
            grid-column: 1 / -1;
        }

        .result-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .agent-grid {
                grid-template-columns: 1fr;
            }
            
            .workflow-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 CrewAI Multi-Agent Dashboard</h1>
            <p>Gebruik AI agents voor CRM optimalisatie en ontwikkeling</p>
        </div>

        <div class="dashboard">
            <!-- Agents Overview -->
            <div class="card">
                <h3>🎯 AI Agents Team</h3>
                <div class="agent-grid">
                    <div class="agent-card">
                        <h4>📊 CRM Analyst</h4>
                        <p>Data analyse & insights</p>
                    </div>
                    <div class="agent-card">
                        <h4>💻 Lead Developer</h4>
                        <p>Code generatie & architectuur</p>
                    </div>
                    <div class="agent-card">
                        <h4>📋 Project Manager</h4>
                        <p>Planning & coördinatie</p>
                    </div>
                    <div class="agent-card">
                        <h4>🤖 AI Specialist</h4>
                        <p>AI integratie & optimalisatie</p>
                    </div>
                </div>
            </div>

            <!-- Quick Workflows -->
            <div class="card">
                <h3>⚡ Quick Workflows</h3>
                <div class="workflow-buttons">
                    <button class="workflow-btn" onclick="runOptimization()">
                        🚀 Complete CRM Optimalisatie
                    </button>
                    <button class="workflow-btn" onclick="runAnalysis()">
                        📈 Performance Analyse
                    </button>
                    <button class="workflow-btn" onclick="runPlanning()">
                        📅 Project Planning
                    </button>
                    <button class="workflow-btn" onclick="runDevelopment()">
                        🔧 Code Development
                    </button>
                </div>
            </div>

            <!-- Custom Task -->
            <div class="card">
                <h3>🎯 Custom AI Task</h3>
                <form id="customTaskForm" class="custom-task">
                    <div class="input-group">
                        <label for="taskType">Task Type:</label>
                        <select id="taskType" name="taskType">
                            <option value="custom">Custom Task</option>
                            <option value="analysis">Data Analyse</option>
                            <option value="development">Code Development</option>
                            <option value="planning">Project Planning</option>
                            <option value="optimization">Optimalisatie</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label for="taskDescription">Beschrijving:</label>
                        <textarea 
                            id="taskDescription" 
                            name="taskDescription" 
                            class="textarea"
                            placeholder="Beschrijf wat je wilt dat de AI agents doen..."
                            required
                        ></textarea>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn">
                        🚀 Start AI Agents
                    </button>
                </form>
            </div>

            <!-- Results -->
            <div class="card results">
                <h3>📋 Resultaten</h3>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>AI Agents aan het werk... Dit kan enkele minuten duren.</p>
                </div>
                
                <div id="status"></div>
                <div id="results" class="result-output" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        async function callCrewAI(task, type = 'custom') {
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const status = document.getElementById('status');
            const submitBtn = document.getElementById('submitBtn');
            
            // Show loading
            loading.style.display = 'block';
            results.style.display = 'none';
            status.innerHTML = '';
            submitBtn.disabled = true;
            
            try {
                console.log('🤖 Starting CrewAI agents...');
                
                const response = await fetch('/api/ai/crewai-agents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task: task,
                        type: type
                    })
                });
                
                const data = await response.json();
                
                loading.style.display = 'none';
                submitBtn.disabled = false;
                
                if (data.success) {
                    status.innerHTML = `
                        <div class="status success">
                            ✅ AI Agents voltooid! Agents gebruikt: ${data.agents?.join(', ') || 'Onbekend'}
                        </div>
                    `;
                    results.innerHTML = data.result || 'Geen resultaat ontvangen';
                    results.style.display = 'block';
                } else {
                    status.innerHTML = `
                        <div class="status error">
                            ❌ Error: ${data.error}
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Error:', error);
                loading.style.display = 'none';
                submitBtn.disabled = false;
                
                status.innerHTML = `
                    <div class="status error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Quick workflow functions
        async function runOptimization() {
            await callCrewAI('', 'optimization');
        }
        
        async function runAnalysis() {
            await callCrewAI('Analyseer de huidige CRM performance en geef verbeterpunten', 'analysis');
        }
        
        async function runPlanning() {
            await callCrewAI('Maak een projectplan voor CRM verbetering', 'planning');
        }
        
        async function runDevelopment() {
            await callCrewAI('Ontwikkel code voor nieuwe CRM features', 'development');
        }
        
        // Custom task form
        document.getElementById('customTaskForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const taskType = document.getElementById('taskType').value;
            const taskDescription = document.getElementById('taskDescription').value;
            
            if (!taskDescription.trim()) {
                alert('Voer een taakbeschrijving in');
                return;
            }
            
            await callCrewAI(taskDescription, taskType);
        });
        
        // Auto-resize textarea
        document.getElementById('taskDescription').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        console.log('🤖 CrewAI Dashboard geladen');
        console.log('💡 Tip: Start met een quick workflow of definieer een custom task');
    </script>
</body>
</html> 