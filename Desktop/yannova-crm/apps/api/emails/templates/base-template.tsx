import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface BaseTemplateProps {
  preview: string;
  heading: string;
  children: React.ReactNode;
  footerText?: string;
}

export const BaseTemplate = ({
  preview,
  heading,
  children,
  footerText = 'Met vriendelijke groet,',
}: BaseTemplateProps) => (
  <Html>
    <Head />
    <Preview>{preview}</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Logo */}
        <Section style={header}>
          <Img
            src="https://yannova.be/logo.png"
            width="150"
            height="50"
            alt="Yannova"
            style={logo}
          />
        </Section>

        {/* Main Content */}
        <Section style={content}>
          <Heading style={h1}>{heading}</Heading>
          {children}
        </Section>

        {/* Footer */}
        <Hr style={hr} />
        <Section style={footer}>
          <Text style={footerTextStyle}>{footerText}</Text>
          <Text style={signature}>
            <strong><PERSON></strong>
            <br />
            AI Developer & Bot Specialist
          </Text>
          
          <Section style={socialLinks}>
            <Link href="https://yannova.be" style={link}>
              Website
            </Link>
            {' • '}
            <Link href="https://linkedin.com/in/yannova" style={link}>
              LinkedIn
            </Link>
            {' • '}
            <Link href="https://github.com/yannova" style={link}>
              GitHub
            </Link>
          </Section>

          <Text style={address}>
            Yannova • <EMAIL> • +31 6 12345678
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
};

const header = {
  padding: '32px 48px 24px',
  textAlign: 'center' as const,
  borderBottom: '1px solid #e6ebf1',
};

const logo = {
  margin: '0 auto',
};

const content = {
  padding: '48px',
};

const h1 = {
  color: '#0D1117',
  fontSize: '32px',
  fontWeight: '700',
  margin: '0 0 24px',
  textAlign: 'center' as const,
  letterSpacing: '-0.5px',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '0',
};

const footer = {
  color: '#8898aa',
  fontSize: '14px',
  lineHeight: '24px',
  padding: '32px 48px',
  textAlign: 'center' as const,
};

const footerTextStyle = {
  margin: '0 0 16px',
  color: '#8898aa',
};

const signature = {
  margin: '0 0 24px',
  color: '#0D1117',
};

const socialLinks = {
  margin: '0 0 16px',
};

const link = {
  color: '#7AA2F7',
  textDecoration: 'none',
  fontWeight: '500',
};

const address = {
  margin: '0',
  fontSize: '12px',
  color: '#8898aa',
};

export default BaseTemplate; 