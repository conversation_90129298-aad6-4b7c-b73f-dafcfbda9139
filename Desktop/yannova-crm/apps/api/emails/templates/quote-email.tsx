import { Button, Text, Section, Row, Column } from '@react-email/components';
import * as React from 'react';
import BaseTemplate from './base-template';

interface QuoteItem {
  description: string;
  price: number;
  quantity: number;
}

interface QuoteEmailProps {
  clientName: string;
  projectName: string;
  items: QuoteItem[];
  validUntil: string;
  notes?: string;
}

export const QuoteEmail = ({
  clientName,
  projectName,
  items,
  validUntil,
  notes,
}: QuoteEmailProps) => {
  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const btw = subtotal * 0.21;
  const total = subtotal + btw;

  return (
    <BaseTemplate
      preview={`Offerte voor ${projectName}`}
      heading="Offerte"
    >
      <Text style={paragraph}>
        Beste {clientName},
      </Text>

      <Text style={paragraph}>
        Hierbij de offerte voor <strong>{projectName}</strong>.
      </Text>

      <Section style={quoteBox}>
        <Text style={quoteHeader}>Offerte Details</Text>
        
        {items.map((item, index) => (
          <Row key={index} style={itemRow}>
            <Column style={itemDescription}>
              {item.description}
              <br />
              <span style={itemQuantity}>Aantal: {item.quantity}</span>
            </Column>
            <Column style={itemPrice}>
              €{(item.price * item.quantity).toFixed(2)}
            </Column>
          </Row>
        ))}

        <Row style={divider} />

        <Row style={totalRow}>
          <Column style={totalLabel}>Subtotaal</Column>
          <Column style={totalAmount}>€{subtotal.toFixed(2)}</Column>
        </Row>

        <Row style={totalRow}>
          <Column style={totalLabel}>BTW (21%)</Column>
          <Column style={totalAmount}>€{btw.toFixed(2)}</Column>
        </Row>

        <Row style={finalTotalRow}>
          <Column style={finalTotalLabel}>Totaal</Column>
          <Column style={finalTotalAmount}>€{total.toFixed(2)}</Column>
        </Row>
      </Section>

      {notes && (
        <Section style={notesSection}>
          <Text style={notesHeader}>Opmerkingen</Text>
          <Text style={notesText}>{notes}</Text>
        </Section>
      )}

      <Text style={paragraph}>
        Deze offerte is geldig tot <strong>{validUntil}</strong>.
      </Text>

      <Button
        href={`https://yannova.be/accept-quote?id=${Date.now()}`}
        style={acceptButton}
      >
        Accepteer Offerte
      </Button>

      <Text style={smallText}>
        Heb je vragen over deze offerte? Neem gerust contact op!
      </Text>
    </BaseTemplate>
  );
};

// Styles
const paragraph = {
  color: '#525f7f',
  fontSize: '16px',
  lineHeight: '28px',
  margin: '16px 0',
};

const quoteBox = {
  backgroundColor: '#f6f9fc',
  borderRadius: '8px',
  padding: '24px',
  margin: '32px 0',
};

const quoteHeader = {
  fontSize: '18px',
  fontWeight: '600',
  color: '#0D1117',
  marginBottom: '16px',
};

const itemRow = {
  padding: '12px 0',
  borderBottom: '1px solid #e6ebf1',
};

const itemDescription = {
  color: '#525f7f',
  fontSize: '14px',
  width: '70%',
};

const itemQuantity = {
  color: '#8898aa',
  fontSize: '12px',
};

const itemPrice = {
  color: '#0D1117',
  fontSize: '14px',
  fontWeight: '500',
  textAlign: 'right' as const,
  width: '30%',
};

const divider = {
  borderBottom: '2px solid #e6ebf1',
  margin: '16px 0',
};

const totalRow = {
  padding: '8px 0',
};

const totalLabel = {
  color: '#525f7f',
  fontSize: '14px',
  width: '70%',
};

const totalAmount = {
  color: '#0D1117',
  fontSize: '14px',
  textAlign: 'right' as const,
  width: '30%',
};

const finalTotalRow = {
  padding: '12px 0',
  borderTop: '2px solid #0D1117',
  marginTop: '8px',
};

const finalTotalLabel = {
  color: '#0D1117',
  fontSize: '16px',
  fontWeight: '600',
  width: '70%',
};

const finalTotalAmount = {
  color: '#0D1117',
  fontSize: '18px',
  fontWeight: '700',
  textAlign: 'right' as const,
  width: '30%',
};

const notesSection = {
  backgroundColor: '#fff8dc',
  borderRadius: '6px',
  padding: '16px',
  margin: '24px 0',
  border: '1px solid #ffd700',
};

const notesHeader = {
  fontSize: '14px',
  fontWeight: '600',
  color: '#0D1117',
  marginBottom: '8px',
};

const notesText = {
  fontSize: '14px',
  color: '#525f7f',
  margin: 0,
};

const acceptButton = {
  backgroundColor: '#2EA043',
  borderRadius: '6px',
  color: '#fff',
  display: 'inline-block',
  fontSize: '16px',
  fontWeight: '600',
  lineHeight: '1',
  padding: '16px 32px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  margin: '32px auto',
};

const smallText = {
  color: '#8898aa',
  fontSize: '14px',
  textAlign: 'center' as const,
  margin: '16px 0',
};

export default QuoteEmail; 