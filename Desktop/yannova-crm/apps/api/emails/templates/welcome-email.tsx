import { Button, Text } from '@react-email/components';
import * as React from 'react';
import BaseTemplate from './base-template';

interface WelcomeEmailProps {
  name: string;
  email: string;
}

export const WelcomeEmail = ({ name, email }: WelcomeEmailProps) => (
  <BaseTemplate
    preview={`Welkom bij Yannova CRM, ${name}!`}
    heading={`Welkom ${name}! 🎉`}
  >
    <Text style={paragraph}>
      Bedankt voor je aanmelding bij Yannova CRM. We zijn blij dat je er bent!
    </Text>

    <Text style={paragraph}>
      Met Yannova CRM kun je:
    </Text>

    <ul style={list}>
      <li>📧 Email campagnes automatiseren</li>
      <li>🤖 Telegram & WhatsApp bots integreren</li>
      <li>📊 Klantinteracties analyseren</li>
      <li>🚀 Workflows automatiseren met AI</li>
    </ul>

    <Button
      href="https://yannova.be/dashboard"
      style={button}
    >
      Ga naar Dashboard
    </Button>

    <Text style={paragraph}>
      Heb je vragen? Reply gewoon op deze email of stuur een bericht naar{' '}
      <a href="mailto:<EMAIL>" style={link}>
        <EMAIL>
      </a>
    </Text>
  </BaseTemplate>
);

// Styles
const paragraph = {
  color: '#525f7f',
  fontSize: '16px',
  lineHeight: '28px',
  margin: '16px 0',
};

const list = {
  color: '#525f7f',
  fontSize: '16px',
  lineHeight: '28px',
  margin: '16px 0',
  paddingLeft: '24px',
};

const button = {
  backgroundColor: '#7AA2F7',
  borderRadius: '6px',
  color: '#fff',
  display: 'inline-block',
  fontSize: '16px',
  fontWeight: '600',
  lineHeight: '1',
  padding: '16px 24px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  margin: '32px auto',
};

const link = {
  color: '#7AA2F7',
  textDecoration: 'none',
};

export default WelcomeEmail; 