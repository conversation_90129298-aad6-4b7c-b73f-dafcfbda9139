import { queryLMStudio } from './lmstudio-service';
import { OpenAIService } from './openai-service';
import { MODEL_CONFIGS, TASK_PREFERENCES, ModelConfig } from './ai-model-configs';

export interface AIModel {
  name: string;
  type: 'local' | 'api';
  endpoint?: string;
  capabilities: string[];
  priority: number;
  maxTokens: number;
}

export interface AITask {
  type: 'coding' | 'analysis' | 'email' | 'chat' | 'planning';
  priority: 'low' | 'medium' | 'high';
  context?: string;
  language?: string;
}

export class AIOrchestrator {
  private models: Map<string, AIModel> = new Map();
  
  constructor() {
    this.initializeModels();
  }
  
  private initializeModels() {
    // LM Studio Models
    this.models.set('mistral-code', {
      name: 'mistral-7b-instruct-v0.1',
      type: 'local',
      endpoint: 'http://127.0.0.1:1234/v1',
      capabilities: ['coding', 'analysis', 'planning'],
      priority: 1,
      maxTokens: 4096
    });
    
    this.models.set('deepseek-coder', {
      name: 'mistral-7b-instruct-v0.1',
      type: 'local',
      endpoint: 'http://127.0.0.1:1234/v1',
      capabilities: ['coding', 'debugging', 'refactoring'],
      priority: 2,
      maxTokens: 4096
    });
    
    // API Models (fallback)
    this.models.set('openai-gpt4', {
      name: 'gpt-4',
      type: 'api',
      capabilities: ['coding', 'analysis', 'email', 'chat', 'planning'],
      priority: 3,
      maxTokens: 8192
    });
    
    this.models.set('openai-gpt3', {
      name: 'gpt-3.5-turbo',
      type: 'api',
      capabilities: ['chat', 'email', 'analysis'],
      priority: 4,
      maxTokens: 4096
    });
  }
  
  // Intelligente model selectie met configuratie
  selectModel(task: AITask): AIModel | null {
    // Gebruik task preferences voor intelligente routing
    const preferredModelIds = TASK_PREFERENCES[task.type] || Object.keys(this.models);
    
    // Zoek het eerste beschikbare model uit de voorkeurlijst
    for (const modelId of preferredModelIds) {
      const model = this.models.get(modelId);
      if (model && model.capabilities.includes(task.type)) {
        return model;
      }
    }
    
    // Fallback: alle modellen die de taak kunnen uitvoeren
    const availableModels = Array.from(this.models.values())
      .filter(model => model.capabilities.includes(task.type))
      .sort((a, b) => a.priority - b.priority);
    
    return availableModels[0] || null;
  }
  
  // Universele query functie
  async query(prompt: string, task: AITask): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    const selectedModel = this.selectModel(task);
    
    if (!selectedModel) {
      throw new Error(`Geen geschikt model gevonden voor taak: ${task.type}`);
    }
    
    try {
      let response: string;
      let provider: string;
      
      if (selectedModel.type === 'local') {
        // Gebruik LM Studio
        response = await queryLMStudio(prompt, selectedModel.name);
        provider = 'LM Studio';
      } else {
        // Gebruik OpenAI als fallback
        response = await this.queryOpenAI(prompt, selectedModel, task);
        provider = 'OpenAI';
      }
      
      return {
        response,
        model: selectedModel.name,
        provider,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Model ${selectedModel.name} failed:`, error);
      
      // Fallback naar volgende model
      return this.fallbackQuery(prompt, task, selectedModel);
    }
  }
  
  // Fallback systeem
  private async fallbackQuery(prompt: string, task: AITask, failedModel: AIModel): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    const availableModels = Array.from(this.models.values())
      .filter(model => 
        model.capabilities.includes(task.type) && 
        model.name !== failedModel.name
      )
      .sort((a, b) => a.priority - b.priority);
    
    for (const model of availableModels) {
      try {
        let response: string;
        let provider: string;
        
        if (model.type === 'local') {
          response = await queryLMStudio(prompt, model.name);
          provider = 'LM Studio (Fallback)';
        } else {
          response = await this.queryOpenAI(prompt, model, task);
          provider = 'OpenAI (Fallback)';
        }
        
        return {
          response,
          model: model.name,
          provider,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error(`Fallback model ${model.name} failed:`, error);
        continue;
      }
    }
    
    throw new Error('Alle modellen zijn niet beschikbaar');
  }
  
  // OpenAI query helper
  private async queryOpenAI(prompt: string, model: AIModel, task: AITask): Promise<string> {
    switch (task.type) {
      case 'email':
        return await OpenAIService.generateEmailReply(prompt, task.context);
      case 'analysis':
        const sentiment = await OpenAIService.analyzeSentiment(prompt);
        return `Sentiment: ${sentiment}`;
      case 'planning':
      case 'coding':
      case 'chat':
        // Voor deze taken gebruiken we een generieke fallback
        return `OpenAI fallback niet beschikbaar voor ${task.type}. Gebruik LM Studio voor deze functionaliteit.`;
      default:
        throw new Error(`OpenAI fallback niet geïmplementeerd voor taak: ${task.type}`);
    }
  }
  
  // Speciale methoden voor verschillende taken
  async codingAssistant(codePrompt: string, language: string = 'typescript'): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    const enhancedPrompt = `
Je bent een expert ${language} developer. Analyseer deze code en geef concrete, bruikbare feedback:

${codePrompt}

Geef antwoord in het Nederlands met:
1. Code review
2. Verbeteringsvoorstellen
3. Potentiële bugs
4. Best practices
`;
    
    return this.query(enhancedPrompt, {
      type: 'coding',
      priority: 'high',
      language
    });
  }
  
  async crmAnalysis(data: string, analysisType: string): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    const prompt = `
Analyseer deze CRM data voor ${analysisType}:

${data}

Geef een Nederlandse analyse met:
1. Belangrijkste inzichten
2. Aanbevelingen
3. Actie items
`;
    
    return this.query(prompt, {
      type: 'analysis',
      priority: 'medium',
      context: analysisType
    });
  }
  
  async planningAssistant(projectDetails: string): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    const prompt = `
Maak een ontwikkelingsplan voor dit project:

${projectDetails}

Geef een gestructureerd plan met:
1. Technische architectuur
2. Implementatie stappen
3. Tijdsinschattingen
4. Risico's en mitigaties
`;
    
    return this.query(prompt, {
      type: 'planning',
      priority: 'high'
    });
  }
  
  // Model status checker
  async checkModelStatus(): Promise<{[key: string]: boolean}> {
    const status: {[key: string]: boolean} = {};
    
    const modelKeys = Array.from(this.models.keys());
    for (const key of modelKeys) {
      const model = this.models.get(key)!;
      try {
        if (model.type === 'local') {
          await queryLMStudio('test', model.name);
          status[key] = true;
        } else {
          // Voor API modellen, check of de key beschikbaar is
          status[key] = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'dummy-key-for-lm-studio';
        }
      } catch (error) {
        status[key] = false;
      }
    }
    
    return status;
  }
} 