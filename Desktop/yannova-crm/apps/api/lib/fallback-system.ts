import { AIModel, AITask } from './ai-orchestrator';
import { queryLMStudio } from './lmstudio-service';
import { OpenAIService } from './openai-service';

export interface FallbackResult {
  response: string;
  model: string;
  provider: string;
  timestamp: string;
  fallbackLevel: number;
  originalError?: string;
}

export class FallbackSystem {
  private fallbackChain: string[] = [
    'mistral-code',    // <PERSON>aal (prioriteit 1)
    'deepseek-coder',  // <PERSON><PERSON> (prioriteit 2) 
    'llama-chat',      // <PERSON><PERSON> (prioriteit 3)
    'openai-gpt4',     // API (prioriteit 4)
    'openai-gpt3',     // API (prioriteit 5)
    'claude-3'         // API (prioriteit 6)
  ];

  async executeWithFallback(
    prompt: string, 
    task: AITask, 
    models: Map<string, AIModel>,
    primaryModel: AIModel
  ): Promise<FallbackResult> {
    
    let lastError = '';
    let fallbackLevel = 0;
    
    // Probeer eerst het primaire model
    try {
      const result = await this.executeModel(prompt, primaryModel, task);
      return {
        ...result,
        fallbackLevel: 0
      };
    } catch (error) {
      lastError = error.message;
      console.warn(`Primary model ${primaryModel.name} failed:`, error);
    }
    
    // Ga door de fallback chain
    for (const modelId of this.fallbackChain) {
      if (modelId === primaryModel.name) continue; // Skip primary model
      
      const model = models.get(modelId);
      if (!model || !model.capabilities.includes(task.type)) continue;
      
      fallbackLevel++;
      
      try {
        const result = await this.executeModel(prompt, model, task);
        return {
          ...result,
          fallbackLevel,
          originalError: lastError
        };
      } catch (error) {
        lastError = error.message;
        console.warn(`Fallback model ${model.name} failed:`, error);
        continue;
      }
    }
    
    // Als alles faalt, geef een standaard antwoord
    return {
      response: `Alle AI modellen zijn momenteel niet beschikbaar. Probeer het later opnieuw. Laatste fout: ${lastError}`,
      model: 'fallback-message',
      provider: 'System',
      timestamp: new Date().toISOString(),
      fallbackLevel: 999,
      originalError: lastError
    };
  }
  
  private async executeModel(prompt: string, model: AIModel, task: AITask): Promise<{
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  }> {
    if (model.type === 'local') {
      const response = await queryLMStudio(prompt, model.name);
      return {
        response,
        model: model.name,
        provider: 'LM Studio',
        timestamp: new Date().toISOString()
      };
    } else {
      // API models
      let response: string;
      
      switch (task.type) {
        case 'email':
          response = await OpenAIService.generateEmailReply(prompt, task.context);
          break;
        case 'analysis':
          const sentiment = await OpenAIService.analyzeSentiment(prompt);
          response = `Sentiment analyse: ${sentiment}`;
          break;
        default:
          throw new Error(`API fallback niet beschikbaar voor ${task.type}`);
      }
      
      return {
        response,
        model: model.name,
        provider: 'OpenAI',
        timestamp: new Date().toISOString()
      };
    }
  }
  
  // Health check voor alle modellen
  async checkAllModelsHealth(models: Map<string, AIModel>): Promise<{
    [key: string]: {
      status: 'online' | 'offline' | 'degraded';
      responseTime?: number;
      error?: string;
    }
  }> {
    const healthStatus: { [key: string]: any } = {};
    
    const modelEntries = Array.from(models.entries());
    for (const [modelId, model] of modelEntries) {
      const startTime = Date.now();
      
      try {
        if (model.type === 'local') {
          await queryLMStudio('health check', model.name);
          const responseTime = Date.now() - startTime;
          
          healthStatus[modelId] = {
            status: responseTime < 5000 ? 'online' : 'degraded',
            responseTime
          };
        } else {
          // Voor API modellen, check of keys beschikbaar zijn
          const hasKey = !!process.env.OPENAI_API_KEY && 
                        process.env.OPENAI_API_KEY !== 'dummy-key-for-lm-studio';
          
          healthStatus[modelId] = {
            status: hasKey ? 'online' : 'offline',
            responseTime: hasKey ? 100 : undefined,
            error: hasKey ? undefined : 'API key niet geconfigureerd'
          };
        }
      } catch (error) {
        healthStatus[modelId] = {
          status: 'offline',
          error: error.message
        };
      }
    }
    
    return healthStatus;
  }
} 