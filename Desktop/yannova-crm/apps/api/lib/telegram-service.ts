import TelegramBot from 'node-telegram-bot-api';
import { queryLMStudio } from './lmstudio-service';

export class TelegramService {
  private bot: TelegramBot;
  
  constructor() {
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN!, { polling: false });
    this.setupCommands();
  }
  
  private setupCommands() {
    // AI vraag commando
    this.bot.onText(/\/vraag (.+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const question = match?.[1];
      
      if (!question) {
        await this.bot.sendMessage(chatId, '❌ Geef een vraag mee: /vraag Hoe maak ik een bot?');
        return;
      }
      
      try {
        await this.bot.sendMessage(chatId, '🤖 Aan het denken...');
        const response = await queryLMStudio(question);
        await this.bot.sendMessage(chatId, `💡 **AI Antwoord:**\n\n${response}`, { parse_mode: 'Markdown' });
      } catch (error) {
        console.error('Telegram AI error:', error);
        await this.bot.sendMessage(chatId, '❌ Kon geen antwoord genereren. Probeer het later opnieuw.');
      }
    });
    
    // Help commando
    this.bot.onText(/\/help/, async (msg) => {
      const helpText = `
🤖 **Yannova CRM Bot Commands:**

/vraag [vraag] - Stel een vraag aan de AI
/help - Toon deze help
/status - Bot status

*Voorbeeld:*
/vraag Hoe maak ik een Telegram bot?
      `;
      
      await this.bot.sendMessage(msg.chat.id, helpText, { parse_mode: 'Markdown' });
    });
    
    // Status commando
    this.bot.onText(/\/status/, async (msg) => {
      await this.bot.sendMessage(msg.chat.id, '✅ Bot is online en verbonden met LM Studio!');
    });
  }
  
  async sendMessage(chatId: string, message: string) {
    return await this.bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown'
    });
  }
  
  async sendContactCard(chatId: string, contact: any) {
    const message = `
🧑‍💼 **${contact.name || contact.email}**
${contact.company ? `🏢 ${contact.company}` : ''}
📧 ${contact.email}
${contact.phone ? `📱 ${contact.phone}` : ''}
📊 Status: ${contact.status}
💬 Gesprekken: ${contact.conversationCount}
💰 Deals: ${contact.dealCount}
    `;
    
    return await this.sendMessage(chatId, message);
  }
}