// apps/api/lib/email-service.ts
import { ResendEmailService } from './email-service-resend';

export class EmailService {
  private static readonly FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';

  static async categorizeEmail(subject: string, content: string) {
    const categories = {
      QUOTE: ['prijs', 'offerte', 'kosten', 'budget'],
      BOT: ['bot', 'telegram', 'whatsapp', 'automatisering'],
      URGENT: ['spoed', 'urgent', 'direct', 'vandaag'],
      GENERAL: ['informatie', 'vraag', 'contact']
    };

    const lowerContent = (subject + ' ' + content).toLowerCase();
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(word => lowerContent.includes(word))) {
        return category;
      }
    }
    return 'GENERAL';
  }

  static async generateSmartResponse(category: string, content: string): Promise<string> {
    const templates = {
      QUOTE: `
        <h2>Bedankt voor je aanvraag!</h2>
        <p>Hoi,</p>
        <p>Ik help je graag met een passende offerte. Om je de beste prijs te kunnen geven, 
        zou je me kunnen vertellen:</p>
        <ul>
          <li>Wat is je exacte wens/doel?</li>
          <li>Wat is je gewenste timeline?</li>
          <li>Heb je een specifiek budget in gedachten?</li>
        </ul>
        <p>Dan maak ik direct een voorstel op maat! 🚀</p>
        <p>Vriendelijke groet,<br>Leon</p>
      `,
      BOT: `
        <h2>Leuk dat je interesse hebt in bots!</h2>
        <p>Hoi,</p>
        <p>Als bot-specialist help ik je graag. Ik bouw:</p>
        <ul>
          <li>🤖 Custom Telegram & WhatsApp bots</li>
          <li>📈 Trading & monitoring bots</li>
          <li>🔄 Workflow automation</li>
          <li>🧠 AI-powered assistenten</li>
        </ul>
        <p>Zullen we even videobellen om je wensen te bespreken?</p>
        <p>Vriendelijke groet,<br>Leon</p>
      `,
      URGENT: `
        <h2>Je bericht heeft prioriteit!</h2>
        <p>Hoi,</p>
        <p>Ik zie dat dit urgent is - je kunt me direct bereiken op:</p>
        <ul>
          <li>📱 Telefoon: +31 6 XX XX XX XX</li>
          <li>💬 Telegram: @leondev</li>
        </ul>
        <p>Ik reageer meestal binnen 30 minuten!</p>
        <p>Vriendelijke groet,<br>Leon</p>
      `,
      GENERAL: `
        <h2>Bedankt voor je bericht!</h2>
        <p>Hoi,</p>
        <p>Ik heb je bericht ontvangen en neem binnen 24 uur contact met je op.</p>
        <p>Voor snellere reactie kun je me ook bereiken via:</p>
        <ul>
          <li>💬 Telegram: @leondev</li>
          <li>🌐 Website: yannova.be</li>
        </ul>
        <p>Vriendelijke groet,<br>Leon</p>
      `
    };

    return templates[category] || templates.GENERAL;
  }

  static async handleIncomingEmail(from: string, subject: string, content: string) {
    try {
      // 1. Categoriseer de email
      const category = await this.categorizeEmail(subject, content);
      
      // 2. Genereer slim antwoord
      const response = await this.generateSmartResponse(category, content);
      
      // 3. Verstuur antwoord via Zoho
      await this.sendEmail(from, `Re: ${subject}`, response);
      
      // 4. Sla op in CRM als het belangrijk is
      if (category === 'QUOTE' || category === 'URGENT') {
        await this.createCRMTask(from, subject, category);
      }

      return true;
    } catch (error) {
      console.error('Email handling error:', error);
      return false;
    }
  }

  static async sendEmail(to: string, subject: string, content: string): Promise<boolean> {
    // Check if Resend is configured
    if (process.env.RESEND_API_KEY) {
      return await ResendEmailService.sendEmail(to, subject, content);
    }
    
    // Als Resend niet geconfigureerd is, return false
    console.log('Geen email provider geconfigureerd. Voeg RESEND_API_KEY toe aan .env');
    return false;
  }

  private static async createCRMTask(email: string, subject: string, category: string) {
    // Implementeer CRM integratie
    console.log(`Creating ${category} task for ${email}`);
  }

  static async generateAIResponse(emailContent: string, subject: string): Promise<string> {
    // Check if OpenAI is configured
    if (process.env.OPENAI_API_KEY) {
      const { OpenAIService } = await import('./openai-service');
      return await OpenAIService.generateEmailReply(emailContent, subject);
    }

    // Fallback to Cloudflare AI if configured
    if (process.env.CLOUDFLARE_AI_TOKEN) {
      try {
        const response = await fetch('https://api.cloudflare.com/client/v4/accounts/YOUR_ACCOUNT_ID/ai/run/@cf/meta/llama-3-8b-instruct', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.CLOUDFLARE_AI_TOKEN}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            messages: [{ role: 'user', content: `Genereer een professionele Nederlandse email response voor:\nOnderwerp: ${subject}\nInhoud: ${emailContent}` }]
          })
        });

        const aiResult = await response.json();
        return aiResult.result?.response || this.getTemplateResponse(emailContent);
      } catch (error) {
        console.error('AI response error:', error);
      }
    }

    // Fallback to template response
    return this.getTemplateResponse(emailContent);
  }

  private static getTemplateResponse(emailContent: string): string {
    // Fallback template responses
    const lowerContent = emailContent.toLowerCase();
    
    if (lowerContent.includes('prijs') || lowerContent.includes('kosten')) {
      return `
        <p>Hoi,</p>
        <p>Bedankt voor je interesse! Ik stuur je graag een gepersonaliseerde offerte.</p>
        <p>Kun je me wat meer vertellen over:</p>
        <ul>
          <li>Wat voor project je in gedachten hebt</li>
          <li>Je budget range</li>
          <li>Gewenste timeline</li>
        </ul>
        <p>Dan kan ik je een scherpe prijs geven!</p>
        <p>Groet,<br>Leon</p>
      `;
    }

    if (lowerContent.includes('bot') || lowerContent.includes('telegram') || lowerContent.includes('whatsapp')) {
      return `
        <p>Hoi,</p>
        <p>Leuk dat je interesse hebt in bot development! Dat is precies mijn specialiteit.</p>
        <p>Ik bouw:</p>
        <ul>
          <li>Telegram & WhatsApp bots</li>
          <li>Trading bots</li>
          <li>CRM integraties</li>
          <li>AI-powered automations</li>
        </ul>
        <p>Zullen we even bellen om je ideeën te bespreken?</p>
        <p>Groet,<br>Leon</p>
      `;
    }

    return `
      <p>Hoi,</p>
      <p>Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.</p>
      <p>Voor dringende zaken kun je me bellen op +31 6 12345678</p>
      <p>Groet,<br>Leon</p>
    `;
  }

  static async sendEmailViaZoho(to: string, subject: string, content: string): Promise<boolean> {
    // Gebruik dezelfde sendEmail functie
    return await this.sendEmail(to, subject, content);
  }

  static async sendAutoReply(to: string, originalSubject: string): Promise<boolean> {
    const subject = `Re: ${originalSubject}`;
    const content = `
      <h2>Bedankt voor je bericht!</h2>
      <p>Hoi,</p>
      <p>We hebben je bericht ontvangen en nemen zo snel mogelijk contact met je op.</p>
      <p>Voor dringende zaken kun je bellen naar <strong>+31 6 12345678</strong></p>
      <br>
      <p>Met vriendelijke groet,<br>
      Team Yannova</p>
    `;

    return await this.sendEmail(to, subject, content);
  }

  static async sendIntelligentReply(to: string, originalSubject: string, emailContent: string): Promise<boolean> {
    const subject = `Re: ${originalSubject}`;
    const aiResponse = await this.generateAIResponse(emailContent, originalSubject);
    
    return await this.sendEmail(to, subject, aiResponse);
  }

  static async analyzeSentiment(text: string): Promise<number> {
    // Use OpenAI if available
    if (process.env.OPENAI_API_KEY) {
      try {
        const { OpenAIService } = await import('./openai-service');
        return await OpenAIService.analyzeSentiment(text);
      } catch (error) {
        console.error('OpenAI sentiment error:', error);
      }
    }

    // Fallback to simple sentiment analysis
    const positiveWords = ['dank', 'geweldig', 'perfect', 'tevreden', 'goed', 'prima', 'uitstekend']
    const negativeWords = ['probleem', 'slecht', 'niet', 'fout', 'irritant', 'teleurgesteld', 'boos']
    
    const words = text.toLowerCase().split(' ')
    let score = 0
    
    words.forEach(word => {
      if (positiveWords.includes(word)) score += 0.2
      if (negativeWords.includes(word)) score -= 0.2
    })
    
    return Math.max(-1, Math.min(1, score))
  }
  
  static extractContactInfo(emailContent: string) {
    const phoneRegex = /(\+31|0)[0-9\s\-]{8,}/g
    const companyRegex = /(?:van|bij|voor) ([A-Z][a-zA-Z\s]+)/g
    
    return {
      phone: emailContent.match(phoneRegex)?.[0],
      company: emailContent.match(companyRegex)?.[1]
    }
  }
}