import { render } from '@react-email/render';
import WelcomeEmail from '../emails/templates/welcome-email';
import QuoteEmail from '../emails/templates/quote-email';
import BotNotification from '../emails/templates/bot-notification';
import { ResendEmailService } from './email-service-resend';

export class EmailTemplates {
  // Welcome Email
  static async sendWelcomeEmail(to: string, data: { name: string; email: string }) {
    const html = render(WelcomeEmail(data));
    
    return await ResendEmailService.sendEmail(
      to,
      `Welkom bij Yannova CRM, ${data.name}!`,
      html
    );
  }

  // Quote Email
  static async sendQuoteEmail(
    to: string,
    data: {
      clientName: string;
      projectName: string;
      items: Array<{ description: string; price: number; quantity: number }>;
      validUntil: string;
      notes?: string;
    }
  ) {
    const html = render(QuoteEmail(data));
    
    return await ResendEmailService.sendEmail(
      to,
      `Offerte voor ${data.projectName}`,
      html
    );
  }

  // Bot Notification
  static async sendBotNotification(
    to: string,
    data: {
      botName: string;
      eventType: 'message' | 'error' | 'alert' | 'update';
      userName?: string;
      message: string;
      timestamp: string;
      metadata?: Record<string, any>;
    }
  ) {
    const html = render(BotNotification(data));
    const icon = data.eventType === 'error' ? '❌' : 
                 data.eventType === 'alert' ? '⚠️' : 
                 data.eventType === 'update' ? '🔄' : '💬';
    
    return await ResendEmailService.sendEmail(
      to,
      `${icon} ${data.botName} - ${data.eventType}`,
      html
    );
  }

  // Generic template sender
  static async sendTemplate(to: string, subject: string, template: React.ReactElement) {
    const html = render(template);
    return await ResendEmailService.sendEmail(to, subject, html);
  }

  // Bulk email with template
  static async sendBulkTemplate(
    recipients: string[],
    subject: string,
    template: React.ReactElement
  ) {
    const html = render(template);
    return await ResendEmailService.sendBulkEmails(recipients, subject, html);
  }
}

// Export template components for custom usage
export { WelcomeEmail, QuoteEmail, BotNotification }; 