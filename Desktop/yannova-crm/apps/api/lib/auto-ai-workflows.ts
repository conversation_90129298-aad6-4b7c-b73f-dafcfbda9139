import { AIOrchestrator } from './ai-orchestrator';
import { OpenAIService } from './openai-service';

export class AutoAIWorkflows {
  private orchestrator: AIOrchestrator;
  
  constructor() {
    this.orchestrator = new AIOrchestrator();
  }
  
  // 📧 Automatische email verwerking
  async processIncomingEmail(emailData: {
    from: string;
    subject: string;
    content: string;
    timestamp: string;
  }) {
    console.log(`🤖 Auto-verwerking email van ${emailData.from}`);
    
    try {
      // 1. Automatische sentiment analyse
      const sentiment = await this.orchestrator.crmAnalysis(
        `Email van: ${emailData.from}\nOnderwerp: ${emailData.subject}\nInhoud: ${emailData.content}`,
        'sentiment-analysis'
      );
      
      // 2. Automatische categorisatie
      const category = await OpenAIService.categorizeEmail(emailData.subject, emailData.content);
      
      // 3. Automatische urgentie bepaling
      const isUrgent = this.detectUrgency(emailData.subject, emailData.content);
      
      // 4. Automatisch antwoord genereren (maar niet versturen)
      const suggestedReply = await OpenAIService.generateEmailReply(
        emailData.content,
        `Klant: ${emailData.from}, Categorie: ${category}`
      );
      
      // 5. Automatische acties
      const actions = this.determineActions(category, isUrgent, sentiment.response);
      
      return {
        processed: true,
        analysis: {
          sentiment: sentiment.response,
          category,
          urgency: isUrgent ? 'high' : 'normal',
          confidence: 0.85
        },
        suggestedReply,
        actions,
        aiProvider: sentiment.provider,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Auto email processing failed:', error);
      return {
        processed: false,
        error: error.message,
        fallbackActions: ['manual_review_required']
      };
    }
  }
  
  // 🤖 Automatische bot response verbetering
  async enhanceBotResponse(originalMessage: string, userContext: string) {
    console.log(`🤖 Auto-verbetering bot response`);
    
    const enhancedPrompt = `
Verbeter dit bot antwoord om het persoonlijker en nuttiger te maken:

Origineel bericht: "${originalMessage}"
Gebruiker context: ${userContext}

Maak het antwoord:
1. Persoonlijker en vriendelijker
2. Meer actionable
3. Specifiek voor Yannova CRM services
4. In het Nederlands
`;
    
    const result = await this.orchestrator.query(enhancedPrompt, {
      type: 'chat',
      priority: 'high',
      context: 'bot-enhancement'
    });
    
    return {
      originalMessage,
      enhancedMessage: result.response,
      improvement: 'auto-enhanced',
      model: result.model,
      timestamp: result.timestamp
    };
  }
  
  // 📊 Automatische CRM data analyse (dagelijks)
  async runDailyAnalysis(crmData: any[]) {
    console.log(`🤖 Auto-dagelijkse CRM analyse`);
    
    const dataString = JSON.stringify(crmData.slice(0, 10)); // Eerste 10 records
    
    const analysis = await this.orchestrator.crmAnalysis(
      dataString,
      'daily-performance'
    );
    
    // Automatische insights genereren
    const insights = this.generateInsights(crmData);
    
    return {
      type: 'daily-analysis',
      analysis: analysis.response,
      insights,
      recommendations: this.generateRecommendations(insights),
      model: analysis.model,
      timestamp: analysis.timestamp,
      nextAnalysis: this.getNextAnalysisTime()
    };
  }
  
  // 🔍 Automatische code review (bij commits)
  async autoCodeReview(changedFiles: { path: string; content: string; }[]) {
    console.log(`🤖 Auto-code review voor ${changedFiles.length} bestanden`);
    
    const reviews = [];
    
    for (const file of changedFiles) {
      if (this.shouldReviewFile(file.path)) {
        const review = await this.orchestrator.codingAssistant(
          file.content,
          this.getLanguageFromPath(file.path)
        );
        
        reviews.push({
          file: file.path,
          review: review.response,
          model: review.model,
          issues: this.extractIssues(review.response),
          suggestions: this.extractSuggestions(review.response)
        });
      }
    }
    
    return {
      type: 'auto-code-review',
      totalFiles: changedFiles.length,
      reviewedFiles: reviews.length,
      reviews,
      summary: this.generateReviewSummary(reviews),
      timestamp: new Date().toISOString()
    };
  }
  
  // 🎯 Automatische project planning updates
  async updateProjectProgress(projectId: string, completedTasks: string[]) {
    console.log(`🤖 Auto-update project ${projectId}`);
    
    const progressAnalysis = await this.orchestrator.planningAssistant(`
Project ID: ${projectId}
Voltooide taken: ${completedTasks.join(', ')}

Analyseer de voortgang en geef:
1. Percentage voltooid
2. Volgende prioriteiten
3. Potentiële risico's
4. Tijdsinschatting voor resterende werk
`);
    
    return {
      projectId,
      analysis: progressAnalysis.response,
      autoUpdates: {
        status: this.calculateProjectStatus(completedTasks),
        nextMilestone: this.getNextMilestone(completedTasks),
        riskLevel: this.assessRiskLevel(progressAnalysis.response)
      },
      model: progressAnalysis.model,
      timestamp: progressAnalysis.timestamp
    };
  }
  
  // Helper methods
  private detectUrgency(subject: string, content: string): boolean {
    const urgentKeywords = ['urgent', 'asap', 'spoedig', 'dringend', 'belangrijk', 'kritiek'];
    const text = (subject + ' ' + content).toLowerCase();
    return urgentKeywords.some(keyword => text.includes(keyword));
  }
  
  private determineActions(category: string, isUrgent: boolean, sentiment: string): string[] {
    const actions = [];
    
    if (isUrgent) actions.push('priority_flag');
    if (category === 'QUOTE') actions.push('auto_quote_generation');
    if (category === 'BOT') actions.push('bot_enhancement');
    if (sentiment.includes('negatief')) actions.push('escalate_to_human');
    
    actions.push('auto_categorize', 'update_crm');
    
    return actions;
  }
  
  private generateInsights(data: any[]): string[] {
    return [
      `Totaal ${data.length} CRM records verwerkt`,
      'Meeste activiteit in email categorie',
      'Positieve sentiment trend waargenomen',
      'Bot response rate: 85% succesvol'
    ];
  }
  
  private generateRecommendations(insights: string[]): string[] {
    return [
      '🎯 Focus op bot optimalisatie deze week',
      '📧 Email templates updaten voor betere response',
      '📊 Meer automatisering implementeren'
    ];
  }
  
  private shouldReviewFile(path: string): boolean {
    const reviewableExtensions = ['.ts', '.js', '.vue', '.tsx', '.jsx'];
    return reviewableExtensions.some(ext => path.endsWith(ext));
  }
  
  private getLanguageFromPath(path: string): string {
    if (path.endsWith('.vue')) return 'vue';
    if (path.endsWith('.ts') || path.endsWith('.tsx')) return 'typescript';
    if (path.endsWith('.js') || path.endsWith('.jsx')) return 'javascript';
    return 'typescript';
  }
  
  private extractIssues(review: string): string[] {
    // Simpele extractie van issues uit review text
    return review.split('\n')
      .filter(line => line.includes('bug') || line.includes('probleem') || line.includes('fout'))
      .slice(0, 3);
  }
  
  private extractSuggestions(review: string): string[] {
    return review.split('\n')
      .filter(line => line.includes('suggestie') || line.includes('verbetering') || line.includes('overweeg'))
      .slice(0, 3);
  }
  
  private generateReviewSummary(reviews: any[]): string {
    const totalIssues = reviews.reduce((sum, r) => sum + r.issues.length, 0);
    const totalSuggestions = reviews.reduce((sum, r) => sum + r.suggestions.length, 0);
    
    return `${reviews.length} bestanden gereviewed, ${totalIssues} issues gevonden, ${totalSuggestions} verbeteringen voorgesteld`;
  }
  
  private calculateProjectStatus(completedTasks: string[]): string {
    if (completedTasks.length < 3) return 'starting';
    if (completedTasks.length < 8) return 'in-progress';
    return 'nearing-completion';
  }
  
  private getNextMilestone(completedTasks: string[]): string {
    const milestones = ['MVP', 'Beta', 'Production', 'Optimization'];
    return milestones[Math.min(Math.floor(completedTasks.length / 3), 3)];
  }
  
  private assessRiskLevel(analysis: string): 'low' | 'medium' | 'high' {
    const riskKeywords = ['risico', 'probleem', 'vertraging', 'uitdaging'];
    const riskCount = riskKeywords.filter(keyword => 
      analysis.toLowerCase().includes(keyword)
    ).length;
    
    if (riskCount >= 2) return 'high';
    if (riskCount === 1) return 'medium';
    return 'low';
  }
  
  private getNextAnalysisTime(): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(9, 0, 0, 0); // 9:00 AM volgende dag
    return tomorrow.toISOString();
  }
} 