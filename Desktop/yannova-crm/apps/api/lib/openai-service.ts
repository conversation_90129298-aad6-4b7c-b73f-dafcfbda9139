import OpenAI from 'openai';
import { queryLMStudio } from './lmstudio-service';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Helper functie om te bepalen of LM Studio gebruikt moet worden
const shouldUseLMStudio = () => process.env.USE_LM_STUDIO === 'true';

export class OpenAIService {
  // Generate smart email reply
  static async generateEmailReply(
    originalEmail: string,
    context?: string
  ): Promise<string> {
    try {
      const systemPrompt = `<PERSON> <PERSON>, een vriendelijke Nederlandse AI developer en bot specialist. 
            Je antwoordt professioneel maar toegankelijk in het Nederlands.
            Specialisaties: Telegram/WhatsApp bots, trading bots, CRM-systemen, apps.
            Houd antwoorden kort en actionable.`;
      
      const userPrompt = `Schrijf een antwoord op deze email:\n\n${originalEmail}\n\n${context ? `Context: ${context}` : ''}`;

      if (shouldUseLMStudio()) {
        const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;
        return await queryLMStudio(fullPrompt);
      }

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
      });

      return completion.choices[0].message.content || 'Kon geen antwoord genereren.';
    } catch (error) {
      console.error('AI error:', error);
      return 'Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.';
    }
  }

  // Analyze email sentiment
  static async analyzeSentiment(text: string): Promise<number> {
    try {
      if (shouldUseLMStudio()) {
        const prompt = `Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\n\nTekst: ${text}`;
        const result = await queryLMStudio(prompt);
        const sentiment = parseFloat(result);
        return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));
      }

      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief)."
          },
          {
            role: "user",
            content: text
          }
        ],
        temperature: 0,
        max_tokens: 10,
      });

      const sentiment = parseFloat(completion.choices[0].message.content || '0');
      return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      return 0;
    }
  }

  // Categorize email
  static async categorizeEmail(subject: string, content: string): Promise<string> {
    try {
      const prompt = `Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\n\nOnderwerp: ${subject}\n\nInhoud: ${content}`;
      
      if (shouldUseLMStudio()) {
        const result = await queryLMStudio(prompt);
        const category = result.trim().toUpperCase();
        return ['QUOTE', 'BOT', 'URGENT', 'GENERAL'].includes(category) ? category : 'GENERAL';
      }

      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie."
          },
          {
            role: "user",
            content: `Onderwerp: ${subject}\n\nInhoud: ${content}`
          }
        ],
        temperature: 0,
        max_tokens: 10,
      });

      const category = completion.choices[0].message.content?.trim().toUpperCase();
      return ['QUOTE', 'BOT', 'URGENT', 'GENERAL'].includes(category || '') ? category! : 'GENERAL';
    } catch (error) {
      console.error('Categorization error:', error);
      return 'GENERAL';
    }
  }

  // Generate email subject suggestions
  static async generateSubjectSuggestions(content: string): Promise<string[]> {
    try {
      const prompt = `Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\n\nInhoud: ${content}`;
      
      if (shouldUseLMStudio()) {
        const result = await queryLMStudio(prompt);
        return result.split('\n').filter(s => s.trim()) || [];
      }

      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels."
          },
          {
            role: "user",
            content: content
          }
        ],
        temperature: 0.8,
        max_tokens: 100,
      });

      return completion.choices[0].message.content?.split('\n').filter(s => s.trim()) || [];
    } catch (error) {
      console.error('Subject generation error:', error);
      return ['Uw aanvraag', 'Informatie verzoek', 'Contact'];
    }
  }

  // Extract key information from email
  static async extractKeyInfo(emailContent: string): Promise<{
    name?: string;
    company?: string;
    phone?: string;
    intent?: string;
    urgency?: 'low' | 'medium' | 'high';
  }> {
    try {
      const prompt = `Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\n\nEmail: ${emailContent}`;
      
      if (shouldUseLMStudio()) {
        const result = await queryLMStudio(prompt);
        try {
          return JSON.parse(result);
        } catch {
          return {};
        }
      }

      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high)."
          },
          {
            role: "user",
            content: emailContent
          }
        ],
        temperature: 0,
        max_tokens: 200,
      });

      return JSON.parse(completion.choices[0].message.content || '{}');
    } catch (error) {
      console.error('Info extraction error:', error);
      return {};
    }
  }
} 