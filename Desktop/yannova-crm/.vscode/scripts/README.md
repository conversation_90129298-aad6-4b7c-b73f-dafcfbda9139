# Leon's Cursor/VS Code Setup Scripts

Deze scripts zorgen ervoor dat je Cursor AI terminal commands goed werken in alle projecten.

## 🎯 Wat is er opgelost?

- **Terminal commando's** zoals `Ctrl+K` werken nu correct
- **Cursor AI features** worden niet meer geblokkeerd door shell
- **Automatische setup** voor nieuwe projecten

## 🚀 Gebruik

### 1. Global Setup (Eenmalig)
```bash
# Maak executable
chmod +x .vscode/scripts/setup-global-cursor.sh

# Run global setup
./.vscode/scripts/setup-global-cursor.sh
```

### 2. Nieuwe Projecten Setup
```bash
# In je nieuwe project directory:
node /path/to/yannova-crm/.vscode/scripts/setup-new-project.js

# Of kopieer het script naar je nieuwe project:
cp .vscode/scripts/setup-new-project.js /path/to/new-project/
cd /path/to/new-project/
node setup-new-project.js
```

### 3. Quick Template Command
Voeg toe aan je `~/.zshrc` of `~/.bashrc`:
```bash
# <PERSON>'s project setup alias
alias setup-cursor="node ~/Desktop/yannova-crm/.vscode/scripts/setup-new-project.js"
```

Dan kun je in elke nieuwe project folder doen:
```bash
setup-cursor
```

## 🔧 Wat doen de settings?

### Terminal Commands to Skip Shell:
- `aichat.newchataction` 
- `cursorai.action.acceptAndRunGenerateInTerminal`
- `cursorai.action.cancelGenerateInTerminal`
- `cursorai.action.generateInTerminal`
- `cursorai.action.hideGenerateInTerminal`
- `cursorai.action.rejectGenerateInTerminal`

### Extra Features:
- Terminal chord support (Ctrl+K combinations)
- Copy on selection
- Optimized cursor & GPU acceleration
- JetBrains Mono font
- Chat/AI editor improvements

## 🎛️ Troubleshooting

Als commands nog steeds niet werken:
1. Herstart Cursor completeet
2. Check of settings correct geladen zijn: `Cmd+,` → zoek "terminal commands"
3. Run global setup opnieuw

## 🔄 Voor je andere projecten

Dit script automatiseert wat je handmatig deed. Nu gewoon:
```bash
cd new-telegram-bot-project
setup-cursor
```

En je hebt meteen dezelfde optimale setup! 🚀
