#!/usr/bin/env node

/**
 * <PERSON>'s VS Code/Cursor Setup Script
 * Kopieert optimale settings naar nieuwe projecten
 */

const fs = require('fs');
const path = require('path');

const SETTINGS_TEMPLATE = {
  // Terminal instellingen voor Cursor AI
  "terminal.integrated.commandsToSkipShell": [
    "aichat.newchataction",
    "cursorai.action.acceptAndRunGenerateInTerminal",
    "cursorai.action.cancelGenerateInTerminal", 
    "cursorai.action.generateInTerminal",
    "cursorai.action.hideGenerateInTerminal",
    "cursorai.action.rejectGenerateInTerminal"
  ],
  "terminal.integrated.allowChords": true,
  "terminal.integrated.sendKeybindingsToShell": false,
  
  // Terminal optimalisaties
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.enableMultiLinePasteWarning": false,
  "terminal.integrated.copyOnSelection": true,
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.cursorStyle": "line",
  "terminal.integrated.rightClickBehavior": "default",
  "terminal.integrated.gpuAcceleration": "on",
  
  // Editor basis
  "editor.fontFamily": "JetBrains Mono, Menlo, Monaco, 'Courier New', monospace",
  "editor.fontSize": 14,
  "editor.lineHeight": 1.5,
  "editor.cursorBlinking": "phase",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.minimap.enabled": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.smoothScrolling": true,
  
  // Chat optimalisaties
  "chat.editor.fontSize": 14,
  "chat.editor.fontFamily": "JetBrains Mono",
  "chat.editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  
  // Quick commands
  "workbench.commandPalette.preserveInput": true,
  "workbench.quickOpen.preserveInput": true
};

function setupProject(projectPath = process.cwd()) {
  const vscodeDir = path.join(projectPath, '.vscode');
  const settingsFile = path.join(vscodeDir, 'settings.json');
  
  console.log(`🚀 Setting up Cursor/VS Code voor: ${projectPath}`);
  
  // Maak .vscode directory als die niet bestaat
  if (!fs.existsSync(vscodeDir)) {
    fs.mkdirSync(vscodeDir, { recursive: true });
    console.log('✅ .vscode directory aangemaakt');
  }
  
  // Merge met bestaande settings als ze er zijn
  let finalSettings = { ...SETTINGS_TEMPLATE };
  
  if (fs.existsSync(settingsFile)) {
    try {
      const existingSettings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
      finalSettings = { ...existingSettings, ...SETTINGS_TEMPLATE };
      console.log('🔄 Bestaande settings gemerged');
    } catch (error) {
      console.log('⚠️  Kon bestaande settings niet lezen, nieuwe aangemaakt');
    }
  }
  
  // Schrijf settings
  fs.writeFileSync(settingsFile, JSON.stringify(finalSettings, null, 2));
  console.log('✅ settings.json bijgewerkt met Cursor AI terminal support');
  
  // Maak launch.json voor debugging
  const launchFile = path.join(vscodeDir, 'launch.json');
  if (!fs.existsSync(launchFile)) {
    const launchConfig = {
      "version": "0.2.0",
      "configurations": [
        {
          "name": "Debug Node.js",
          "type": "node",
          "request": "launch",
          "program": "${workspaceFolder}/index.js",
          "env": {
            "NODE_ENV": "development"
          }
        }
      ]
    };
    fs.writeFileSync(launchFile, JSON.stringify(launchConfig, null, 2));
    console.log('✅ launch.json aangemaakt voor debugging');
  }
  
  console.log('🎉 Project setup compleet! Herstart Cursor voor de beste ervaring.');
}

// CLI gebruik
if (require.main === module) {
  const targetPath = process.argv[2] || process.cwd();
  setupProject(targetPath);
}

module.exports = { setupProject, SETTINGS_TEMPLATE };
