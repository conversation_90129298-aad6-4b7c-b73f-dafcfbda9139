#!/bin/bash

# <PERSON><PERSON>uren voor output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Project initialisatie start...${NC}"

# Maak .vscode directory als die niet bestaat
mkdir -p .vscode

# Kopieer workspace template
cp "$(dirname "$0")/../project-templates/default.code-workspace" ./.vscode/settings.json

# Installeer aanbevolen extensies
echo -e "${BLUE}📦 Installeren van aanbevolen extensies...${NC}"
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension prisma.prisma
code --install-extension vue.volar
code --install-extension bradlc.vscode-tailwindcss

# Setup Git hooks
if [ -d ".git" ]; then
  echo -e "${BLUE}🔧 Git hooks configureren...${NC}"
  npx husky install || true
  npx husky add .husky/pre-commit "npm run lint"
fi

# Project specifieke setup
if [ -f "package.json" ]; then
  echo -e "${BLUE}📦 Node modules installeren...${NC}"
  npm install
fi

echo -e "${GREEN}✅ Project setup compleet!${NC}"
echo -e "${BLUE}💡 Tips:${NC}"
echo "1. Run 'npm run dev' om te starten"
echo "2. Check .vscode/settings.json voor project configuratie"
echo "3. Gebruik Cmd+Shift+P en type 'task' voor beschikbare taken" 