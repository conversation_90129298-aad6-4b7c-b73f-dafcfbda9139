#!/bin/bash

# <PERSON>'s Global Cursor Setup Script
# Instelt globale Cursor settings

echo "🚀 Setting up global Cursor settings..."

# Backup bestaande settings
CURSOR_SETTINGS_DIR="$HOME/Library/Application Support/Cursor/User"
BACKUP_DIR="$HOME/.cursor-backup-$(date +%Y%m%d-%H%M%S)"

if [ -d "$CURSOR_SETTINGS_DIR" ]; then
    echo "📦 Backup maken van bestaande settings naar: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    cp -r "$CURSOR_SETTINGS_DIR"/* "$BACKUP_DIR/" 2>/dev/null || true
fi

# Global settings.json
cat > "$CURSOR_SETTINGS_DIR/settings.json" << 'EOF'
{
  "terminal.integrated.commandsToSkipShell": [
    "aichat.newchataction",
    "cursorai.action.acceptAndRunGenerateInTerminal",
    "cursorai.action.cancelGenerateInTerminal",
    "cursorai.action.generateInTerminal",
    "cursorai.action.hideGenerateInTerminal",
    "cursorai.action.rejectGenerateInTerminal"
  ],
  "terminal.integrated.allowChords": true,
  "terminal.integrated.sendKeybindingsToShell": false,
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.enableMultiLinePasteWarning": false,
  "terminal.integrated.copyOnSelection": true,
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.gpuAcceleration": "on",
  
  "editor.fontFamily": "JetBrains Mono, Menlo, Monaco, 'Courier New', monospace",
  "editor.fontSize": 14,
  "editor.lineHeight": 1.5,
  "editor.cursorBlinking": "phase",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.minimap.enabled": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.smoothScrolling": true,
  
  "workbench.colorTheme": "GitHub Dark Default",
  "workbench.commandPalette.preserveInput": true,
  "workbench.quickOpen.preserveInput": true,
  
  "chat.editor.fontSize": 14,
  "chat.editor.fontFamily": "JetBrains Mono",
  "chat.editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  }
}
EOF

echo "✅ Global Cursor settings installed!"
echo "🔄 Herstart Cursor om de settings te activeren"
echo "📍 Settings location: $CURSOR_SETTINGS_DIR/settings.json"
echo "💾 Backup location: $BACKUP_DIR"
