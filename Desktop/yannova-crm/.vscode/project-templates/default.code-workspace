{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    // Project Structure
    "files.exclude": {
      "**/node_modules": true,
      "**/.git": true,
      "**/.DS_Store": true,
      "**/dist": false,
      "**/coverage": true
    },
    "search.exclude": {
      "**/node_modules": true,
      "**/dist": true
    },
    
    // Editor Settings
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": true,
      "source.organizeImports": true
    },
    
    // TypeScript Settings
    "typescript.suggest.paths": true,
    "typescript.updateImportsOnFileMove.enabled": "always",
    "typescript.preferences.importModuleSpecifier": "relative",
    
    // Git Settings
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    
    // Cursor AI Settings
    "cursor.showChatCompletions": true,
    "cursor.preferredLanguage": "nl-NL",
    "cursor.features": {
      "chat": true,
      "copilot": true,
      "snippets": true
    },
    
    // Project Specific Rules
    "eslint.validate": [
      "javascript",
      "typescript",
      "javascriptreact",
      "typescriptreact"
    ],
    "eslint.workingDirectories": [
      "./apps/api",
      "./apps/mobile",
      "./apps/workers"
    ],
    
    // Custom Templates
    "files.associations": {
      "*.ts": "typescript",
      "*.tsx": "typescriptreact",
      "*.vue": "vue",
      "*.prisma": "prisma"
    },
    
    // Recommended Extensions
    "recommendations": [
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "prisma.prisma",
      "vue.volar",
      "bradlc.vscode-tailwindcss"
    ]
  },
  "extensions": {
    "recommendations": [
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "prisma.prisma",
      "vue.volar",
      "bradlc.vscode-tailwindcss"
    ]
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Setup New Project",
        "type": "shell",
        "command": "npm install && npm run dev",
        "presentation": {
          "reveal": "always",
          "panel": "new"
        }
      }
    ]
  }
} 