{"lmstudio": {"serverURL": "http://localhost:1234", "host": "0.0.0.0", "cors": true, "maxLoadedModels": 3}, "localai": {"defaultModel": "codellama-7b-kstack", "editorToolbar": ["completeSelection", "chatWithModel"], "requestTimeout": 60000, "maxTokens": 1024, "models": [{"id": "deepseek-coder-6.7b-instruct", "name": "DeepSeek Coder", "description": "Model voor modulair en async Python-codegeneratie"}, {"id": "mistral-7b-instruct-v0.3-abliterated", "name": "mistral-7b-instruct-v0.3-abliterated", "description": "General-purpose instructiemodel voor chat en uitleg"}, {"id": "deepseek-r1-8b-mikrotik-distilled", "name": "DeepSeek R1 (8B)", "description": "Gespecialiseerd model voor Mikrotik-configuraties"}]}}