# 🚀 API Setup Guide - Stap voor Stap

## Stap 1: Maak een .env file

```bash
# In de root directory van het project
touch .env
```

Kopieer deze basis configuratie:

```env
# Database (lokaal)
DATABASE_URL="file:./dev.db"

# Email
RESEND_API_KEY=""
FROM_EMAIL="<EMAIL>"
```

## Stap 2: Resend.com Setup (Email)

### A. Account aanmaken
1. Ga naar https://resend.com/signup
2. Maak een gratis account (10.000 emails/maand gratis)
3. Bevestig je email

### B. API Key genereren
1. Dashboard → API Keys
2. Klik "Create API Key"
3. Geef het een naam: "Yannova CRM"
4. Kopieer de key (begint met `re_`)
5. Plak in .env: `RESEND_API_KEY="re_jouw_key"`

### C. Domein toevoegen
1. Dashboard → Domains
2. "Add Domain" → yannova.be
3. Voeg deze DNS records toe bij je provider:

```
Type: TXT
Name: resend._domainkey
Value: [krijg je van Resend]

Type: TXT  
Name: @
Value: v=spf1 include:amazonses.com ~all

Type: MX (voor email ontvangen)
Priority: 10
Value: feedback-smtp.eu-west-1.amazonses.com
```

## Stap 3: Telegram Bot Setup

### A. Bot aanmaken
1. Open Telegram
2. Zoek naar @BotFather
3. Stuur: `/newbot`
4. Kies een naam: "Yannova CRM Bot"
5. Kies username: `yannova_crm_bot`
6. Kopieer de token

### B. Token toevoegen
```env
TELEGRAM_BOT_TOKEN="**********:ABCdefGHIjklMNOpqrsTUVwxyz"
```

### C. Webhook instellen (later)
```bash
curl -X POST "https://api.telegram.org/bot{TOKEN}/setWebhook" \
  -d "url=https://jouw-app.com/api/telegram/webhook"
```

## Stap 4: WhatsApp Setup (Twilio)

### A. Twilio account
1. Ga naar https://www.twilio.com/try-twilio
2. Maak gratis account
3. Bevestig telefoonnummer

### B. WhatsApp Sandbox activeren
1. Console → Messaging → Try it out → WhatsApp
2. Volg instructies om sandbox te joinen
3. Noteer het WhatsApp nummer

### C. Credentials toevoegen
```env
TWILIO_ACCOUNT_SID="ACxxxxx"
TWILIO_AUTH_TOKEN="xxxxx"
TWILIO_WHATSAPP_NUMBER="whatsapp:+***********"
```

## Stap 5: AI Setup (Optioneel)

### Optie A: OpenAI
1. https://platform.openai.com/api-keys
2. "Create new secret key"
3. Toevoegen aan .env:
```env
OPENAI_API_KEY="sk-xxxxx"
```

### Optie B: Cloudflare AI (Gratis)
1. https://dash.cloudflare.com/
2. Workers & Pages → AI
3. Get API Token
```env
CLOUDFLARE_ACCOUNT_ID="xxxxx"
CLOUDFLARE_AI_TOKEN="xxxxx"
```

## Stap 6: Test je setup

### Email testen:
```bash
curl -X POST http://localhost:3001/api/email/config-test
```

### Telegram bot testen:
```bash
curl -X POST http://localhost:3001/api/telegram/test
```

## Complete .env Template

```env
# ===== ESSENTIEEL =====
DATABASE_URL="file:./dev.db"
RESEND_API_KEY="re_xxxxx"
FROM_EMAIL="<EMAIL>"

# ===== BOTS =====
TELEGRAM_BOT_TOKEN="xxxxx"
TWILIO_ACCOUNT_SID="ACxxxxx"
TWILIO_AUTH_TOKEN="xxxxx"
TWILIO_WHATSAPP_NUMBER="whatsapp:+***********"

# ===== AI (kies één) =====
OPENAI_API_KEY="sk-xxxxx"
# OF
CLOUDFLARE_AI_TOKEN="xxxxx"

# ===== DEVELOPMENT =====
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3001"
```

## 🎯 Prioriteit volgorde:

1. **Resend API** - Voor email versturen ✅
2. **Database** - Voor data opslag ✅
3. **Telegram Bot** - Voor notificaties 📱
4. **WhatsApp** - Voor klant communicatie 💬
5. **AI** - Voor slimme features 🤖

## Hulp nodig?

- **Resend**: <EMAIL>
- **Telegram**: @BotSupport
- **Twilio**: https://www.twilio.com/help

Start met alleen Resend, de rest kun je later toevoegen! 