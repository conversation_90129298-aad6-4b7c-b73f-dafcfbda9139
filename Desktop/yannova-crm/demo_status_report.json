{"timestamp": "2025-07-06T11:07:28.049498", "project": "Yannova CRM", "demo_mode": true, "summary": {"total_files_scanned": 47, "issues_found": 5, "critical_issues": 1, "warnings": 3, "code_smells": 1}, "recommendations": ["Fix database error handling in apps/api/lib/database.ts", "Complete TODO items in CustomerList component", "Remove console.log statements from production code", "Add proper error boundaries in React components"], "next_actions": ["Review and fix critical database connection issue", "Schedule code review for mobile components", "Update error handling documentation"]}