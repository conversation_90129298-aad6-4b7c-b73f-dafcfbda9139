#!/usr/bin/env python3
"""
Global AI Workflow Manager Installer
Installeert de Yannova AI Workflow Manager globally op je systeem
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_global_script():
    """Maak een global executable script aan"""
    
    # Bepaal directories
    home_dir = Path.home()
    global_dir = home_dir / '.yannova_ai'
    bin_dir = home_dir / '.local' / 'bin'
    
    # Maak directories aan
    global_dir.mkdir(exist_ok=True)
    bin_dir.mkdir(parents=True, exist_ok=True)
    
    # Kopieer workflow manager naar global directory
    current_file = Path(__file__).parent / 'ai_workflow_manager.py'
    global_file = global_dir / 'ai_workflow_manager.py'
    
    if current_file.exists():
        shutil.copy2(current_file, global_file)
        print(f"✅ Workflow manager gekopieerd naar {global_file}")
    else:
        print("❌ ai_workflow_manager.py niet gevonden in huidige directory")
        return False
    
    # Maak executable wrapper script
    wrapper_script = bin_dir / 'yannova-ai'
    
    wrapper_content = f'''#!/usr/bin/env python3
"""
Yannova AI Workflow Manager - Global Executable
Voor alle projecten beschikbaar via: yannova-ai
"""

import sys
import os
from pathlib import Path

# Add global directory to path
global_dir = Path.home() / '.yannova_ai'
sys.path.insert(0, str(global_dir))

# Import and run the workflow manager
try:
    from ai_workflow_manager import YannovaCRMWorkflowManager
    
    def main():
        print("🚀 Yannova AI Workflow Manager (Global)")
        print("=" * 50)
        
        # Check if we're in a project directory
        current_dir = Path.cwd()
        
        if not any((current_dir / f).exists() for f in ['package.json', '.git', 'requirements.txt', 'setup.py']):
            print("⚠️  Geen project gedetecteerd in huidige directory")
            print("💡 Tip: Run dit in een project directory voor beste resultaten")
        
        # Initialize and run workflow manager
        try:
            workflow_manager = YannovaCRMWorkflowManager()
            workflow_manager.run_automated_workflow()
        except Exception as e:
            print(f"❌ Fout: {{str(e)}}")
            sys.exit(1)
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Import error: {{e}}")
    print("🔧 Run: python {global_dir / 'setup_ai_dependencies.py'}")
    sys.exit(1)
'''
    
    # Schrijf wrapper script
    with open(wrapper_script, 'w', encoding='utf-8') as f:
        f.write(wrapper_content)
    
    # Maak executable
    os.chmod(wrapper_script, 0o755)
    print(f"✅ Executable script aangemaakt: {wrapper_script}")
    
    # Kopieer ook dependency setup script
    setup_file = Path(__file__).parent / 'setup_ai_dependencies.py'
    global_setup = global_dir / 'setup_ai_dependencies.py'
    
    if setup_file.exists():
        shutil.copy2(setup_file, global_setup)
        print(f"✅ Setup script gekopieerd naar {global_setup}")
    
    return True

def update_shell_config():
    """Update shell configuratie om ~/.local/bin toe te voegen aan PATH"""
    
    home_dir = Path.home()
    shell_configs = [
        home_dir / '.bashrc',
        home_dir / '.zshrc',
        home_dir / '.profile'
    ]
    
    path_line = 'export PATH="$HOME/.local/bin:$PATH"'
    
    for config_file in shell_configs:
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if path_line not in content:
                    with open(config_file, 'a', encoding='utf-8') as f:
                        f.write(f'\n# Added by Yannova AI Workflow Manager\n{path_line}\n')
                    print(f"✅ PATH bijgewerkt in {config_file}")
                else:
                    print(f"ℹ️  PATH al correct in {config_file}")
                    
            except Exception as e:
                print(f"⚠️  Kon {config_file} niet bijwerken: {e}")

def install_dependencies():
    """Installeer benodigde dependencies"""
    print("🔧 Installeren van dependencies...")
    
    dependencies = [
        "requests",
        "python-dotenv",
        "openai"
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', dep
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {dep} geïnstalleerd")
        except subprocess.CalledProcessError:
            print(f"⚠️  Kon {dep} niet installeren - installeer handmatig")

def main():
    """Hoofdfunctie voor de installer"""
    print("🚀 Yannova AI Workflow Manager - Global Installer")
    print("=" * 60)
    
    print("\n📦 Installeren van dependencies...")
    install_dependencies()
    
    print("\n📁 Aanmaken van global scripts...")
    if not create_global_script():
        print("❌ Installatie gefaald")
        return False
    
    print("\n🔧 Bijwerken van shell configuratie...")
    update_shell_config()
    
    print("\n" + "=" * 60)
    print("✅ INSTALLATIE VOLTOOID!")
    print("=" * 60)
    
    print("\n📋 Volgende stappen:")
    print("1. Herstart je terminal of run: source ~/.bashrc")
    print("2. Test de installatie: yannova-ai")
    print("3. Voor LM Studio: start LM Studio met een model")
    print("4. Voor OpenAI: stel OPENAI_API_KEY in je .env bestand in")
    
    print("\n💡 Gebruik:")
    print("   yannova-ai                  # In elk project directory")
    print("   cd mijn-project && yannova-ai  # Voor project-specifieke workflows")
    
    print("\n🔗 Meer info:")
    print(f"   Config directory: {Path.home() / '.yannova_ai'}")
    print(f"   Logs directory: {Path.home() / '.yannova_ai' / 'logs'}")
    print(f"   Executable: {Path.home() / '.local' / 'bin' / 'yannova-ai'}")
    
    print("\n🚀 De AI Workflow Manager is nu globally beschikbaar!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 