# Basis image
FROM node:18-alpine

# Werk directory
WORKDIR /app

# Installeer dependencies eerst (voor betere caching)
COPY package*.json ./
RUN npm ci --only=production

# Kopieer broncode
COPY . .

# Build stap
RUN npm run build

# Health check
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:$PORT/health || exit 1

# Poort configuratie
EXPOSE $PORT

# Start commando
CMD ["node", "dist/main.js"]
