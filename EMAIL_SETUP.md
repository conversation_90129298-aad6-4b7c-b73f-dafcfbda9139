# Email Setup Guide voor Yannova CRM

## Stap 1: Maak een .env file

Maak een `.env` file in de root van je project met deze inhoud:

```env
# Database
DATABASE_URL="file:./dev.db"

# Email Configuration - Zoho Mail
ZOHO_EMAIL="<EMAIL>"
ZOHO_PASSWORD="jouw_app_wachtwoord"
FROM_EMAIL="<EMAIL>"
```

## Stap 2: Zoho App Password

Voor Zoho Mail heb je een app-specifiek wachtwoord nodig:

1. Log in op Zoho Mail
2. Ga naar Settings → Security → Application-Specific Passwords
3. Genereer een nieuw wachtwoord voor "Yannova CRM"
4. Gebruik dit wachtwoord in je .env file

## Stap 3: Test de Email

1. Open http://localhost:3001/send-email.html
2. De Malaga email is al vooringevuld
3. Klik op "Verstuur Email"

## Alternatieve Email Providers

### Option 1: Resend (Makkelijkste)
```env
RESEND_API_KEY="re_xxxxxx"
```

Update dan `email-service.ts`:
```typescript
// Gebruik Resend API
const response = await fetch('https://api.resend.com/emails', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    from: '<EMAIL>',
    to: to,
    subject: subject,
    html: content
  })
});
```

### Option 2: SendGrid
```env
SENDGRID_API_KEY="SG.xxxxxx"
```

### Option 3: Gmail SMTP
```env
GMAIL_USER="<EMAIL>"
GMAIL_APP_PASSWORD="xxxx xxxx xxxx xxxx"
```

## Troubleshooting

Als emails niet werken:

1. **Check je firewall/antivirus** - Port 587 moet open zijn
2. **Zoho specifiek**:
   - Gebruik het EU datacenter: smtp.zoho.eu (niet .com)
   - Zorg dat 2FA aan staat voor app passwords
   - Check of je email verified is

3. **Test met curl**:
```bash
curl -X POST http://localhost:3001/api/email/test \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test",
    "content": "Test email",
    "testType": "direct"
  }'
```

## Quick Fix zonder SMTP

Als je snel wilt testen zonder SMTP setup, gebruik dan een email testing service:

1. Ga naar https://ethereal.email
2. Genereer test credentials
3. Gebruik die in je .env:
```env
ZOHO_EMAIL="<EMAIL>"
ZOHO_PASSWORD="ethereal_password"
```

Dit vangt alle emails op zonder ze echt te versturen! 