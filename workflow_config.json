{"workflow_settings": {"auto_commit": false, "auto_create_issues": false, "max_issues_per_run": 3, "scan_directories": ["apps/api", "apps/mobile", "apps/workers", "packages"], "file_extensions": [".ts", ".tsx", ".js", ".jsx", ".py", ".vue"], "ignore_patterns": ["node_modules", ".git", "dist", "build", ".next"]}, "error_patterns": {"critical": ["throw new Error", "console.error", "process.exit(1)", "CRITICAL:", "FATAL:"], "warnings": ["console.warn", "TODO: FIX", "FIXME", "HACK:", "BUG:", "WARNING:"], "code_smells": ["any;", "// @ts-ignore", "eslint-disable", "debugger;", "console.log"]}, "github_settings": {"issue_labels": ["bug", "automated", "ai-generated"], "assignee": null, "milestone": null, "project_board": null}, "commit_message_templates": {"feat": "feat: {description}", "fix": "fix: {description}", "docs": "docs: {description}", "style": "style: {description}", "refactor": "refactor: {description}", "test": "test: {description}", "chore": "chore: {description}"}, "notifications": {"email": {"enabled": false, "recipients": []}, "slack": {"enabled": false, "webhook_url": ""}, "discord": {"enabled": false, "webhook_url": ""}}, "ai_settings": {"provider": "lm_studio", "model": "mistral-7b-instruct-v0.1", "base_url": "http://127.0.0.1:1234/v1", "temperature": 0.3, "max_tokens": 1000, "language": "nl", "fallback_to_openai": true}, "scheduled_tasks": {"daily_scan": {"enabled": false, "time": "09:00"}, "weekly_report": {"enabled": false, "day": "monday", "time": "10:00"}}, "integrations": {"jira": {"enabled": false, "url": "", "username": "", "api_token": ""}, "trello": {"enabled": false, "api_key": "", "token": ""}, "linear": {"enabled": false, "api_key": ""}}}