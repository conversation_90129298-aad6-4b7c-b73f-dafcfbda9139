# GitHub Integratie Setup - Yannova CRM

## 🐙 Overzicht

De GitHub integratie biedt AI-enhanced repository management, automatische code reviews en project analyse voor je Yannova CRM ontwikkeling.

## 🔧 Environment Variabelen

Voeg deze variabelen toe aan je `.env` bestand:

```bash
# GitHub Integration
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITHUB_OWNER=innovars_lab
GITHUB_REPO=yannova-crm
```

## 🔑 GitHub Personal Access Token Aanmaken

1. **Ga naar GitHub Settings**
   - Klik op je profielfoto → Settings
   - Scroll naar beneden → Developer settings
   - Personal access tokens → Tokens (classic)

2. **Maak nieuwe token aan**
   - Klik "Generate new token (classic)"
   - Geef een beschrijvende naam: "Yannova CRM Integration"
   - Selecteer expiration (aanbevolen: 90 dagen)

3. **Selecteer de juiste scopes:**
   ```
   ✅ repo (Full control of private repositories)
   ✅ read:user (Read access to profile info)
   ✅ user:email (Access to user email addresses)
   ✅ read:org (Read access to organizations)
   ```

4. **Genereer en kopieer token**
   - Klik "Generate token"
   - **BELANGRIJK**: Kopieer de token onmiddellijk (je kunt hem later niet meer zien)
   - Plak hem in je `.env` bestand bij `GITHUB_TOKEN=`

## 🚀 Features

### 📊 Repository Analyse
- **Statistieken**: Stars, forks, issues, repository grootte
- **Taalverdeling**: Overzicht van gebruikte programmeertalen
- **Recent commits**: Laatste ontwikkelingen
- **Open issues**: Actieve taken en bugs

### 🤖 AI-Enhanced Code Review
- **Automatische analyse** van commits
- **Security checks**: Detectie van potentiële beveiligingsproblemen
- **Performance tips**: Optimalisatie suggesties
- **Code quality**: Best practices aanbevelingen

### 🎯 Issue Management
- **Bekijk open issues** met labels en assignees
- **Maak nieuwe issues** direct vanuit dashboard
- **Update bestaande issues** (titel, beschrijving, status)
- **Label management** voor betere organisatie

### 🧠 AI Project Insights
- **Project Health Score**: Geautomatiseerde gezondheidscheck
- **Aanbevelingen**: Specifieke verbeterpunten
- **Sprint planning**: Suggesties voor volgende iteratie
- **Technical debt analyse**: Identificatie van code problemen

### 📁 File Explorer
- **Bestand inhoud** bekijken zonder GitHub te openen
- **Snelle toegang** tot package.json, README.md, etc.
- **Code highlighting** voor betere leesbaarheid

## 🌐 API Endpoints

### POST `/api/github/repository`

**Beschikbare acties:**

```typescript
// Repository analyse
{ "action": "analyze_repository" }

// Commits ophalen
{ "action": "get_commits", "count": 10 }

// Issues beheer
{ "action": "get_issues" }
{ "action": "create_issue", "title": "Bug fix", "body": "Beschrijving", "labels": ["bug"] }
{ "action": "update_issue", "issueNumber": 123, "state": "closed" }

// Code review
{ "action": "code_review", "commitSha": "abc123..." }

// Bestand ophalen
{ "action": "get_file", "path": "package.json" }

// AI project analyse
{ "action": "ai_project_analysis" }
```

## 🎨 Dashboard Toegang

Ga naar: `http://localhost:3001/github-dashboard.html`

### Dashboard Features:
- **📊 Repository Analyse**: Volledige repository statistieken
- **📝 Commit Tracking**: Recent commits met AI review opties
- **🎯 Issue Management**: Creëer en beheer GitHub issues
- **🔍 AI Code Review**: Geautomatiseerde code analyse
- **🧠 AI Project Insights**: Intelligente project aanbevelingen
- **📁 File Explorer**: Browse repository bestanden

## 🔄 Automatische Workflows

De GitHub integratie kan gekoppeld worden aan je bestaande AI workflows:

```typescript
// Automatische code review na elke commit
await github.performCodeReview(latestCommitSha);

// Wekelijkse project health check
await github.analyzeRepository();

// Automatische issue creation bij bugs
await github.createIssue("Bug detected", bugDescription, ["bug", "auto-detected"]);
```

## 🛡️ Security Best Practices

1. **Token Beveiliging**
   - Gebruik alleen de minimaal benodigde scopes
   - Stel expiration datum in (max 90 dagen)
   - Revoke tokens die je niet meer gebruikt

2. **Environment Variables**
   - Voeg `.env` toe aan `.gitignore`
   - Gebruik verschillende tokens voor dev/prod
   - Monitor token usage in GitHub settings

3. **Rate Limiting**
   - GitHub API heeft rate limits (5000 requests/hour)
   - De service implementeert automatische retry logic
   - Monitor API usage via dashboard

## 🐛 Troubleshooting

### "GitHub token niet geconfigureerd"
- Controleer of `GITHUB_TOKEN` in `.env` staat
- Verifieer dat de token geldig is
- Check token scopes in GitHub settings

### "HTTP 403 Forbidden"
- Token heeft onvoldoende rechten
- Repository is private en token heeft geen toegang
- Rate limit bereikt (wacht 1 uur)

### "Repository not found"
- Controleer `GITHUB_OWNER` en `GITHUB_REPO` values
- Verifieer dat je toegang hebt tot de repository
- Check of repository naam correct gespeld is

## 🔗 Integratie met Bestaande Services

De GitHub service integreert naadloos met:
- **🤖 AI Orchestrator**: Automatische model routing voor code analyse
- **📧 Email Service**: Notificaties bij belangrijke repository events
- **📱 Telegram Bot**: Updates over commits en issues
- **📊 CRM Dashboard**: Project metrics en development insights

## 📈 Volgende Stappen

1. **Setup GitHub token** volgens bovenstaande instructies
2. **Test de verbinding** via repository analyse
3. **Configureer automatische workflows** voor je development proces
4. **Integreer met AI agents** voor intelligente project management

---

🎉 **Je GitHub integratie is nu klaar!** 

Ga naar het dashboard en start met "Analyseer Repository" voor een volledig overzicht van je project. 