{
  "name": "Default Yannova Profile",
  "isDefault": true,
  "settings": {
    "window.newWindowDimensions": "maximized",
    "window.restoreWindows": "preserve",
    "window.title": "${rootName}${separator}${activeEditorShort}",
    "workbench.startupEditor": "none",
    "workbench.editor.restoreViewState": true,
    
    // Editor basis instellingen
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.fontFamily": "JetBrains Mono, Menlo, Monaco, 'Courier New', monospace",
    "editor.fontSize": 14,
    "editor.lineHeight": 1.5,
    "editor.cursorBlinking": "phase",
    "editor.cursorSmoothCaretAnimation": "on",
    "editor.cursorWidth": 2,
    "editor.guides.indentation": true,
    "editor.bracketPairColorization.enabled": true,
    "editor.smoothScrolling": true,

    // Cursor AI configuratie
    "cursor.showChatCompletions": true,
    "cursor.preferredLanguage": "nl-NL",
    "cursor.features": {
      "chat": true,
      "copilot": true,
      "snippets": true
    },

    // Thema en kleuren
    "workbench.colorTheme": "GitHub Dark Default",
    "workbench.colorCustomizations": {
      "editor.background": "#0D1117",
      "editor.foreground": "#c9d1d9",
      "activityBar.background": "#161B22FA",
      "sideBar.background": "#161B22FA",
      "terminal.background": "#0D1117FA",
      "statusBar.background": "#161B22FA",
      "editorGutter.background": "#0D1117FA",
      "tab.activeBackground": "#1D242DFA",
      "tab.inactiveBackground": "#161B22FA",
      "editor.lineHighlightBackground": "#1B2028CC",
      "editor.selectionBackground": "#2E4C75CC",
      "editor.wordHighlightBackground": "#2E4C7566"
    },

    // Project defaults
    "files.exclude": {
      "**/node_modules": true,
      "**/.git": true,
      "**/.DS_Store": true,
      "**/dist": false,
      "**/coverage": true
    },

    // Terminal settings
    "terminal.integrated.gpuAcceleration": "on",
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.fontFamily": "JetBrains Mono",
    
    // Workspace behavior
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "explorer.compactFolders": false,
    "explorer.confirmDelete": false,
    "workbench.editor.enablePreview": false
  }
} 