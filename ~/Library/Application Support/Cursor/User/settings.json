{
  // Editor basis instellingen
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.fontFamily": "JetBrains Mono, Menlo, Monaco, 'Courier New', monospace",
  "editor.fontSize": 14,
  "editor.lineHeight": 1.5,
  "editor.cursorBlinking": "phase",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.cursorWidth": 2,
  "editor.guides.indentation": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.smoothScrolling": true,

  // Cursor AI configuratie
  "cursor.showChatCompletions": true,
  "cursor.preferredLanguage": "nl-NL",
  "cursor.features": {
    "chat": true,
    "copilot": true,
    "snippets": true
  },

  // Thema en kleuren
  "workbench.colorTheme": "GitHub Dark Default",
  "workbench.colorCustomizations": {
    "editor.background": "#0D1117",
    "editor.foreground": "#c9d1d9",
    "activityBar.background": "#161B22FA",
    "sideBar.background": "#161B22FA",
    "terminal.background": "#0D1117FA",
    "statusBar.background": "#161B22FA",
    // ... andere kleurinstellingen ...
  },

  // Project defaults
  "files.exclude": {
    "**/node_modules": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/dist": false,
    "**/coverage": true
  },

  // Extensie aanbevelingen
  "extensions.ignoreRecommendations": false,
  "extensions.autoUpdate": true,

  // Automatische imports
  "typescript.suggest.paths": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",

  // Git instellingen
  "git.enableSmartCommit": true,
  "git.autofetch": true,

  // Terminal settings
  "terminal.integrated.gpuAcceleration": "on",
  "terminal.integrated.defaultProfile.osx": "zsh",
  
  // Workspace templates
  "workbench.startupEditor": "none",
  "workbench.editor.restoreViewState": true,
  
  // File associations
  "files.associations": {
    "*.ts": "typescript",
    "*.tsx": "typescriptreact",
    "*.vue": "vue",
    "*.prisma": "prisma"
  }
} 