#!/bin/bash

# K<PERSON>uren voor output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Cursor globale setup start...${NC}"

# Maak Cursor configuratie directories
mkdir -p ~/Library/Application\ Support/Cursor/User/
mkdir -p ~/Library/Application\ Support/Cursor/User/snippets/
mkdir -p ~/Library/Application\ Support/Cursor/User/profiles/

# Kopieer settings en profielen
cp .vscode/settings.json ~/Library/Application\ Support/Cursor/User/settings.json
cp .vscode/profiles/default.json ~/Library/Application\ Support/Cursor/User/profiles/default.json

# Installeer basis extensies
echo -e "${BLUE}📦 Installeren van basis extensies...${NC}"
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension prisma.prisma
code --install-extension vue.volar
code --install-extension bradlc.vscode-tailwindcss

# Maak globale project template directory
mkdir -p ~/Library/Application\ Support/Cursor/User/templates/
cp .vscode/project-templates/default.code-workspace ~/Library/Application\ Support/Cursor/User/templates/

# Maak Cursor snelkoppelingen
defaults write com.cursor.Cursor NSUserKeyEquivalents -dict-add "New Window" "@\$n"
defaults write com.cursor.Cursor ApplePressAndHoldEnabled -bool false

echo -e "${GREEN}✅ Cursor globale setup compleet!${NC}"
echo -e "${BLUE}💡 Tips:${NC}"
echo "1. Herstart Cursor om de nieuwe instellingen toe te passen"
echo "2. Nieuwe vensters krijgen automatisch deze instellingen"
echo "3. Gebruik Cmd+Shift+N voor een nieuw venster met deze instellingen"
echo "4. Check ~/Library/Application Support/Cursor/User/profiles/default.json voor aanpassingen" 