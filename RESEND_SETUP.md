# Resend Email Setup

## Stap 1: Haal je API Key op

1. Log in op https://resend.com
2. Ga naar **API Keys** in het dashboard
3. <PERSON><PERSON><PERSON> je API key (begint met `re_`)

## Stap 2: Maak een .env file

Maak een `.env` file in de root directory van het project:

```env
# Resend Configuration
RESEND_API_KEY=re_QD1HKKcD_2UqTWniv94vAn3B7ahBu2JgR
FROM_EMAIL=<EMAIL>

# Database
DATABASE_URL="file:./dev.db"
```

## Stap 3: Test je configuratie

Open: http://localhost:3001/api/email/config-test

Je zou moeten zien:
- resendApiKey: ✅ Configured

## Stap 4: Verstuur je Malaga email

Open: http://localhost:3001/send-email.html

De email is al vooringevuld en klaar om te versturen!

## Extra Features met Resend

### 1. Email Templates
Je kunt templates maken in Resend dashboard en gebruiken:
```javascript
ResendEmailService.sendWithTemplate(
  '<EMAIL>',
  'template_id',
  { name: 'Leon', destination: 'Malaga' }
)
```

### 2. Bulk Emails
Verstuur naar meerdere ontvangers tegelijk:
```javascript
ResendEmailService.sendBulkEmails(
  ['<EMAIL>', '<EMAIL>'],
  'Subject',
  'Content'
)
```

### 3. Email Tracking
Resend biedt automatisch:
- Open tracking
- Click tracking
- Bounce handling
- Spam complaints

## Troubleshooting

Als emails niet werken:

1. **Check API Key**: Zorg dat je API key correct is
2. **Check domein**: Je domein moet geverifieerd zijn in Resend
3. **Check logs**: Kijk in de browser console voor errors
4. **Test met curl**:
```bash
curl -X POST http://localhost:3001/api/email/test \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test",
    "content": "Test email",
    "testType": "direct"
  }'
``` 

Ja, we kunnen het `memory-bank-mcp` project toevoegen aan je bestaande yannova-crm project. Hier is hoe we dat kunnen doen:

1. **Clone het memory-bank-mcp project** als submodule:
```bash
cd /Users/<USER>/Desktop/yannova-crm
git submodule add https://github.com/alioshr/memory-bank-mcp.git apps/memory-bank-mcp
```

2. **Installeer de dependencies**:
```bash
cd apps/memory-bank-mcp
npm install
```

3. **Voeg configuratie toe** aan je bestaande MCP setup (bijvoorbeeld in `apps/api/lib/memory-bank.ts`):
```typescript
import { MemoryBankServer } from '../../memory-bank-mcp/src/server';

export const initMemoryBank = () => {
  const server = new MemoryBankServer({
    rootPath: '/Users/<USER>/Desktop/yannova-crm/data/memory-banks'
  });
  
  server.start(3001); // Poort aanpassen indien nodig
  console.log('Memory Bank MCP server running on port 3001');
};
```

4. **Update je Docker configuratie** (indien van toepassing):
```5:15:docker-compose.yml
// ... existing code ...
services:
  memory-bank:
    build: ./apps/memory-bank-mcp
    ports:
      - "3001:3001"
    volumes:
      - ./data/memory-banks:/app/data
// ... existing code ...
```

5. **Integratie met bestaande services**:
Voeg dit toe aan je `apps/api/lib/ai-orchestrator.ts`:
```typescript
import { MemoryBankClient } from '../../memory-bank-mcp/src/client';

const memoryBank = new MemoryBankClient('http://localhost:3001');
```

Belangrijke punten:
- Zorg dat je Node.js v16+ hebt geïnstalleerd
- De memory bank data wordt opgeslagen in `/Users/<USER>/Desktop/yannova-crm/data/memory-banks`
- Pas de poorten aan indien nodig (3001 is een voorbeeld)

Wil je dat ik specifieke onderdelen verder uitwerk voor je project? Bijvoorbeeld:
1. Aanpassingen voor Prisma integratie
2. Specifieke API endpoints voor je CRM
3. Aanpassingen voor je bestaande AI workflows

[Bron: GitHub - alioshr/memory-bank-mcp](https://github.com/alioshr/memory-bank-mcp.git) 