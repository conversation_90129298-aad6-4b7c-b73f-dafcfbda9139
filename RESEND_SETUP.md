# Resend Email Setup

## Stap 1: Haal je API Key op

1. Log in op https://resend.com
2. Ga naar **API Keys** in het dashboard
3. <PERSON><PERSON><PERSON> je API key (begint met `re_`)

## Stap 2: Maak een .env file

Maak een `.env` file in de root directory van het project:

```env
# Resend Configuration
RESEND_API_KEY=re_QD1HKKcD_2UqTWniv94vAn3B7ahBu2JgR
FROM_EMAIL=<EMAIL>

# Database
DATABASE_URL="file:./dev.db"
```

## Stap 3: Test je configuratie

Open: http://localhost:3001/api/email/config-test

Je zou moeten zien:
- resendApiKey: ✅ Configured

## Stap 4: Verstuur je Malaga email

Open: http://localhost:3001/send-email.html

De email is al vooringevuld en klaar om te versturen!

## Extra Features met Resend

### 1. Email Templates
Je kunt templates maken in Resend dashboard en gebruiken:
```javascript
ResendEmailService.sendWithTemplate(
  '<EMAIL>',
  'template_id',
  { name: 'Leon', destination: 'Malaga' }
)
```

### 2. Bulk Emails
Verstuur naar meerdere ontvangers tegelijk:
```javascript
ResendEmailService.sendBulkEmails(
  ['<EMAIL>', '<EMAIL>'],
  'Subject',
  'Content'
)
```

### 3. Email Tracking
Resend biedt automatisch:
- Open tracking
- Click tracking
- Bounce handling
- Spam complaints

## Troubleshooting

Als emails niet werken:

1. **Check API Key**: Zorg dat je API key correct is
2. **Check domein**: Je domein moet geverifieerd zijn in Resend
3. **Check logs**: Kijk in de browser console voor errors
4. **Test met curl**:
```bash
curl -X POST http://localhost:3001/api/email/test \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test",
    "content": "Test email",
    "testType": "direct"
  }'
``` 