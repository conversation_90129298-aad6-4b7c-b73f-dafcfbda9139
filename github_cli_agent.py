#!/usr/bin/env python3
"""
GitHub CLI Agent - AI-powered GitHub management via CLI
Alternatief voor Composio met directe GitHub CLI integratie
"""

import subprocess
import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime

class GitHubCLIAgent:
    """AI Agent die GitHub CLI gebruikt voor repository management"""
    
    def __init__(self):
        self.check_gh_cli()
        self.check_authentication()
    
    def check_gh_cli(self):
        """Controleer of GitHub CLI geïnstalleerd is"""
        try:
            result = subprocess.run(['gh', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("GitHub CLI niet gevonden")
            print(f"✅ GitHub CLI gedetecteerd: {result.stdout.strip()}")
        except FileNotFoundError:
            print("❌ GitHub CLI niet geïnstalleerd")
            print("💡 Installeer met: brew install gh")
            sys.exit(1)
    
    def check_authentication(self):
        """Controleer GitHub CLI authenticatie"""
        try:
            result = subprocess.run(['gh', 'auth', 'status'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ GitHub CLI geauthenticeerd")
            else:
                print("❌ GitHub CLI niet geauthenticeerd")
                print("💡 Login met: gh auth login")
                return False
        except Exception as e:
            print(f"⚠️ Authenticatie check gefaald: {e}")
            return False
        return True
    
    def run_gh_command(self, command: List[str]) -> Dict[str, Any]:
        """Voer GitHub CLI commando uit en return JSON result"""
        try:
            full_command = ['gh'] + command
            print(f"🔧 Uitvoeren: {' '.join(full_command)}")
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                return {
                    'success': False,
                    'error': result.stderr.strip(),
                    'command': ' '.join(full_command)
                }
            
            # Probeer JSON te parsen als het output JSON is
            try:
                data = json.loads(result.stdout) if result.stdout.strip() else {}
                return {
                    'success': True,
                    'data': data,
                    'raw_output': result.stdout
                }
            except json.JSONDecodeError:
                return {
                    'success': True,
                    'data': result.stdout.strip(),
                    'raw_output': result.stdout
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def analyze_repository(self, repo: str = None) -> Dict[str, Any]:
        """Analyseer repository met GitHub CLI"""
        commands = []
        
        if repo:
            commands.append(['repo', 'view', repo, '--json', 'name,description,stargazerCount,forkCount,issues,createdAt,updatedAt,languages'])
        else:
            commands.append(['repo', 'view', '--json', 'name,description,stargazerCount,forkCount,issues,createdAt,updatedAt,languages'])
        
        # Voeg extra info toe
        commands.extend([
            ['issue', 'list', '--json', 'number,title,state,labels,createdAt,updatedAt', '--limit', '20'],
            ['pr', 'list', '--json', 'number,title,state,createdAt,updatedAt', '--limit', '10'],
            ['run', 'list', '--json', 'status,conclusion,createdAt', '--limit', '5']
        ])
        
        results = {}
        for i, cmd in enumerate(commands):
            result = self.run_gh_command(cmd)
            
            if i == 0:
                results['repository'] = result
            elif i == 1:
                results['issues'] = result
            elif i == 2:
                results['pull_requests'] = result
            elif i == 3:
                results['workflows'] = result
        
        return self.generate_repository_insights(results)
    
    def generate_repository_insights(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Genereer AI insights van repository data"""
        insights = {
            'timestamp': datetime.now().isoformat(),
            'repository_health': 'Unknown',
            'metrics': {},
            'recommendations': [],
            'action_items': [],
            'ai_analysis': {}
        }
        
        # Repository metrics
        if data.get('repository', {}).get('success'):
            repo_data = data['repository']['data']
            insights['metrics'] = {
                'stars': repo_data.get('stargazerCount', 0),
                'forks': repo_data.get('forkCount', 0),
                'name': repo_data.get('name', 'Unknown'),
                'description': repo_data.get('description', ''),
                'created': repo_data.get('createdAt', ''),
                'updated': repo_data.get('updatedAt', '')
            }
        
        # Issues analysis
        if data.get('issues', {}).get('success'):
            issues = data['issues']['data']
            open_issues = [i for i in issues if i.get('state') == 'open']
            bug_issues = [i for i in issues if any('bug' in str(label).lower() for label in i.get('labels', []))]
            
            insights['metrics']['open_issues'] = len(open_issues)
            insights['metrics']['bug_issues'] = len(bug_issues)
            
            # AI Recommendations based on issues
            if len(open_issues) > 15:
                insights['recommendations'].append("🔴 Hoog aantal open issues - prioriteer sluiten van oude issues")
            
            if len(bug_issues) > 5:
                insights['recommendations'].append("🐛 Veel bug reports - overweeg extra testing en QA")
                insights['action_items'].append("Implementeer automated testing pipeline")
        
        # Pull requests analysis
        if data.get('pull_requests', {}).get('success'):
            prs = data['pull_requests']['data']
            open_prs = [pr for pr in prs if pr.get('state') == 'open']
            
            insights['metrics']['open_prs'] = len(open_prs)
            
            if len(open_prs) > 5:
                insights['recommendations'].append("📋 Veel open PRs - versneld review proces")
        
        # Workflow analysis
        if data.get('workflows', {}).get('success'):
            workflows = data['workflows']['data']
            failed_workflows = [w for w in workflows if w.get('conclusion') == 'failure']
            
            insights['metrics']['failed_workflows'] = len(failed_workflows)
            
            if len(failed_workflows) > 0:
                insights['recommendations'].append("❌ Gefaalde workflows gedetecteerd - fix CI/CD issues")
                insights['action_items'].append("Debug en fix failing GitHub Actions")
        
        # Calculate health score
        score = 100
        score -= min(insights['metrics'].get('open_issues', 0) * 2, 30)
        score -= min(insights['metrics'].get('bug_issues', 0) * 5, 25)
        score -= min(insights['metrics'].get('failed_workflows', 0) * 10, 20)
        
        if score >= 80:
            insights['repository_health'] = 'Excellent'
        elif score >= 60:
            insights['repository_health'] = 'Good'
        elif score >= 40:
            insights['repository_health'] = 'Fair'
        else:
            insights['repository_health'] = 'Needs Attention'
        
        # AI Analysis
        insights['ai_analysis'] = {
            'health_score': score,
            'priority_focus': self.determine_priority_focus(insights),
            'next_sprint_suggestions': self.generate_sprint_suggestions(insights),
            'technical_recommendations': self.generate_technical_recommendations(insights)
        }
        
        return insights
    
    def determine_priority_focus(self, insights: Dict[str, Any]) -> str:
        """Bepaal waar de focus moet liggen"""
        metrics = insights.get('metrics', {})
        
        if metrics.get('failed_workflows', 0) > 0:
            return "🔧 CI/CD Stabiliteit - Fix failing workflows eerst"
        elif metrics.get('bug_issues', 0) > 5:
            return "🐛 Bug Fixes - Veel open bugs moeten opgelost worden"
        elif metrics.get('open_issues', 0) > 20:
            return "📋 Issue Management - Grote backlog moet aangepakt worden"
        elif metrics.get('open_prs', 0) > 5:
            return "🔍 Code Review - Veel pending pull requests"
        else:
            return "🚀 Feature Development - Project is stabiel, focus op nieuwe features"
    
    def generate_sprint_suggestions(self, insights: Dict[str, Any]) -> List[str]:
        """Genereer sprint suggesties"""
        suggestions = []
        metrics = insights.get('metrics', {})
        
        # Gebaseerd op metrics
        if metrics.get('bug_issues', 0) > 0:
            suggestions.append(f"🐛 Reserveer 40% van sprint voor {metrics['bug_issues']} bug fixes")
        
        if metrics.get('open_prs', 0) > 0:
            suggestions.append(f"🔍 Plan dagelijkse code review sessies voor {metrics['open_prs']} open PRs")
        
        # Altijd goede suggesties
        suggestions.extend([
            "📚 Update documentatie en README",
            "🧪 Verhoog test coverage met unit tests",
            "⚡ Performance audit van kritieke functies",
            "🛡️ Security review van authentication flows"
        ])
        
        return suggestions[:5]  # Top 5 suggesties
    
    def generate_technical_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Genereer technische aanbevelingen"""
        recommendations = []
        
        # Gebaseerd op repository health
        health = insights.get('repository_health', '')
        
        if health in ['Fair', 'Needs Attention']:
            recommendations.extend([
                "🔄 Implementeer automated dependency updates",
                "📊 Setup code quality metrics (SonarQube/CodeClimate)",
                "🏗️ Refactor legacy code met hoge complexiteit"
            ])
        
        recommendations.extend([
            "🚀 Setup automated deployment pipeline",
            "📈 Implementeer application monitoring",
            "🔐 Audit en update security dependencies",
            "📱 Optimize voor mobile/responsive design"
        ])
        
        return recommendations[:6]  # Top 6 aanbevelingen
    
    def create_issue_from_ai_analysis(self, insights: Dict[str, Any]) -> Dict[str, Any]:
        """Maak automatisch een issue aan gebaseerd op AI analyse"""
        health = insights.get('repository_health', '')
        
        if health in ['Fair', 'Needs Attention']:
            title = f"🤖 AI Repository Health Check - Status: {health}"
            
            body_parts = [
                f"# 🤖 Geautomatiseerde Repository Health Check",
                f"",
                f"**Status:** {health}",
                f"**Health Score:** {insights['ai_analysis']['health_score']}/100",
                f"**Gegenereerd:** {insights['timestamp']}",
                f"",
                f"## 📊 Metrics",
                f"- Open Issues: {insights['metrics'].get('open_issues', 0)}",
                f"- Bug Issues: {insights['metrics'].get('bug_issues', 0)}",
                f"- Open PRs: {insights['metrics'].get('open_prs', 0)}",
                f"- Failed Workflows: {insights['metrics'].get('failed_workflows', 0)}",
                f"",
                f"## 🎯 Priority Focus",
                f"{insights['ai_analysis']['priority_focus']}",
                f"",
                f"## 💡 Aanbevelingen",
            ]
            
            for rec in insights['recommendations']:
                body_parts.append(f"- {rec}")
            
            body_parts.extend([
                f"",
                f"## 🚀 Sprint Suggesties",
            ])
            
            for suggestion in insights['ai_analysis']['next_sprint_suggestions']:
                body_parts.append(f"- {suggestion}")
            
            body_parts.extend([
                f"",
                f"## 🔧 Technische Aanbevelingen",
            ])
            
            for tech_rec in insights['ai_analysis']['technical_recommendations']:
                body_parts.append(f"- {tech_rec}")
            
            body_parts.extend([
                f"",
                f"---",
                f"*Deze issue is automatisch gegenereerd door de AI Repository Health Agent*"
            ])
            
            body = "\n".join(body_parts)
            
            # Maak issue aan
            result = self.run_gh_command([
                'issue', 'create',
                '--title', title,
                '--body', body,
                '--label', 'ai-generated,health-check,maintenance'
            ])
            
            return result
        
        return {'success': False, 'message': 'Repository health is goed, geen issue nodig'}
    
    def run_ai_workflow(self, action: str = 'full_analysis') -> Dict[str, Any]:
        """Voer complete AI workflow uit"""
        print(f"🤖 Starting AI GitHub workflow: {action}")
        
        if action == 'full_analysis':
            # Stap 1: Repository analyse
            print("📊 Stap 1: Repository analyse...")
            insights = self.analyze_repository()
            
            # Stap 2: AI issue creation (indien nodig)
            print("🎯 Stap 2: AI issue analysis...")
            issue_result = self.create_issue_from_ai_analysis(insights)
            
            # Stap 3: Genereer rapport
            print("📋 Stap 3: Rapport genereren...")
            report = {
                'workflow': 'full_analysis',
                'timestamp': datetime.now().isoformat(),
                'insights': insights,
                'issue_created': issue_result.get('success', False),
                'issue_details': issue_result if issue_result.get('success') else None,
                'summary': {
                    'health': insights.get('repository_health'),
                    'score': insights.get('ai_analysis', {}).get('health_score'),
                    'priority': insights.get('ai_analysis', {}).get('priority_focus'),
                    'recommendations_count': len(insights.get('recommendations', [])),
                    'action_items_count': len(insights.get('action_items', []))
                }
            }
            
            return report
        
        return {'error': f'Unknown action: {action}'}


def main():
    """Main CLI interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='GitHub CLI AI Agent')
    parser.add_argument('action', choices=['analyze', 'full_analysis', 'health_check'], 
                       help='Action to perform')
    parser.add_argument('--repo', help='Repository to analyze (owner/repo)')
    parser.add_argument('--output', choices=['json', 'pretty'], default='pretty',
                       help='Output format')
    
    args = parser.parse_args()
    
    agent = GitHubCLIAgent()
    
    if args.action == 'analyze':
        result = agent.analyze_repository(args.repo)
    elif args.action in ['full_analysis', 'health_check']:
        result = agent.run_ai_workflow('full_analysis')
    else:
        result = {'error': f'Unknown action: {args.action}'}
    
    if args.output == 'json':
        print(json.dumps(result, indent=2))
    else:
        # Pretty print
        print("\n🤖 GitHub AI Agent Results")
        print("=" * 50)
        
        if 'insights' in result:
            insights = result['insights']
            print(f"📊 Repository Health: {insights['repository_health']}")
            print(f"🎯 Health Score: {insights['ai_analysis']['health_score']}/100")
            print(f"🔍 Priority Focus: {insights['ai_analysis']['priority_focus']}")
            
            print(f"\n📈 Metrics:")
            for key, value in insights['metrics'].items():
                print(f"  • {key}: {value}")
            
            print(f"\n💡 Recommendations ({len(insights['recommendations'])}):")
            for rec in insights['recommendations']:
                print(f"  • {rec}")
            
            print(f"\n🚀 Sprint Suggestions:")
            for suggestion in insights['ai_analysis']['next_sprint_suggestions']:
                print(f"  • {suggestion}")
        
        if result.get('issue_created'):
            print(f"\n✅ AI Health Check issue created successfully!")


if __name__ == '__main__':
    main() 