#!/bin/bash

# Stap 1: Type checking
echo "Controleer TypeScript typen..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
  echo "❌ TypeScript fouten gevonden!"
  exit 1
fi

# Stap 2: Testen
echo "Draai unit tests..."
npx jest --passWithNoTests --findRelatedTests --onlyChanged
if [ $? -ne 0 ]; then
  echo "❌ Unit tests gefaald!"
  exit 1
fi

# Stap 3: Linting
echo "Controleer code stijl..."
npx eslint --fix --ext .ts,.tsx src/
if [ $? -ne 0 ]; then
  echo "❌ ESLint fouten gevonden!"
  exit 1
fi

echo "✅ Alles in orde! Committing..."
exit 0 