# YannovaCRM MVP - Complete Setup Guide

## 📁 Project Structure
```
yannova-crm/
├── apps/
│   ├── api/                 # Next.js Backend API
│   ├── web/                 # Admin Dashboard (Next.js)
│   ├── mobile/              # PWA Mobile App (Vue/React)
│   └── workers/             # Cloudflare Workers
├── packages/
│   ├── database/            # Prisma Schema
│   ├── types/               # Shared TypeScript types
│   └── utils/               # Shared utilities
└── deployment/
    ├── docker-compose.yml
    └── .env.example
```

## 🚀 Quick Start Commands
```bash
# 1. Setup project
npx create-turbo@latest yannova-crm
cd yannova-crm

# 2. Install dependencies
npm install prisma @prisma/client
npm install next react react-dom
npm install @whatsapp/node-sdk telegram-bot-api

# 3. Setup database
npx prisma init
npx prisma db push
npx prisma generate

# 4. Start development
npm run dev
```

## 🗄️ Environment Variables (.env)
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/yannova_crm"

# Email
CLOUDFLARE_API_TOKEN="your_cf_token"
CLOUDFLARE_ZONE_ID="your_zone_id"

# WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID="your_wa_business_id"
WHATSAPP_ACCESS_TOKEN="your_wa_token"

# Telegram
TELEGRAM_BOT_TOKEN="your_telegram_token"

# NextAuth
NEXTAUTH_SECRET="your_secret_key"
NEXTAUTH_URL="https://crm.yannova.be"
```