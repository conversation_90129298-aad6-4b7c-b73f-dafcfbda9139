# YannovaCRM Quick Start Guide

## 🚀 Setup in 5 minuten:

### 1. Install dependencies
```bash
cd /Users/<USER>/Desktop/yannova-crm
npm install
```

### 2. Setup database
```bash
# Kopieer env file
cp .env.example .env

# Vul DATABASE_URL in met jouw PostgreSQL details
# Bijvoorbeeld: postgresql://user:pass@localhost:5432/yannova_crm

# Genereer Prisma client
npm run db:generate

# Push database schema
npm run db:push
```

### 3. Start development
```bash
# Start alle services
npm run dev

# Of individueel:
# API server (poort 3001): cd apps/api && npm run dev
# Mobile app (poort 3002): cd apps/mobile && npm run dev
```

### 4. Setup Cloudflare Email Worker
```bash
cd apps/workers
npm install -g wrangler
wrangler login
wrangler deploy
```

### 5. Configure Cloudflare DNS
In je Cloudflare dashboard voor yannova.be:
- Ga naar Email → Email Routing
- Enable Email Routing
- Add destination: <EMAIL>
- Add rule: <EMAIL> → forward to destination

## 📱 URLs:
- API: http://localhost:3001
- Mobile: http://localhost:3002
- Database Studio: npm run db:studio

## 🔧 Next Steps:
1. Vul .env file in met echte API keys
2. Setup WhatsApp Business API
3. Create Telegram bot via @BotFather
4. Deploy naar production (Vercel/Railway)

## 📧 Test email integration:
Stuur een <NAME_EMAIL> en check:
- Database voor nieuwe contact
- API logs
- Mobile app voor nieuwe conversatie