{"version": "2.0.0", "created": "2025-07-06T12:00:00.000Z", "project_name": "Yannova CRM", "ai_preferences": {"prefer_lm_studio": true, "fallback_to_openai": true, "model": "mistral-7b-instruct-v0.1", "base_url": "http://127.0.0.1:1234/v1", "temperature": 0.7, "max_tokens": 1000}, "lm_studio": {"enabled": true, "base_url": "http://127.0.0.1:1234/v1", "api_key": "lm-studio", "timeout": 30, "models": [{"name": "mistral-7b-instruct-v0.1", "displayName": "Mistral 7B (Code)", "capabilities": ["coding", "analysis", "planning", "chat"], "priority": 1, "maxTokens": 4096, "description": "Snelle lokale AI voor coding en algemene taken", "systemPrompt": "<PERSON> bent een Nederlandse AI developer assistant. Antwoord altijd in het Nederlands."}, {"name": "deepseek-coder-6.7b-instruct", "displayName": "DeepSeek Coder", "capabilities": ["coding", "debugging", "refactoring"], "priority": 2, "maxTokens": 4096, "description": "Gespecialiseerd model voor code review en debugging", "systemPrompt": "<PERSON> <PERSON> een expert code reviewer. <PERSON>f concrete feedback in het Nederlands."}, {"name": "llama-2-7b-chat", "displayName": "Llama 2 Chat", "capabilities": ["chat", "analysis", "planning"], "priority": 3, "maxTokens": 4096, "description": "Conversationele AI voor algemene vragen en planning"}]}, "openai": {"enabled": true, "fallback_only": true, "models": [{"name": "gpt-4", "capabilities": ["coding", "analysis", "email", "chat", "planning"], "priority": 10, "maxTokens": 8192, "description": "Krachtige API model voor complexe taken", "systemPrompt": "<PERSON> <PERSON>, Nederlandse AI developer. Antwoord professioneel in het Nederlands."}, {"name": "gpt-3.5-turbo", "capabilities": ["chat", "email", "analysis"], "priority": 11, "maxTokens": 4096, "description": "Snelle API model voor eenvoudige taken"}]}, "task_preferences": {"coding": ["mistral-7b-instruct-v0.1", "deepseek-coder-6.7b-instruct", "gpt-4"], "analysis": ["mistral-7b-instruct-v0.1", "llama-2-7b-chat", "gpt-4"], "planning": ["mistral-7b-instruct-v0.1", "llama-2-7b-chat", "gpt-4"], "chat": ["llama-2-7b-chat", "mistral-7b-instruct-v0.1", "gpt-3.5-turbo"], "email": ["mistral-7b-instruct-v0.1", "gpt-4", "gpt-3.5-turbo"], "commit_messages": ["mistral-7b-instruct-v0.1", "deepseek-coder-6.7b-instruct"], "bug_analysis": ["deepseek-coder-6.7b-instruct", "mistral-7b-instruct-v0.1", "gpt-4"]}, "workflow_settings": {"auto_detect_changes": true, "smart_commit_messages": true, "auto_scan_errors": true, "github_integration": true, "prefer_local_ai": true, "notifications": {"enabled": false, "channels": []}}}