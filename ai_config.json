{"version": "2.0.0", "created": "2025-07-06T12:00:00.000Z", "project_name": "Yannova CRM", "ai_preferences": {"prefer_lm_studio": true, "fallback_to_openai": true, "model": "mistral-7b-instruct-v0.1", "base_url": "http://127.0.0.1:1234/v1", "temperature": 0.7, "max_tokens": 1000}, "lm_studio": {"enabled": true, "multi_model": true, "api_key": "lm-studio", "timeout": 30, "servers": [{"name": "coding_server", "base_url": "http://127.0.0.1:1234/v1", "port": 1234, "model": "mistral-7b-instruct-v0.1", "displayName": "Mistral 7B (Code Server)", "capabilities": ["coding", "analysis", "planning", "commit_messages"], "priority": 1, "maxTokens": 4096, "description": "Primaire server voor coding en algemene taken", "systemPrompt": "<PERSON> bent een Nederlandse AI developer assistant. Antwoord altijd in het Nederlands en focus op code kwaliteit."}, {"name": "chat_server", "base_url": "http://127.0.0.1:1235/v1", "port": 1235, "model": "deepseek-coder-6.7b-instruct", "displayName": "DeepSeek Coder (Chat Server)", "capabilities": ["debugging", "refactoring", "code_review", "chat"], "priority": 1, "maxTokens": 4096, "description": "Gespecialiseerde server voor code review en debugging", "systemPrompt": "<PERSON> bent een expert code reviewer en debugger. <PERSON>f concrete, actionable feedback in het Nederlands."}], "load_balancing": {"enabled": true, "strategy": "capability_based", "fallback_enabled": true}}, "openai": {"enabled": true, "fallback_only": true, "models": [{"name": "gpt-4", "capabilities": ["coding", "analysis", "email", "chat", "planning"], "priority": 10, "maxTokens": 8192, "description": "Krachtige API model voor complexe taken", "systemPrompt": "<PERSON> <PERSON>, Nederlandse AI developer. Antwoord professioneel in het Nederlands."}, {"name": "gpt-3.5-turbo", "capabilities": ["chat", "email", "analysis"], "priority": 11, "maxTokens": 4096, "description": "Snelle API model voor eenvoudige taken"}]}, "task_preferences": {"coding": ["coding_server", "chat_server", "gpt-4"], "analysis": ["coding_server", "chat_server", "gpt-4"], "planning": ["coding_server", "chat_server", "gpt-4"], "chat": ["chat_server", "coding_server", "gpt-3.5-turbo"], "email": ["coding_server", "gpt-4", "gpt-3.5-turbo"], "commit_messages": ["coding_server", "chat_server"], "bug_analysis": ["chat_server", "coding_server", "gpt-4"], "debugging": ["chat_server", "coding_server"], "refactoring": ["chat_server", "coding_server"], "code_review": ["chat_server", "coding_server"]}, "workflow_settings": {"auto_detect_changes": true, "smart_commit_messages": true, "auto_scan_errors": true, "github_integration": true, "prefer_local_ai": true, "notifications": {"enabled": false, "channels": []}}}