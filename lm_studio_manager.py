#!/usr/bin/env python3
"""
LM Studio Multi-Model Manager voor Yannova CRM
Beheert meerdere LM Studio servers voor gespecialiseerde AI taken
"""

import requests
import json
import time
from typing import Dict, List, Optional

class LMStudioManager:
    def __init__(self):
        """Initialiseer de LM Studio Multi-Model Manager"""
        self.servers = {
            'coding_server': {
                'url': 'http://127.0.0.1:1234/v1',
                'port': 1234,
                'model': 'mistral-7b-instruct-v0.1',
                'capabilities': ['coding', 'analysis', 'planning', 'commit_messages'],
                'status': 'unknown'
            },
            'chat_server': {
                'url': 'http://127.0.0.1:1235/v1', 
                'port': 1235,
                'model': 'deepseek-coder-6.7b-instruct',
                'capabilities': ['debugging', 'refactoring', 'code_review', 'chat'],
                'status': 'unknown'
            }
        }
        
    def check_server_status(self, server_name: str) -> bool:
        """Controleer of een LM Studio server actief is"""
        server = self.servers.get(server_name)
        if not server:
            return False
            
        try:
            response = requests.get(f"{server['url']}/models", timeout=5)
            if response.status_code == 200:
                self.servers[server_name]['status'] = 'active'
                return True
            else:
                self.servers[server_name]['status'] = 'inactive'
                return False
        except Exception as e:
            self.servers[server_name]['status'] = 'error'
            return False
    
    def check_all_servers(self) -> Dict[str, bool]:
        """Controleer status van alle servers"""
        status = {}
        for server_name in self.servers.keys():
            status[server_name] = self.check_server_status(server_name)
        return status
    
    def get_best_server_for_task(self, task_type: str) -> Optional[str]:
        """Bepaal de beste server voor een specifieke taak"""
        task_preferences = {
            'coding': ['coding_server', 'chat_server'],
            'analysis': ['coding_server', 'chat_server'],
            'planning': ['coding_server', 'chat_server'],
            'chat': ['chat_server', 'coding_server'],
            'commit_messages': ['coding_server', 'chat_server'],
            'bug_analysis': ['chat_server', 'coding_server'],
            'debugging': ['chat_server', 'coding_server'],
            'refactoring': ['chat_server', 'coding_server'],
            'code_review': ['chat_server', 'coding_server']
        }
        
        preferred_servers = task_preferences.get(task_type, ['coding_server', 'chat_server'])
        
        # Zoek eerste beschikbare server
        for server_name in preferred_servers:
            if self.check_server_status(server_name):
                return server_name
        
        return None
    
    def query_server(self, server_name: str, messages: List[Dict], temperature: float = 0.7) -> Optional[str]:
        """Stuur een query naar een specifieke server"""
        server = self.servers.get(server_name)
        if not server or not self.check_server_status(server_name):
            return None
            
        try:
            response = requests.post(
                f"{server['url']}/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer lm-studio"
                },
                json={
                    "model": server['model'],
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": 1000,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data['choices'][0]['message']['content']
            else:
                return None
                
        except Exception as e:
            print(f"❌ Fout bij query naar {server_name}: {str(e)}")
            return None
    
    def smart_query(self, task_type: str, messages: List[Dict], temperature: float = 0.7) -> Optional[str]:
        """Intelligente query die de beste server kiest voor de taak"""
        best_server = self.get_best_server_for_task(task_type)
        
        if best_server:
            print(f"🎯 Gebruik {best_server} voor {task_type}")
            return self.query_server(best_server, messages, temperature)
        else:
            print(f"❌ Geen beschikbare server voor {task_type}")
            return None
    
    def print_status(self):
        """Print status van alle servers"""
        print("\n" + "="*60)
        print("🚀 LM STUDIO MULTI-MODEL STATUS")
        print("="*60)
        
        status = self.check_all_servers()
        
        for server_name, server_info in self.servers.items():
            is_active = status[server_name]
            status_icon = "✅" if is_active else "❌"
            status_text = "ACTIEF" if is_active else "INACTIEF"
            
            print(f"\n📡 {server_info['model']}")
            print(f"   Status: {status_icon} {status_text}")
            print(f"   URL: {server_info['url']}")
            print(f"   Poort: {server_info['port']}")
            print(f"   Capabilities: {', '.join(server_info['capabilities'])}")
        
        active_count = sum(status.values())
        print(f"\n📊 Totaal: {active_count}/{len(self.servers)} servers actief")
        
        if active_count == 0:
            print("\n⚠️  GEEN SERVERS ACTIEF!")
            print("💡 Start LM Studio en laad modellen op poort 1234 en 1235")
        elif active_count == 1:
            print("\n⚠️  SLECHTS 1 SERVER ACTIEF")
            print("💡 Start een tweede model voor optimale performance")
        else:
            print("\n🎉 MULTI-MODEL SETUP ACTIEF!")
            print("💡 Alle servers draaien - optimale AI performance!")
        
        print("="*60)
    
    def test_both_servers(self):
        """Test beide servers met voorbeeldqueries"""
        print("\n🧪 TESTEN VAN BEIDE SERVERS...")
        
        # Test coding server
        coding_response = self.smart_query(
            'coding',
            [{"role": "user", "content": "Schrijf een korte Python functie om een lijst te sorteren"}]
        )
        
        if coding_response:
            print(f"\n✅ Coding Server Response:")
            print(f"   {coding_response[:100]}...")
        
        # Test chat server  
        chat_response = self.smart_query(
            'debugging',
            [{"role": "user", "content": "Leg uit wat een null pointer exception is"}]
        )
        
        if chat_response:
            print(f"\n✅ Chat Server Response:")
            print(f"   {chat_response[:100]}...")

def main():
    """Hoofdfunctie voor LM Studio Manager"""
    manager = LMStudioManager()
    
    print("🚀 LM Studio Multi-Model Manager")
    print("Controleren van server status...")
    
    manager.print_status()
    
    # Test servers als beide actief zijn
    status = manager.check_all_servers()
    if sum(status.values()) >= 1:
        manager.test_both_servers()
    
    print("\n💡 SETUP INSTRUCTIES:")
    print("1. Open LM Studio")
    print("2. Laad Mistral 7B op poort 1234 (coding)")
    print("3. Laad DeepSeek Coder op poort 1235 (debugging)")
    print("4. Test met: python lm_studio_manager.py")

if __name__ == "__main__":
    main()
