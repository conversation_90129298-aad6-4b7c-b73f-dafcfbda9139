import { Pool } from 'pg';
import { validateEnv } from '../utils/envValidator';

// Valideer eerst environment
validateEnv();

const pool = new Pool({
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
  ssl: process.env.NODE_ENV === 'production' 
    ? { rejectUnauthorized: true } 
    : false
});

// Database health check
export async function checkDbConnection() {
  try {
    await pool.query('SELECT NOW()');
    return true;
  } catch (error) {
    console.error('❌ Database connection error:', error);
    return false;
  }
}

// Automatische health check bij opstart
checkDbConnection().then(connected => {
  if (!connected) {
    console.error('❌ Kritieke fout: Kan niet verbinden met database');
    process.exit(1);
  }
});

export default pool; 