// Valideert kritieke environment variabelen
const requiredVars = [
  'POSTGRES_HOST', 
  'POSTGRES_DB',
  'OPENAI_API_KEY',
  'RESEND_API_KEY'
];

export function validateEnv() {
  const missing: string[] = [];
  
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  if (missing.length > 0) {
    const errorMsg = `❌ Ontbrekende environment variabelen: ${missing.join(', ')}`;
    console.error(errorMsg);
    throw new Error(errorMsg);
  }
  
  console.log('✅ Environment configuratie is geldig');
} 