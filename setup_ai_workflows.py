#!/usr/bin/env python3
"""
Setup script voor Yannova CRM AI Workflows
Configureert alle benodigde instellingen en dependencies
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def print_header():
    """Print een mooie header"""
    print("=" * 60)
    print("🚀 YANNOVA CRM AI WORKFLOW SETUP")
    print("=" * 60)
    print()

def check_requirements():
    """Controleer of alle vereisten aanwezig zijn"""
    print("📋 Controleren van vereisten...")
    
    requirements = {
        'python': True,
        'git': False,
        'node': False,
        'npm': False
    }
    
    # Check Python
    try:
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            print(f"✅ Python {python_version.major}.{python_version.minor} gevonden")
        else:
            print(f"❌ Python 3.8+ vereist, gevonden: {python_version.major}.{python_version.minor}")
            requirements['python'] = False
    except:
        print("❌ Python niet gevonden")
        requirements['python'] = False
    
    # Check Git
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git gevonden")
            requirements['git'] = True
        else:
            print("❌ Git niet gevonden")
    except:
        print("❌ Git niet gevonden")
    
    # Check Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js gevonden: {result.stdout.strip()}")
            requirements['node'] = True
        else:
            print("❌ Node.js niet gevonden")
    except:
        print("❌ Node.js niet gevonden")
    
    # Check npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm gevonden: {result.stdout.strip()}")
            requirements['npm'] = True
        else:
            print("❌ npm niet gevonden")
    except:
        print("❌ npm niet gevonden")
    
    print()
    return all(requirements.values())

def setup_environment():
    """Setup Python virtual environment en installeer dependencies"""
    print("🔧 Instellen van Python omgeving...")
    
    # Check of virtual environment al bestaat
    if os.path.exists('ai-agents-env'):
        print("✅ Virtual environment bestaat al")
    else:
        print("📦 Aanmaken van virtual environment...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', 'ai-agents-env'], check=True)
            print("✅ Virtual environment aangemaakt")
        except subprocess.CalledProcessError:
            print("❌ Fout bij aanmaken virtual environment")
            return False
    
    # Activeer virtual environment en installeer packages
    if os.name == 'nt':  # Windows
        pip_path = 'ai-agents-env\\Scripts\\pip'
        python_path = 'ai-agents-env\\Scripts\\python'
    else:  # Unix/Linux/macOS
        pip_path = 'ai-agents-env/bin/pip'
        python_path = 'ai-agents-env/bin/python'
    
    print("📦 Installeren van Python packages...")
    packages = [
        'openai',
        'composio_openai',
        'requests',
        'python-dotenv',
        'gitpython',
        'schedule'
    ]
    
    for package in packages:
        try:
            print(f"   Installing {package}...")
            subprocess.run([pip_path, 'install', package], 
                         check=True, capture_output=True)
            print(f"   ✅ {package} geïnstalleerd")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Fout bij installeren {package}: {e}")
            return False
    
    print("✅ Alle Python packages geïnstalleerd")
    return True

def setup_environment_file():
    """Maak .env bestand aan voor API keys"""
    print("🔐 Instellen van environment variabelen...")
    
    env_file = '.env'
    env_template = """# Yannova CRM AI Workflow Environment Variables
# Vul je API keys in:

# OpenAI API Key (vereist voor AI functionaliteit)
OPENAI_API_KEY=your_openai_api_key_here

# Composio API Key (automatisch ingesteld via composio login)
COMPOSIO_API_KEY=your_composio_api_key_here

# GitHub Token (optioneel, voor geavanceerde GitHub integratie)
GITHUB_TOKEN=your_github_token_here

# Notification Settings (optioneel)
SLACK_WEBHOOK_URL=your_slack_webhook_here
DISCORD_WEBHOOK_URL=your_discord_webhook_here

# Email Settings (optioneel)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Project Settings
PROJECT_NAME=Yannova CRM
ENVIRONMENT=development
"""
    
    if os.path.exists(env_file):
        print("✅ .env bestand bestaat al")
        return True
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_template)
        print("✅ .env template bestand aangemaakt")
        print("⚠️  BELANGRIJK: Vul je API keys in het .env bestand in!")
        return True
    except Exception as e:
        print(f"❌ Fout bij aanmaken .env bestand: {e}")
        return False

def create_workflow_scripts():
    """Maak handige workflow scripts aan"""
    print("📝 Aanmaken van workflow scripts...")
    
    # Quick start script
    quick_start = """#!/bin/bash
# Quick start script voor Yannova CRM AI Workflows

echo "🚀 Starting Yannova CRM AI Workflow..."

# Activeer virtual environment
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    source ai-agents-env/Scripts/activate
else
    source ai-agents-env/bin/activate
fi

# Run de workflow
python ai_workflow_manager.py

echo "✅ Workflow completed!"
"""
    
    try:
        with open('run_workflow.sh', 'w') as f:
            f.write(quick_start)
        
        # Maak executable op Unix systemen
        if os.name != 'nt':
            os.chmod('run_workflow.sh', 0o755)
        
        print("✅ run_workflow.sh script aangemaakt")
    except Exception as e:
        print(f"❌ Fout bij aanmaken workflow script: {e}")
    
    # Windows batch file
    windows_batch = """@echo off
echo 🚀 Starting Yannova CRM AI Workflow...

REM Activeer virtual environment
call ai-agents-env\\Scripts\\activate.bat

REM Run de workflow
python ai_workflow_manager.py

echo ✅ Workflow completed!
pause
"""
    
    try:
        with open('run_workflow.bat', 'w') as f:
            f.write(windows_batch)
        print("✅ run_workflow.bat script aangemaakt")
    except Exception as e:
        print(f"❌ Fout bij aanmaken Windows batch script: {e}")

def setup_git_hooks():
    """Setup Git hooks voor automatische workflows"""
    print("🔗 Instellen van Git hooks...")
    
    git_hooks_dir = '.git/hooks'
    if not os.path.exists(git_hooks_dir):
        print("⚠️  Git repository niet gevonden, Git hooks overgeslagen")
        return
    
    # Pre-commit hook
    pre_commit_hook = """#!/bin/sh
# Yannova CRM AI Workflow Pre-commit Hook

echo "🔍 Running AI workflow pre-commit checks..."

# Activeer virtual environment en run quick scan
if [ -f "ai-agents-env/bin/activate" ]; then
    source ai-agents-env/bin/activate
    python -c "
from ai_workflow_manager import YannovaCRMWorkflowManager
manager = YannovaCRMWorkflowManager()
errors = manager.scan_for_errors()
if len(errors) > 5:
    print(f'⚠️  {len(errors)} potential issues found. Consider reviewing before commit.')
    for error in errors[:3]:
        print(f'   - {error}')
"
fi

echo "✅ Pre-commit checks completed"
"""
    
    try:
        hook_file = os.path.join(git_hooks_dir, 'pre-commit')
        with open(hook_file, 'w') as f:
            f.write(pre_commit_hook)
        
        # Maak executable
        if os.name != 'nt':
            os.chmod(hook_file, 0o755)
        
        print("✅ Git pre-commit hook geïnstalleerd")
    except Exception as e:
        print(f"❌ Fout bij instellen Git hooks: {e}")

def print_next_steps():
    """Print volgende stappen voor de gebruiker"""
    print()
    print("=" * 60)
    print("🎉 SETUP VOLTOOID!")
    print("=" * 60)
    print()
    print("📋 VOLGENDE STAPPEN:")
    print()
    print("1. 🔐 Configureer je API keys in het .env bestand:")
    print("   - OpenAI API key (vereist)")
    print("   - GitHub token (optioneel)")
    print()
    print("2. 🚀 Test de workflow:")
    print("   - Linux/macOS: ./run_workflow.sh")
    print("   - Windows: run_workflow.bat")
    print("   - Of direct: python ai_workflow_manager.py")
    print()
    print("3. ⚙️  Pas instellingen aan in workflow_config.json")
    print()
    print("4. 📖 Lees de documentatie voor geavanceerde features")
    print()
    print("=" * 60)
    print("💡 TIP: Start met 'python ai_workflow_manager.py' om de workflow te testen!")
    print("=" * 60)

def main():
    """Hoofdfunctie voor setup"""
    print_header()
    
    # Check of we in de juiste directory zijn
    if not os.path.exists('package.json'):
        print("❌ Fout: Voer dit script uit vanuit de root directory van je Yannova CRM project")
        return False
    
    # Controleer vereisten
    if not check_requirements():
        print("❌ Niet alle vereisten zijn aanwezig. Installeer ontbrekende software en probeer opnieuw.")
        return False
    
    # Setup stappen
    steps = [
        ("Python omgeving", setup_environment),
        ("Environment variabelen", setup_environment_file),
        ("Workflow scripts", create_workflow_scripts),
        ("Git hooks", setup_git_hooks)
    ]
    
    for step_name, step_func in steps:
        print(f"🔄 {step_name}...")
        if not step_func():
            print(f"❌ Fout bij {step_name}")
            return False
        print()
    
    print_next_steps()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
