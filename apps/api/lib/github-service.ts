import { Octokit } from '@octokit/rest';

export interface GitHubConfig {
  token: string;
  owner: string;
  repo: string;
}

export interface GitHubIssue {
  number: number;
  title: string;
  body: string;
  state: 'open' | 'closed';
  labels: string[];
  assignee?: string;
  created_at: string;
  updated_at: string;
}

export interface GitHubCommit {
  sha: string;
  message: string;
  author: string;
  date: string;
  files: string[];
}

export class GitHubService {
  private octokit: Octokit;
  private config: GitHubConfig;

  constructor(config: GitHubConfig) {
    this.config = config;
    this.octokit = new Octokit({
      auth: config.token,
    });
  }

  // Repository Analysis
  async analyzeRepository(): Promise<{
    stats: any;
    recent_commits: GitHubCommit[];
    open_issues: GitHubIssue[];
    languages: any;
  }> {
    try {
      const [repo, commits, issues, languages] = await Promise.all([
        this.octokit.repos.get({
          owner: this.config.owner,
          repo: this.config.repo,
        }),
        this.getRecentCommits(10),
        this.getOpenIssues(),
        this.octokit.repos.listLanguages({
          owner: this.config.owner,
          repo: this.config.repo,
        }),
      ]);

      return {
        stats: {
          stars: repo.data.stargazers_count,
          forks: repo.data.forks_count,
          open_issues: repo.data.open_issues_count,
          size: repo.data.size,
          default_branch: repo.data.default_branch,
          created_at: repo.data.created_at,
          updated_at: repo.data.updated_at,
        },
        recent_commits: commits,
        open_issues: issues,
        languages: languages.data,
      };
    } catch (error) {
      console.error('Error analyzing repository:', error);
      throw error;
    }
  }

  // Get Recent Commits
  async getRecentCommits(count: number = 10): Promise<GitHubCommit[]> {
    try {
      const { data } = await this.octokit.repos.listCommits({
        owner: this.config.owner,
        repo: this.config.repo,
        per_page: count,
      });

      return data.map(commit => ({
        sha: commit.sha,
        message: commit.commit.message,
        author: commit.commit.author?.name || 'Unknown',
        date: commit.commit.author?.date || '',
        files: [], // We'd need another API call for files
      }));
    } catch (error) {
      console.error('Error fetching commits:', error);
      throw error;
    }
  }

  // Get Open Issues
  async getOpenIssues(): Promise<GitHubIssue[]> {
    try {
      const { data } = await this.octokit.issues.listForRepo({
        owner: this.config.owner,
        repo: this.config.repo,
        state: 'open',
        per_page: 20,
      });

      return data.map(issue => ({
        number: issue.number,
        title: issue.title,
        body: issue.body || '',
        state: issue.state as 'open' | 'closed',
        labels: issue.labels.map(label => 
          typeof label === 'string' ? label : label.name || ''
        ),
        assignee: issue.assignee?.login,
        created_at: issue.created_at,
        updated_at: issue.updated_at,
      }));
    } catch (error) {
      console.error('Error fetching issues:', error);
      throw error;
    }
  }

  // Create Issue
  async createIssue(title: string, body: string, labels?: string[]): Promise<GitHubIssue> {
    try {
      const { data } = await this.octokit.issues.create({
        owner: this.config.owner,
        repo: this.config.repo,
        title,
        body,
        labels,
      });

      return {
        number: data.number,
        title: data.title,
        body: data.body || '',
        state: data.state as 'open' | 'closed',
        labels: data.labels.map(label => 
          typeof label === 'string' ? label : label.name || ''
        ),
        assignee: data.assignee?.login,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } catch (error) {
      console.error('Error creating issue:', error);
      throw error;
    }
  }

  // Update Issue
  async updateIssue(issueNumber: number, updates: {
    title?: string;
    body?: string;
    state?: 'open' | 'closed';
    labels?: string[];
  }): Promise<GitHubIssue> {
    try {
      const { data } = await this.octokit.issues.update({
        owner: this.config.owner,
        repo: this.config.repo,
        issue_number: issueNumber,
        ...updates,
      });

      return {
        number: data.number,
        title: data.title,
        body: data.body || '',
        state: data.state as 'open' | 'closed',
        labels: data.labels.map(label => 
          typeof label === 'string' ? label : label.name || ''
        ),
        assignee: data.assignee?.login,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } catch (error) {
      console.error('Error updating issue:', error);
      throw error;
    }
  }

  // Get File Content
  async getFileContent(path: string, ref?: string): Promise<string> {
    try {
      const { data } = await this.octokit.repos.getContent({
        owner: this.config.owner,
        repo: this.config.repo,
        path,
        ref,
      });

      if ('content' in data) {
        return Buffer.from(data.content, 'base64').toString('utf-8');
      }
      throw new Error('File not found or is a directory');
    } catch (error) {
      console.error('Error fetching file content:', error);
      throw error;
    }
  }

  // Create or Update File
  async createOrUpdateFile(
    path: string,
    content: string,
    message: string,
    sha?: string
  ): Promise<void> {
    try {
      await this.octokit.repos.createOrUpdateFileContents({
        owner: this.config.owner,
        repo: this.config.repo,
        path,
        message,
        content: Buffer.from(content).toString('base64'),
        sha,
      });
    } catch (error) {
      console.error('Error creating/updating file:', error);
      throw error;
    }
  }

  // AI-Enhanced Code Review
  async performCodeReview(commitSha: string): Promise<{
    files: any[];
    suggestions: string[];
    security_issues: string[];
    performance_notes: string[];
  }> {
    try {
      const { data } = await this.octokit.repos.getCommit({
        owner: this.config.owner,
        repo: this.config.repo,
        ref: commitSha,
      });

      const files = data.files || [];
      const suggestions: string[] = [];
      const security_issues: string[] = [];
      const performance_notes: string[] = [];

      // Basic static analysis
      for (const file of files) {
        const patch = file.patch || '';
        
        // Security checks
        if (patch.includes('eval(') || patch.includes('innerHTML')) {
          security_issues.push(`Possible security issue in ${file.filename}: avoid eval() or innerHTML`);
        }
        
        // Performance checks
        if (patch.includes('for (') && patch.includes('.length')) {
          performance_notes.push(`Performance tip in ${file.filename}: cache array length in loops`);
        }
        
        // General suggestions
        if (patch.includes('console.log')) {
          suggestions.push(`Remove console.log statements in ${file.filename} before production`);
        }
      }

      return {
        files: files.map(f => ({
          filename: f.filename,
          additions: f.additions,
          deletions: f.deletions,
          changes: f.changes,
          status: f.status,
        })),
        suggestions,
        security_issues,
        performance_notes,
      };
    } catch (error) {
      console.error('Error performing code review:', error);
      throw error;
    }
  }
}

export default GitHubService; 