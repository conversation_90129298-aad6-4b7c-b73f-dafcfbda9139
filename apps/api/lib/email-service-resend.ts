export class ResendEmailService {
  private static readonly RESEND_API_KEY = process.env.RESEND_API_KEY;
  private static readonly FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';

  static async sendEmail(to: string, subject: string, content: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          from: `Yannova <${this.FROM_EMAIL}>`,
          to: [to],
          subject: subject,
          html: content
        })
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Resend API error:', error);
        return false;
      }

      const result = await response.json();
      console.log('Email sent successfully via Resend:', result.id);
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  static async sendBulkEmails(recipients: string[], subject: string, content: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.resend.com/emails/batch', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(
          recipients.map(to => ({
            from: `Yannova <${this.FROM_EMAIL}>`,
            to: [to],
            subject: subject,
            html: content
          }))
        )
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Resend batch API error:', error);
        return false;
      }

      const result = await response.json();
      console.log('Bulk emails sent successfully:', result);
      return true;
    } catch (error) {
      console.error('Error sending bulk emails:', error);
      return false;
    }
  }

  static async sendWithTemplate(to: string, templateId: string, variables: Record<string, any>): Promise<boolean> {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          from: `Yannova <${this.FROM_EMAIL}>`,
          to: [to],
          template: templateId,
          data: variables
        })
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Resend template error:', error);
        return false;
      }

      const result = await response.json();
      console.log('Template email sent successfully:', result.id);
      return true;
    } catch (error) {
      console.error('Error sending template email:', error);
      return false;
    }
  }
} 