export interface ModelConfig {
  name: string;
  displayName: string;
  type: 'local' | 'api';
  endpoint?: string;
  capabilities: string[];
  priority: number;
  maxTokens: number;
  description: string;
  systemPrompt?: string;
}

export const MODEL_CONFIGS: { [key: string]: ModelConfig } = {
  // Lokale modellen (LM Studio)
  'mistral-code': {
    name: 'mistral-7b-instruct-v0.1',
    displayName: 'Mistral 7B (Code)',
    type: 'local',
    endpoint: 'http://127.0.0.1:1234/v1',
    capabilities: ['coding', 'analysis', 'planning', 'chat'],
    priority: 1,
    maxTokens: 4096,
    description: 'Snelle lokale AI voor coding en algemene taken',
    systemPrompt: 'Je bent een Nederlandse AI developer assistant. Antwoord altijd in het Nederlands.'
  },
  
  'deepseek-coder': {
    name: 'deepseek-coder-6.7b-instruct',
    displayName: 'DeepSeek Coder',
    type: 'local',
    endpoint: 'http://127.0.0.1:1234/v1',
    capabilities: ['coding', 'debugging', 'refactoring'],
    priority: 2,
    maxTokens: 4096,
    description: 'Gespecialiseerd model voor code review en debugging',
    systemPrompt: 'Je bent een expert code reviewer. Geef concrete feedback in het Nederlands.'
  },
  
  'llama-chat': {
    name: 'llama-2-7b-chat',
    displayName: 'Llama 2 Chat',
    type: 'local',
    endpoint: 'http://127.0.0.1:1234/v1',
    capabilities: ['chat', 'analysis', 'planning'],
    priority: 3,
    maxTokens: 4096,
    description: 'Conversationele AI voor algemene vragen en planning'
  },
  
  // API modellen (fallback)
  'openai-gpt4': {
    name: 'gpt-4',
    displayName: 'GPT-4',
    type: 'api',
    capabilities: ['coding', 'analysis', 'email', 'chat', 'planning'],
    priority: 4,
    maxTokens: 8192,
    description: 'Krachtige API model voor complexe taken',
    systemPrompt: 'Je bent Leon van Yannova, Nederlandse AI developer. Antwoord professioneel in het Nederlands.'
  },
  
  'openai-gpt3': {
    name: 'gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    type: 'api',
    capabilities: ['chat', 'email', 'analysis'],
    priority: 5,
    maxTokens: 4096,
    description: 'Snelle API model voor eenvoudige taken'
  },
  
  'claude-3': {
    name: 'claude-3-sonnet',
    displayName: 'Claude 3 Sonnet',
    type: 'api',
    capabilities: ['coding', 'analysis', 'planning', 'chat'],
    priority: 6,
    maxTokens: 8192,
    description: 'Anthropic model voor analytische taken'
  }
};

export const TASK_PREFERENCES: { [key: string]: string[] } = {
  'coding': ['mistral-code', 'deepseek-coder', 'openai-gpt4'],
  'analysis': ['mistral-code', 'llama-chat', 'openai-gpt4'],
  'planning': ['mistral-code', 'llama-chat', 'openai-gpt4'],
  'chat': ['llama-chat', 'mistral-code', 'openai-gpt3'],
  'email': ['mistral-code', 'openai-gpt4', 'openai-gpt3']
}; 