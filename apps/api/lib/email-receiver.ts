import { PrismaClient } from '@prisma/client';
import { EmailService } from './email-service';
import { EmailTemplates } from './email-templates';

const prisma = new PrismaClient();

export class EmailReceiver {
  // Process incoming email and save to CRM
  static async processIncomingEmail(
    from: string,
    subject: string,
    content: string,
    messageId?: string
  ) {
    try {
      // 1. Find or create contact
      const contact = await this.findOrCreateContact(from, content);
      
      // 2. Save conversation
      const conversation = await prisma.conversation.create({
        data: {
          contactId: contact.id,
          type: 'EMAIL',
          channel: 'EMAIL',
          subject: subject,
          content: content,
          direction: 'INBOUND',
          sentiment: await EmailService.analyzeSentiment(content)
        }
      });

      // 3. Update last contact date
      await prisma.contact.update({
        where: { id: contact.id },
        data: { lastContact: new Date() }
      });

      // 4. Auto-categorize and respond
      const category = await EmailService.categorizeEmail(subject, content);
      
      // 5. Create tasks based on category
      await this.createTasksFromEmail(contact.id, category, subject);

      // 6. Send auto-reply if needed
      if (category === 'QUOTE' || category === 'URGENT') {
        const response = await EmailService.generateSmartResponse(category, content);
        await EmailService.sendEmail(from, `Re: ${subject}`, response);
        
        // Save outbound conversation
        await prisma.conversation.create({
          data: {
            contactId: contact.id,
            type: 'EMAIL',
            channel: 'EMAIL',
            subject: `Re: ${subject}`,
            content: response,
            direction: 'OUTBOUND'
          }
        });
      }

      console.log(`Email processed from ${from} - Category: ${category}`);
      return { success: true, contactId: contact.id, conversationId: conversation.id };
      
    } catch (error) {
      console.error('Error processing email:', error);
      return { success: false, error };
    }
  }

  // Find existing contact or create new one
  private static async findOrCreateContact(email: string, content: string) {
    let contact = await prisma.contact.findUnique({
      where: { email }
    });

    if (!contact) {
      const contactInfo = EmailService.extractContactInfo(content);
      
      contact = await prisma.contact.create({
        data: {
          email,
          phone: contactInfo.phone,
          company: contactInfo.company,
          source: 'EMAIL',
          status: 'LEAD'
        }
      });

      // Send welcome email to new contact
      const name = email.split('@')[0];
      await EmailTemplates.sendWelcomeEmail(email, { name, email });
    }

    return contact;
  }

  // Create tasks based on email category
  private static async createTasksFromEmail(
    contactId: string,
    category: string,
    subject: string
  ) {
    const taskMap = {
      QUOTE: {
        title: `Offerte opstellen: ${subject}`,
        type: 'QUOTE' as const,
        priority: 'HIGH' as const,
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
      },
      URGENT: {
        title: `URGENT: ${subject}`,
        type: 'FOLLOWUP' as const,
        priority: 'URGENT' as const,
        dueDate: new Date(Date.now() + 4 * 60 * 60 * 1000) // 4 hours
      },
      BOT: {
        title: `Bot aanvraag: ${subject}`,
        type: 'DEMO' as const,
        priority: 'MEDIUM' as const,
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days
      },
      GENERAL: {
        title: `Follow-up: ${subject}`,
        type: 'FOLLOWUP' as const,
        priority: 'MEDIUM' as const,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 1 week
      }
    };

    const taskData = taskMap[category] || taskMap.GENERAL;
    
    await prisma.task.create({
      data: {
        contactId,
        ...taskData,
        description: `Automatisch aangemaakt vanuit email categorie: ${category}`
      }
    });
  }

  // Get all conversations for a contact
  static async getContactConversations(contactId: string) {
    return await prisma.conversation.findMany({
      where: { contactId },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Mark conversation as read
  static async markAsRead(conversationId: string) {
    return await prisma.conversation.update({
      where: { id: conversationId },
      data: { readAt: new Date() }
    });
  }

  // Get unread count
  static async getUnreadCount() {
    return await prisma.conversation.count({
      where: { 
        readAt: null,
        direction: 'INBOUND'
      }
    });
  }
} 