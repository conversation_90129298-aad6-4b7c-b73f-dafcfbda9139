import { WhatsAppApi } from '@whatsapp-business/api';

export class WhatsAppService {
  private api: WhatsAppApi;
  
  constructor() {
    this.api = new WhatsAppApi({
      accessToken: process.env.WHATSAPP_ACCESS_TOKEN!,
      businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID!
    });
  }
  
  async sendMessage(phoneNumber: string, message: string) {
    return await this.api.messages.send({
      to: phoneNumber,
      type: 'text',
      text: { body: message }
    });
  }
  
  async sendTemplate(phoneNumber: string, templateName: string, params: string[]) {
    return await this.api.messages.send({
      to: phoneNumber,
      type: 'template',
      template: {
        name: templateName,
        language: { code: 'nl' },
        components: [{
          type: 'body',
          parameters: params.map(p => ({ type: 'text', text: p }))
        }]
      }
    });
  }
}