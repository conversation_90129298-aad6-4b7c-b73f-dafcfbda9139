import { exec } from 'child_process';

export async function queryMemoryBank(query: string) {
  return new Promise((resolve, reject) => {
    exec(`npx @allpepper/memory-bank-mcp query "${query}"`, 
      { 
        env: { 
          ...process.env,  // Voeg alle bestaande env variabelen toe
          MEMORY_BANK_ROOT: process.env.MEMORY_BANK_ROOT 
        } 
      },
      (error, stdout, stderr) => {
        if (error) reject(error);
        else resolve(stdout);
      }
    );
  });
} 