import axios from 'axios';

export async function queryLMStudio(prompt: string, model = 'mistral-7b-instruct-v0.1') {
  try {
    const response = await axios.post('http://127.0.0.1:1234/v1/chat/completions', {
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 500
    }, {
      headers: { 'Authorization': 'Bearer lm-studio' },
      timeout: 30000  // 30 seconden timeout
    });
    
    return response.data.choices[0]?.message?.content || 'Geen antwoord ontvangen';
  } catch (error) {
    console.error('LM Studio fout:', error.response?.data || error.message);
    return 'Fout bij verwerken verzoek';
  }
} 