// <PERSON> ↔ LM Studio Bridge
// Deze functie laat Claude commu<PERSON>ere<PERSON> met jouw lokale LM Studio

export interface ClaudeLMRequest {
  prompt: string;
  taskType: 'coding' | 'planning' | 'analysis' | 'chat' | 'debug' | 'optimize' | 'architecture';
  language?: string;
  context?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface ClaudeLMResponse {
  success: boolean;
  result?: {
    response: string;
    model: string;
    provider: string;
    timestamp: string;
  };
  error?: string;
  metadata?: {
    promptLength: number;
    responseLength: number;
    processingTime: string;
    modelUsed: string;
    provider: string;
  };
}

export class ClaudeLMBridge {
  private baseUrl: string;
  
  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }
  
  // Hoofdfunctie die Claude gebruikt om LM Studio aan te roepen
  async queryLMStudio(request: ClaudeLMRequest): Promise<ClaudeLMResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/claude-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          requester: 'claude'
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data;
      
    } catch (error) {
      console.error('Claude→LM Studio bridge error:', error);
      return {
        success: false,
        error: error.message || 'Failed to connect to LM Studio'
      };
    }
  }
  
  // Specifieke methoden voor verschillende taken
  async askForCode(prompt: string, language = 'typescript'): Promise<string> {
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'coding',
      language,
      priority: 'high'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to get code from LM Studio');
  }
  
  async askForPlan(prompt: string, context?: string): Promise<string> {
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'planning',
      context,
      priority: 'high'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to get plan from LM Studio');
  }
  
  async askForAnalysis(prompt: string, context?: string): Promise<string> {
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'analysis',
      context,
      priority: 'medium'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to get analysis from LM Studio');
  }
  
  async askQuestion(prompt: string, context?: string): Promise<string> {
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'chat',
      context,
      priority: 'medium'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to get answer from LM Studio');
  }
  
  async debugCode(code: string, language = 'typescript'): Promise<string> {
    const prompt = `Debug deze ${language} code en vind problemen:\n\n${code}`;
    
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'debug',
      language,
      priority: 'high'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to debug code with LM Studio');
  }
  
  async optimizeCode(code: string, language = 'typescript'): Promise<string> {
    const prompt = `Optimaliseer deze ${language} code voor performance:\n\n${code}`;
    
    const response = await this.queryLMStudio({
      prompt,
      taskType: 'optimize',
      language,
      priority: 'high'
    });
    
    if (response.success && response.result) {
      return response.result.response;
    }
    
    throw new Error(response.error || 'Failed to optimize code with LM Studio');
  }
  
  // Health check
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.queryLMStudio({
        prompt: 'Hello, test connection',
        taskType: 'chat',
        priority: 'low'
      });
      
      return response.success;
    } catch (error) {
      return false;
    }
  }
}

// Singleton instance die Claude kan gebruiken
export const claudeLMBridge = new ClaudeLMBridge();

// Convenience functies voor directe toegang
export const askLMStudioForCode = (prompt: string, language?: string) => 
  claudeLMBridge.askForCode(prompt, language);

export const askLMStudioForPlan = (prompt: string, context?: string) => 
  claudeLMBridge.askForPlan(prompt, context);

export const askLMStudioForAnalysis = (prompt: string, context?: string) => 
  claudeLMBridge.askForAnalysis(prompt, context);

export const askLMStudioQuestion = (prompt: string, context?: string) => 
  claudeLMBridge.askQuestion(prompt, context); 