{"name": "yannova-crm-api", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3001", "start": "next start -p 3001", "lint": "next lint", "db:generate": "prisma generate --schema=../../packages/database/schema.prisma", "db:push": "prisma db push --schema=../../packages/database/schema.prisma"}, "dependencies": {"@octokit/rest": "^22.0.0", "@prisma/client": "^5.6.0", "@react-email/components": "^0.1.1", "@react-email/render": "^1.1.3", "@types/node-telegram-bot-api": "^0.64.0", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "dotenv": "^17.0.1", "next": "14.0.0", "node-telegram-bot-api": "^0.64.0", "nodemailer": "^7.0.4", "openai": "^5.8.2", "react": "^18", "react-dom": "^18", "react-email": "^4.0.17"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.0", "typescript": "^5"}}