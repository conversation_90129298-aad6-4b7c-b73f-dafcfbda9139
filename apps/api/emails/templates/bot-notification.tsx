import { Text, Section } from '@react-email/components';
import * as React from 'react';
import BaseTemplate from './base-template';

interface BotNotificationProps {
  botName: string;
  eventType: 'message' | 'error' | 'alert' | 'update';
  userName?: string;
  message: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export const BotNotification = ({
  botName,
  eventType,
  userName,
  message,
  timestamp,
  metadata,
}: BotNotificationProps) => {
  const getEventIcon = () => {
    switch (eventType) {
      case 'message': return '💬';
      case 'error': return '❌';
      case 'alert': return '⚠️';
      case 'update': return '🔄';
      default: return '📢';
    }
  };

  const getEventColor = () => {
    switch (eventType) {
      case 'message': return '#7AA2F7';
      case 'error': return '#f85149';
      case 'alert': return '#ffa657';
      case 'update': return '#2ea043';
      default: return '#525f7f';
    }
  };

  return (
    <BaseTemplate
      preview={`${getEventIcon()} ${botName} - ${eventType}`}
      heading={`Bot Notification`}
    >
      <Section style={notificationBox}>
        <Text style={{ ...eventHeader, color: getEventColor() }}>
          {getEventIcon()} {eventType.toUpperCase()} - {botName}
        </Text>

        {userName && (
          <Text style={userInfo}>
            👤 Gebruiker: <strong>{userName}</strong>
          </Text>
        )}

        <Text style={messageStyle}>
          {message}
        </Text>

        <Text style={timestampStyle}>
          ⏰ {new Date(timestamp).toLocaleString('nl-NL')}
        </Text>
      </Section>

      {metadata && Object.keys(metadata).length > 0 && (
        <Section style={metadataSection}>
          <Text style={metadataHeader}>Details</Text>
          <pre style={codeBlock}>
            {JSON.stringify(metadata, null, 2)}
          </pre>
        </Section>
      )}

      <Text style={helpText}>
        Dit is een automatische notificatie van je {botName} bot.
        Bekijk het dashboard voor meer details.
      </Text>
    </BaseTemplate>
  );
};

// Styles
const notificationBox = {
  backgroundColor: '#f6f9fc',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #e6ebf1',
};

const eventHeader = {
  fontSize: '18px',
  fontWeight: '600',
  marginBottom: '12px',
  textTransform: 'capitalize' as const,
};

const userInfo = {
  fontSize: '14px',
  color: '#525f7f',
  marginBottom: '8px',
};

const messageStyle = {
  fontSize: '16px',
  color: '#0D1117',
  lineHeight: '24px',
  margin: '16px 0',
  padding: '16px',
  backgroundColor: '#ffffff',
  borderRadius: '6px',
  border: '1px solid #e6ebf1',
};

const timestampStyle = {
  fontSize: '12px',
  color: '#8898aa',
  marginTop: '12px',
};

const metadataSection = {
  margin: '24px 0',
};

const metadataHeader = {
  fontSize: '16px',
  fontWeight: '600',
  color: '#0D1117',
  marginBottom: '12px',
};

const codeBlock = {
  backgroundColor: '#0D1117',
  color: '#c9d1d9',
  padding: '16px',
  borderRadius: '6px',
  fontSize: '13px',
  fontFamily: 'JetBrains Mono, monospace',
  overflow: 'auto',
};

const helpText = {
  fontSize: '14px',
  color: '#8898aa',
  textAlign: 'center' as const,
  marginTop: '24px',
};

export default BotNotification; 