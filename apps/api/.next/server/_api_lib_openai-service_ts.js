"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_api_lib_openai-service_ts";
exports.ids = ["_api_lib_openai-service_ts"];
exports.modules = {

/***/ "(api)/./lib/lmstudio-service.ts":
/*!*********************************!*\
  !*** ./lib/lmstudio-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryLMStudio: () => (/* binding */ queryLMStudio)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function queryLMStudio(prompt, model = \"mistral-7b-instruct-v0.1\") {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"http://127.0.0.1:1234/v1/chat/completions\", {\n            model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        }, {\n            headers: {\n                \"Authorization\": \"Bearer lm-studio\"\n            },\n            timeout: 30000 // 30 seconden timeout\n        });\n        return response.data.choices[0]?.message?.content || \"Geen antwoord ontvangen\";\n    } catch (error) {\n        console.error(\"LM Studio fout:\", error.response?.data || error.message);\n        return \"Fout bij verwerken verzoek\";\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUVuQixlQUFlQyxjQUFjQyxNQUFjLEVBQUVDLFFBQVEsMEJBQTBCO0lBQ3BGLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1KLGtEQUFVLENBQUMsNkNBQTZDO1lBQzdFRztZQUNBRyxVQUFVO2dCQUFDO29CQUFFQyxNQUFNO29CQUFRQyxTQUFTTjtnQkFBTzthQUFFO1lBQzdDTyxhQUFhO1lBQ2JDLFlBQVk7UUFDZCxHQUFHO1lBQ0RDLFNBQVM7Z0JBQUUsaUJBQWlCO1lBQW1CO1lBQy9DQyxTQUFTLE1BQU8sc0JBQXNCO1FBQ3hDO1FBRUEsT0FBT1IsU0FBU1MsSUFBSSxDQUFDQyxPQUFPLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxXQUFXO0lBQ3ZELEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQSxNQUFNWixRQUFRLEVBQUVTLFFBQVFHLE1BQU1ELE9BQU87UUFDdEUsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbG1zdHVkaW8tc2VydmljZS50cz80NzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeUxNU3R1ZGlvKHByb21wdDogc3RyaW5nLCBtb2RlbCA9ICdtaXN0cmFsLTdiLWluc3RydWN0LXYwLjEnKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjEyMzQvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogcHJvbXB0IH1dLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heF90b2tlbnM6IDUwMFxuICAgIH0sIHtcbiAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIGxtLXN0dWRpbycgfSxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwICAvLyAzMCBzZWNvbmRlbiB0aW1lb3V0XG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnR2VlbiBhbnR3b29yZCBvbnR2YW5nZW4nO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xNIFN0dWRpbyBmb3V0OicsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiAnRm91dCBiaWogdmVyd2Vya2VuIHZlcnpvZWsnO1xuICB9XG59ICJdLCJuYW1lcyI6WyJheGlvcyIsInF1ZXJ5TE1TdHVkaW8iLCJwcm9tcHQiLCJtb2RlbCIsInJlc3BvbnNlIiwicG9zdCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJoZWFkZXJzIiwidGltZW91dCIsImRhdGEiLCJjaG9pY2VzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/lmstudio-service.ts\n");

/***/ }),

/***/ "(api)/./lib/openai-service.ts":
/*!*******************************!*\
  !*** ./lib/openai-service.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAIService: () => (/* binding */ OpenAIService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lmstudio-service */ \"(api)/./lib/lmstudio-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__]);\n([openai__WEBPACK_IMPORTED_MODULE_0__, _lmstudio_service__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Helper functie om te bepalen of LM Studio gebruikt moet worden\nconst shouldUseLMStudio = ()=>process.env.USE_LM_STUDIO === \"true\";\nclass OpenAIService {\n    // Generate smart email reply\n    static async generateEmailReply(originalEmail, context) {\n        try {\n            const systemPrompt = `Je bent Leon van Yannova, een vriendelijke Nederlandse AI developer en bot specialist. \n            Je antwoordt professioneel maar toegankelijk in het Nederlands.\n            Specialisaties: Telegram/WhatsApp bots, trading bots, CRM-systemen, apps.\n            Houd antwoorden kort en actionable.`;\n            const userPrompt = `Schrijf een antwoord op deze email:\\n\\n${originalEmail}\\n\\n${context ? `Context: ${context}` : \"\"}`;\n            if (shouldUseLMStudio()) {\n                const fullPrompt = `${systemPrompt}\\n\\n${userPrompt}`;\n                return await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(fullPrompt);\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-4\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: systemPrompt\n                    },\n                    {\n                        role: \"user\",\n                        content: userPrompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 500\n            });\n            return completion.choices[0].message.content || \"Kon geen antwoord genereren.\";\n        } catch (error) {\n            console.error(\"AI error:\", error);\n            return \"Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.\";\n        }\n    }\n    // Analyze email sentiment\n    static async analyzeSentiment(text) {\n        try {\n            if (shouldUseLMStudio()) {\n                const prompt = `Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\\n\\nTekst: ${text}`;\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const sentiment = parseFloat(result);\n                return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Analyseer het sentiment van deze tekst. Antwoord alleen met een getal tussen -1 (zeer negatief) en 1 (zeer positief).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: text\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const sentiment = parseFloat(completion.choices[0].message.content || \"0\");\n            return isNaN(sentiment) ? 0 : Math.max(-1, Math.min(1, sentiment));\n        } catch (error) {\n            console.error(\"Sentiment analysis error:\", error);\n            return 0;\n        }\n    }\n    // Categorize email\n    static async categorizeEmail(subject, content) {\n        try {\n            const prompt = `Categoriseer deze email in één van deze categorieën: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\\n\\nOnderwerp: ${subject}\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                const category = result.trim().toUpperCase();\n                return [\n                    \"QUOTE\",\n                    \"BOT\",\n                    \"URGENT\",\n                    \"GENERAL\"\n                ].includes(category) ? category : \"GENERAL\";\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Categoriseer deze email in \\xe9\\xe9n van deze categorie\\xebn: QUOTE, BOT, URGENT, GENERAL. Antwoord alleen met de categorie.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: `Onderwerp: ${subject}\\n\\nInhoud: ${content}`\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 10\n            });\n            const category = completion.choices[0].message.content?.trim().toUpperCase();\n            return [\n                \"QUOTE\",\n                \"BOT\",\n                \"URGENT\",\n                \"GENERAL\"\n            ].includes(category || \"\") ? category : \"GENERAL\";\n        } catch (error) {\n            console.error(\"Categorization error:\", error);\n            return \"GENERAL\";\n        }\n    }\n    // Generate email subject suggestions\n    static async generateSubjectSuggestions(content) {\n        try {\n            const prompt = `Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\\n\\nInhoud: ${content}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                return result.split(\"\\n\").filter((s)=>s.trim()) || [];\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Genereer 3 korte, professionele email onderwerpregels in het Nederlands voor deze email inhoud. Geef alleen de 3 onderwerpen, gescheiden door nieuwe regels.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: content\n                    }\n                ],\n                temperature: 0.8,\n                max_tokens: 100\n            });\n            return completion.choices[0].message.content?.split(\"\\n\").filter((s)=>s.trim()) || [];\n        } catch (error) {\n            console.error(\"Subject generation error:\", error);\n            return [\n                \"Uw aanvraag\",\n                \"Informatie verzoek\",\n                \"Contact\"\n            ];\n        }\n    }\n    // Extract key information from email\n    static async extractKeyInfo(emailContent) {\n        try {\n            const prompt = `Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\\n\\nEmail: ${emailContent}`;\n            if (shouldUseLMStudio()) {\n                const result = await (0,_lmstudio_service__WEBPACK_IMPORTED_MODULE_1__.queryLMStudio)(prompt);\n                try {\n                    return JSON.parse(result);\n                } catch  {\n                    return {};\n                }\n            }\n            const completion = await openai.chat.completions.create({\n                model: \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"Extract key information from this email. Return a JSON object with: name, company, phone, intent (wat wil de klant), urgency (low/medium/high).\"\n                    },\n                    {\n                        role: \"user\",\n                        content: emailContent\n                    }\n                ],\n                temperature: 0,\n                max_tokens: 200\n            });\n            return JSON.parse(completion.choices[0].message.content || \"{}\");\n        } catch (error) {\n            console.error(\"Info extraction error:\", error);\n            return {};\n        }\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/openai-service.ts\n");

/***/ })

};
;