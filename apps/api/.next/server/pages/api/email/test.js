"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/email/test";
exports.ids = ["pages/api/email/test"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Ftest&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Ftest.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Ftest&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Ftest.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_email_test_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/email/test.ts */ \"(api)/./pages/api/email/test.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_email_test_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_email_test_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/email/test\",\n        pathname: \"/api/email/test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_email_test_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Ftest&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Ftest.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/email-service-resend.ts":
/*!*************************************!*\
  !*** ./lib/email-service-resend.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResendEmailService: () => (/* binding */ ResendEmailService)\n/* harmony export */ });\nclass ResendEmailService {\n    static{\n        this.RESEND_API_KEY = process.env.RESEND_API_KEY;\n    }\n    static{\n        this.FROM_EMAIL = process.env.FROM_EMAIL || \"<EMAIL>\";\n    }\n    static async sendEmail(to, subject, content) {\n        try {\n            const response = await fetch(\"https://api.resend.com/emails\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.RESEND_API_KEY}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    from: `Yannova <${this.FROM_EMAIL}>`,\n                    to: [\n                        to\n                    ],\n                    subject: subject,\n                    html: content\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                console.error(\"Resend API error:\", error);\n                return false;\n            }\n            const result = await response.json();\n            console.log(\"Email sent successfully via Resend:\", result.id);\n            return true;\n        } catch (error) {\n            console.error(\"Error sending email:\", error);\n            return false;\n        }\n    }\n    static async sendBulkEmails(recipients, subject, content) {\n        try {\n            const response = await fetch(\"https://api.resend.com/emails/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.RESEND_API_KEY}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(recipients.map((to)=>({\n                        from: `Yannova <${this.FROM_EMAIL}>`,\n                        to: [\n                            to\n                        ],\n                        subject: subject,\n                        html: content\n                    })))\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                console.error(\"Resend batch API error:\", error);\n                return false;\n            }\n            const result = await response.json();\n            console.log(\"Bulk emails sent successfully:\", result);\n            return true;\n        } catch (error) {\n            console.error(\"Error sending bulk emails:\", error);\n            return false;\n        }\n    }\n    static async sendWithTemplate(to, templateId, variables) {\n        try {\n            const response = await fetch(\"https://api.resend.com/emails\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.RESEND_API_KEY}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    from: `Yannova <${this.FROM_EMAIL}>`,\n                    to: [\n                        to\n                    ],\n                    template: templateId,\n                    data: variables\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                console.error(\"Resend template error:\", error);\n                return false;\n            }\n            const result = await response.json();\n            console.log(\"Template email sent successfully:\", result.id);\n            return true;\n        } catch (error) {\n            console.error(\"Error sending template email:\", error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./lib/email-service-resend.ts\n");

/***/ }),

/***/ "(api)/./lib/email-service.ts":
/*!******************************!*\
  !*** ./lib/email-service.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailService: () => (/* binding */ EmailService)\n/* harmony export */ });\n/* harmony import */ var _email_service_resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./email-service-resend */ \"(api)/./lib/email-service-resend.ts\");\n// apps/api/lib/email-service.ts\n\nclass EmailService {\n    static{\n        this.FROM_EMAIL = process.env.FROM_EMAIL || \"<EMAIL>\";\n    }\n    static async categorizeEmail(subject, content) {\n        const categories = {\n            QUOTE: [\n                \"prijs\",\n                \"offerte\",\n                \"kosten\",\n                \"budget\"\n            ],\n            BOT: [\n                \"bot\",\n                \"telegram\",\n                \"whatsapp\",\n                \"automatisering\"\n            ],\n            URGENT: [\n                \"spoed\",\n                \"urgent\",\n                \"direct\",\n                \"vandaag\"\n            ],\n            GENERAL: [\n                \"informatie\",\n                \"vraag\",\n                \"contact\"\n            ]\n        };\n        const lowerContent = (subject + \" \" + content).toLowerCase();\n        for (const [category, keywords] of Object.entries(categories)){\n            if (keywords.some((word)=>lowerContent.includes(word))) {\n                return category;\n            }\n        }\n        return \"GENERAL\";\n    }\n    static async generateSmartResponse(category, content) {\n        const templates = {\n            QUOTE: `\n        <h2>Bedankt voor je aanvraag!</h2>\n        <p>Hoi,</p>\n        <p>Ik help je graag met een passende offerte. Om je de beste prijs te kunnen geven, \n        zou je me kunnen vertellen:</p>\n        <ul>\n          <li>Wat is je exacte wens/doel?</li>\n          <li>Wat is je gewenste timeline?</li>\n          <li>Heb je een specifiek budget in gedachten?</li>\n        </ul>\n        <p>Dan maak ik direct een voorstel op maat! 🚀</p>\n        <p>Vriendelijke groet,<br>Leon</p>\n      `,\n            BOT: `\n        <h2>Leuk dat je interesse hebt in bots!</h2>\n        <p>Hoi,</p>\n        <p>Als bot-specialist help ik je graag. Ik bouw:</p>\n        <ul>\n          <li>🤖 Custom Telegram & WhatsApp bots</li>\n          <li>📈 Trading & monitoring bots</li>\n          <li>🔄 Workflow automation</li>\n          <li>🧠 AI-powered assistenten</li>\n        </ul>\n        <p>Zullen we even videobellen om je wensen te bespreken?</p>\n        <p>Vriendelijke groet,<br>Leon</p>\n      `,\n            URGENT: `\n        <h2>Je bericht heeft prioriteit!</h2>\n        <p>Hoi,</p>\n        <p>Ik zie dat dit urgent is - je kunt me direct bereiken op:</p>\n        <ul>\n          <li>📱 Telefoon: +31 6 XX XX XX XX</li>\n          <li>💬 Telegram: @leondev</li>\n        </ul>\n        <p>Ik reageer meestal binnen 30 minuten!</p>\n        <p>Vriendelijke groet,<br>Leon</p>\n      `,\n            GENERAL: `\n        <h2>Bedankt voor je bericht!</h2>\n        <p>Hoi,</p>\n        <p>Ik heb je bericht ontvangen en neem binnen 24 uur contact met je op.</p>\n        <p>Voor snellere reactie kun je me ook bereiken via:</p>\n        <ul>\n          <li>💬 Telegram: @leondev</li>\n          <li>🌐 Website: yannova.be</li>\n        </ul>\n        <p>Vriendelijke groet,<br>Leon</p>\n      `\n        };\n        return templates[category] || templates.GENERAL;\n    }\n    static async handleIncomingEmail(from, subject, content) {\n        try {\n            // 1. Categoriseer de email\n            const category = await this.categorizeEmail(subject, content);\n            // 2. Genereer slim antwoord\n            const response = await this.generateSmartResponse(category, content);\n            // 3. Verstuur antwoord via Zoho\n            await this.sendEmail(from, `Re: ${subject}`, response);\n            // 4. Sla op in CRM als het belangrijk is\n            if (category === \"QUOTE\" || category === \"URGENT\") {\n                await this.createCRMTask(from, subject, category);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Email handling error:\", error);\n            return false;\n        }\n    }\n    static async sendEmail(to, subject, content) {\n        // Check if Resend is configured\n        if (process.env.RESEND_API_KEY) {\n            return await _email_service_resend__WEBPACK_IMPORTED_MODULE_0__.ResendEmailService.sendEmail(to, subject, content);\n        }\n        // Als Resend niet geconfigureerd is, return false\n        console.log(\"Geen email provider geconfigureerd. Voeg RESEND_API_KEY toe aan .env\");\n        return false;\n    }\n    static async createCRMTask(email, subject, category) {\n        // Implementeer CRM integratie\n        console.log(`Creating ${category} task for ${email}`);\n    }\n    static async generateAIResponse(emailContent, subject) {\n        // Check if OpenAI is configured\n        if (process.env.OPENAI_API_KEY) {\n            const { OpenAIService } = await __webpack_require__.e(/*! import() */ \"_api_lib_openai-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./openai-service */ \"(api)/./lib/openai-service.ts\"));\n            return await OpenAIService.generateEmailReply(emailContent, subject);\n        }\n        // Fallback to Cloudflare AI if configured\n        if (process.env.CLOUDFLARE_AI_TOKEN) {\n            try {\n                const response = await fetch(\"https://api.cloudflare.com/client/v4/accounts/YOUR_ACCOUNT_ID/ai/run/@cf/meta/llama-3-8b-instruct\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${process.env.CLOUDFLARE_AI_TOKEN}`,\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        messages: [\n                            {\n                                role: \"user\",\n                                content: `Genereer een professionele Nederlandse email response voor:\\nOnderwerp: ${subject}\\nInhoud: ${emailContent}`\n                            }\n                        ]\n                    })\n                });\n                const aiResult = await response.json();\n                return aiResult.result?.response || this.getTemplateResponse(emailContent);\n            } catch (error) {\n                console.error(\"AI response error:\", error);\n            }\n        }\n        // Fallback to template response\n        return this.getTemplateResponse(emailContent);\n    }\n    static getTemplateResponse(emailContent) {\n        // Fallback template responses\n        const lowerContent = emailContent.toLowerCase();\n        if (lowerContent.includes(\"prijs\") || lowerContent.includes(\"kosten\")) {\n            return `\n        <p>Hoi,</p>\n        <p>Bedankt voor je interesse! Ik stuur je graag een gepersonaliseerde offerte.</p>\n        <p>Kun je me wat meer vertellen over:</p>\n        <ul>\n          <li>Wat voor project je in gedachten hebt</li>\n          <li>Je budget range</li>\n          <li>Gewenste timeline</li>\n        </ul>\n        <p>Dan kan ik je een scherpe prijs geven!</p>\n        <p>Groet,<br>Leon</p>\n      `;\n        }\n        if (lowerContent.includes(\"bot\") || lowerContent.includes(\"telegram\") || lowerContent.includes(\"whatsapp\")) {\n            return `\n        <p>Hoi,</p>\n        <p>Leuk dat je interesse hebt in bot development! Dat is precies mijn specialiteit.</p>\n        <p>Ik bouw:</p>\n        <ul>\n          <li>Telegram & WhatsApp bots</li>\n          <li>Trading bots</li>\n          <li>CRM integraties</li>\n          <li>AI-powered automations</li>\n        </ul>\n        <p>Zullen we even bellen om je ideeën te bespreken?</p>\n        <p>Groet,<br>Leon</p>\n      `;\n        }\n        return `\n      <p>Hoi,</p>\n      <p>Bedankt voor je bericht! Ik neem zo snel mogelijk contact met je op.</p>\n      <p>Voor dringende zaken kun je me bellen op +31 6 12345678</p>\n      <p>Groet,<br>Leon</p>\n    `;\n    }\n    static async sendEmailViaZoho(to, subject, content) {\n        // Gebruik dezelfde sendEmail functie\n        return await this.sendEmail(to, subject, content);\n    }\n    static async sendAutoReply(to, originalSubject) {\n        const subject = `Re: ${originalSubject}`;\n        const content = `\n      <h2>Bedankt voor je bericht!</h2>\n      <p>Hoi,</p>\n      <p>We hebben je bericht ontvangen en nemen zo snel mogelijk contact met je op.</p>\n      <p>Voor dringende zaken kun je bellen naar <strong>+31 6 12345678</strong></p>\n      <br>\n      <p>Met vriendelijke groet,<br>\n      Team Yannova</p>\n    `;\n        return await this.sendEmail(to, subject, content);\n    }\n    static async sendIntelligentReply(to, originalSubject, emailContent) {\n        const subject = `Re: ${originalSubject}`;\n        const aiResponse = await this.generateAIResponse(emailContent, originalSubject);\n        return await this.sendEmail(to, subject, aiResponse);\n    }\n    static async analyzeSentiment(text) {\n        // Use OpenAI if available\n        if (process.env.OPENAI_API_KEY) {\n            try {\n                const { OpenAIService } = await __webpack_require__.e(/*! import() */ \"_api_lib_openai-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./openai-service */ \"(api)/./lib/openai-service.ts\"));\n                return await OpenAIService.analyzeSentiment(text);\n            } catch (error) {\n                console.error(\"OpenAI sentiment error:\", error);\n            }\n        }\n        // Fallback to simple sentiment analysis\n        const positiveWords = [\n            \"dank\",\n            \"geweldig\",\n            \"perfect\",\n            \"tevreden\",\n            \"goed\",\n            \"prima\",\n            \"uitstekend\"\n        ];\n        const negativeWords = [\n            \"probleem\",\n            \"slecht\",\n            \"niet\",\n            \"fout\",\n            \"irritant\",\n            \"teleurgesteld\",\n            \"boos\"\n        ];\n        const words = text.toLowerCase().split(\" \");\n        let score = 0;\n        words.forEach((word)=>{\n            if (positiveWords.includes(word)) score += 0.2;\n            if (negativeWords.includes(word)) score -= 0.2;\n        });\n        return Math.max(-1, Math.min(1, score));\n    }\n    static extractContactInfo(emailContent) {\n        const phoneRegex = /(\\+31|0)[0-9\\s\\-]{8,}/g;\n        const companyRegex = /(?:van|bij|voor) ([A-Z][a-zA-Z\\s]+)/g;\n        return {\n            phone: emailContent.match(phoneRegex)?.[0],\n            company: emailContent.match(companyRegex)?.[1]\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/email-service.ts\n");

/***/ }),

/***/ "(api)/./pages/api/email/test.ts":
/*!*********************************!*\
  !*** ./pages/api/email/test.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_email_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/email-service */ \"(api)/./lib/email-service.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { to, subject, content, testType } = req.body;\n    try {\n        let result = false;\n        switch(testType){\n            case \"auto-reply\":\n                result = await _lib_email_service__WEBPACK_IMPORTED_MODULE_0__.EmailService.sendAutoReply(to, subject);\n                break;\n            case \"intelligent\":\n                result = await _lib_email_service__WEBPACK_IMPORTED_MODULE_0__.EmailService.sendIntelligentReply(to, subject, content);\n                break;\n            case \"direct\":\n                result = await _lib_email_service__WEBPACK_IMPORTED_MODULE_0__.EmailService.sendEmail(to, subject, content);\n                break;\n            default:\n                return res.status(400).json({\n                    error: \"Invalid test type\"\n                });\n        }\n        if (result) {\n            res.json({\n                success: true,\n                message: `Email sent successfully via ${testType}`\n            });\n        } else {\n            res.json({\n                success: false,\n                message: \"Email failed to send\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Email test error:\", error);\n        res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/email/test.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Ftest&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Ftest.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();