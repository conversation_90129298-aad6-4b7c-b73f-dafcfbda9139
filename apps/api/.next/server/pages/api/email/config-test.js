"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/email/config-test";
exports.ids = ["pages/api/email/config-test"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "nodemailer":
/*!*****************************!*\
  !*** external "nodemailer" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("nodemailer");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Fconfig-test&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Fconfig-test.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Fconfig-test&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Fconfig-test.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_email_config_test_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/email/config-test.ts */ \"(api)/./pages/api/email/config-test.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_email_config_test_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_email_config_test_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/email/config-test\",\n        pathname: \"/api/email/config-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_email_config_test_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFU19BUEkmcGFnZT0lMkZhcGklMkZlbWFpbCUyRmNvbmZpZy10ZXN0JnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZlbWFpbCUyRmNvbmZpZy10ZXN0LnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQzZEO0FBQzdEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyw0REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsNERBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veWFubm92YS1jcm0tYXBpLz8yN2QxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvZW1haWwvY29uZmlnLXRlc3QudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9lbWFpbC9jb25maWctdGVzdFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2VtYWlsL2NvbmZpZy10ZXN0XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Fconfig-test&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Fconfig-test.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/email/config-test.ts":
/*!****************************************!*\
  !*** ./pages/api/email/config-test.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"nodemailer\");\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(nodemailer__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const config = {\n        zohoEmail: process.env.ZOHO_EMAIL || \"NOT SET\",\n        zohoPasswordSet: !!process.env.ZOHO_PASSWORD,\n        fromEmail: process.env.FROM_EMAIL || \"NOT SET\",\n        sendgridSet: !!process.env.SENDGRID_API_KEY,\n        resendSet: !!process.env.RESEND_API_KEY,\n        resendApiKey: process.env.RESEND_API_KEY ? \"✅ Configured\" : \"❌ Not set\"\n    };\n    // Test SMTP connection if credentials are set\n    let smtpStatus = \"Not configured\";\n    if (process.env.ZOHO_EMAIL && process.env.ZOHO_PASSWORD) {\n        try {\n            const transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: \"smtp.zoho.eu\",\n                port: 587,\n                secure: false,\n                auth: {\n                    user: process.env.ZOHO_EMAIL,\n                    pass: process.env.ZOHO_PASSWORD\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            await transporter.verify();\n            smtpStatus = \"✅ SMTP connection successful!\";\n        } catch (error) {\n            smtpStatus = `❌ SMTP error: ${error.message}`;\n        }\n    }\n    res.status(200).json({\n        status: \"Email Configuration Check\",\n        config,\n        smtpStatus,\n        instructions: {\n            step1: \"Create a .env file in the root directory\",\n            step2: \"Add ZOHO_EMAIL and ZOHO_PASSWORD\",\n            step3: \"For Zoho: use app-specific password from security settings\",\n            alternativeOptions: [\n                \"Use Resend.com with RESEND_API_KEY\",\n                \"Use SendGrid with SENDGRID_API_KEY\",\n                \"Use Ethereal.email for testing\"\n            ]\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/email/config-test.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Femail%2Fconfig-test&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Femail%2Fconfig-test.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();