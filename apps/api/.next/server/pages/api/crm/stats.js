"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/crm/stats";
exports.ids = ["pages/api/crm/stats"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fstats&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fstats.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fstats&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fstats.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_crm_stats_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/crm/stats.ts */ \"(api)/./pages/api/crm/stats.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_stats_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_stats_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/crm/stats\",\n        pathname: \"/api/crm/stats\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_crm_stats_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFU19BUEkmcGFnZT0lMkZhcGklMkZjcm0lMkZzdGF0cyZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGY3JtJTJGc3RhdHMudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDcUQ7QUFDckQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLG9EQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxvREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvPzJiOWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2FwaS9jcm0vc3RhdHMudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jcm0vc3RhdHNcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jcm0vc3RhdHNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fstats&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fstats.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/crm/stats.ts":
/*!********************************!*\
  !*** ./pages/api/crm/stats.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    // Return mock data if database is not available\n    const mockStats = {\n        totalContacts: 12,\n        unreadEmails: 3,\n        openTasks: 7,\n        activeDeals: 4,\n        urgentTasks: 2,\n        totalPipeline: 45000\n    };\n    try {\n        // Get statistics\n        const [totalContacts, unreadEmails, openTasks, activeDeals] = await Promise.all([\n            prisma.contact.count(),\n            prisma.conversation.count({\n                where: {\n                    readAt: null,\n                    direction: \"INBOUND\"\n                }\n            }),\n            prisma.task.count({\n                where: {\n                    completed: false\n                }\n            }),\n            prisma.deal.count({\n                where: {\n                    stage: {\n                        notIn: [\n                            \"CLOSED_WON\",\n                            \"CLOSED_LOST\"\n                        ]\n                    }\n                }\n            })\n        ]);\n        // Get additional stats\n        const urgentTasks = await prisma.task.count({\n            where: {\n                completed: false,\n                priority: \"URGENT\"\n            }\n        });\n        const totalPipeline = await prisma.deal.aggregate({\n            where: {\n                stage: {\n                    notIn: [\n                        \"CLOSED_WON\",\n                        \"CLOSED_LOST\"\n                    ]\n                }\n            },\n            _sum: {\n                value: true\n            }\n        });\n        res.json({\n            totalContacts,\n            unreadEmails,\n            openTasks,\n            activeDeals,\n            urgentTasks,\n            totalPipeline: totalPipeline._sum.value || 0\n        });\n    } catch (error) {\n        console.error(\"Stats error:\", error);\n        // Return mock data if database fails\n        res.json(mockStats);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/crm/stats.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fstats&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fstats.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();