"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/crm/conversation/[id]";
exports.ids = ["pages/api/crm/conversation/[id]"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_crm_conversation_id_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/crm/conversation/[id].ts */ \"(api)/./pages/api/crm/conversation/[id].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_conversation_id_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_crm_conversation_id_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/crm/conversation/[id]\",\n        pathname: \"/api/crm/conversation/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_crm_conversation_id_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/memory-bank.ts":
/*!****************************!*\
  !*** ./lib/memory-bank.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryMemoryBank: () => (/* binding */ queryMemoryBank)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function queryMemoryBank(query) {\n    return new Promise((resolve, reject)=>{\n        (0,child_process__WEBPACK_IMPORTED_MODULE_0__.exec)(`npx @allpepper/memory-bank-mcp query \"${query}\"`, {\n            env: {\n                ...process.env,\n                MEMORY_BANK_ROOT: process.env.MEMORY_BANK_ROOT\n            }\n        }, (error, stdout, stderr)=>{\n            if (error) reject(error);\n            else resolve(stdout);\n        });\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbWVtb3J5LWJhbmsudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFDO0FBRTlCLGVBQWVDLGdCQUFnQkMsS0FBYTtJQUNqRCxPQUFPLElBQUlDLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JMLG1EQUFJQSxDQUFDLENBQUMsc0NBQXNDLEVBQUVFLE1BQU0sQ0FBQyxDQUFDLEVBQ3BEO1lBQ0VJLEtBQUs7Z0JBQ0gsR0FBR0MsUUFBUUQsR0FBRztnQkFDZEUsa0JBQWtCRCxRQUFRRCxHQUFHLENBQUNFLGdCQUFnQjtZQUNoRDtRQUNGLEdBQ0EsQ0FBQ0MsT0FBT0MsUUFBUUM7WUFDZCxJQUFJRixPQUFPSixPQUFPSTtpQkFDYkwsUUFBUU07UUFDZjtJQUVKO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly95YW5ub3ZhLWNybS1hcGkvLi9saWIvbWVtb3J5LWJhbmsudHM/ZjYwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBleGVjIH0gZnJvbSAnY2hpbGRfcHJvY2Vzcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeU1lbW9yeUJhbmsocXVlcnk6IHN0cmluZykge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGV4ZWMoYG5weCBAYWxscGVwcGVyL21lbW9yeS1iYW5rLW1jcCBxdWVyeSBcIiR7cXVlcnl9XCJgLCBcbiAgICAgIHsgXG4gICAgICAgIGVudjogeyBcbiAgICAgICAgICAuLi5wcm9jZXNzLmVudiwgIC8vIFZvZWcgYWxsZSBiZXN0YWFuZGUgZW52IHZhcmlhYmVsZW4gdG9lXG4gICAgICAgICAgTUVNT1JZX0JBTktfUk9PVDogcHJvY2Vzcy5lbnYuTUVNT1JZX0JBTktfUk9PVCBcbiAgICAgICAgfSBcbiAgICAgIH0sXG4gICAgICAoZXJyb3IsIHN0ZG91dCwgc3RkZXJyKSA9PiB7XG4gICAgICAgIGlmIChlcnJvcikgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgZWxzZSByZXNvbHZlKHN0ZG91dCk7XG4gICAgICB9XG4gICAgKTtcbiAgfSk7XG59ICJdLCJuYW1lcyI6WyJleGVjIiwicXVlcnlNZW1vcnlCYW5rIiwicXVlcnkiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImVudiIsInByb2Nlc3MiLCJNRU1PUllfQkFOS19ST09UIiwiZXJyb3IiLCJzdGRvdXQiLCJzdGRlcnIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./lib/memory-bank.ts\n");

/***/ }),

/***/ "(api)/./pages/api/crm/conversation/[id].ts":
/*!********************************************!*\
  !*** ./pages/api/crm/conversation/[id].ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_memory_bank__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/memory-bank */ \"(api)/./lib/memory-bank.ts\");\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nasync function handler(req, res) {\n    const { id } = req.query;\n    console.log(`Fetching conversation for ID: ${id}`);\n    if (req.method === \"GET\") {\n        try {\n            const conversation = await prisma.conversation.findUnique({\n                where: {\n                    id: id\n                },\n                include: {\n                    contact: true\n                }\n            });\n            if (!conversation) {\n                return res.status(404).json({\n                    error: \"Conversation not found\"\n                });\n            }\n            try {\n                // Verbeterde query met foutafhandeling\n                const memories = await (0,_lib_memory_bank__WEBPACK_IMPORTED_MODULE_1__.queryMemoryBank)(`Get memories for contact:${conversation.contactId}`);\n                res.json({\n                    ...conversation,\n                    memories: JSON.parse(memories)\n                });\n            } catch (memoryError) {\n                console.error(\"Memory bank error:\", memoryError);\n                res.json({\n                    ...conversation,\n                    memories: []\n                });\n            }\n        } catch (error) {\n            console.error(\"Conversation error:\", error);\n            res.status(500).json({\n                error: \"Internal server error\"\n            });\n        }\n    } else {\n        res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/crm/conversation/[id].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fcrm%2Fconversation%2F%5Bid%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();