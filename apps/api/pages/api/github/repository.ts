import { NextApiRequest, NextApiResponse } from 'next';
import GitHubService from '../../../lib/github-service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { action, ...params } = req.body;

    // GitHub configuratie uit environment variables
    const githubConfig = {
      token: process.env.GITHUB_TOKEN || '',
      owner: process.env.GITHUB_OWNER || 'innovars_lab',
      repo: process.env.GITHUB_REPO || 'yannova-crm',
    };

    if (!githubConfig.token) {
      return res.status(400).json({ 
        error: 'GitHub token niet geconfigureerd. Voeg GITHUB_TOKEN toe aan .env' 
      });
    }

    const github = new GitHubService(githubConfig);

    switch (action) {
      case 'analyze_repository':
        const analysis = await github.analyzeRepository();
        return res.status(200).json({
          success: true,
          data: analysis,
          message: 'Repository geanalyseerd'
        });

      case 'get_commits':
        const commits = await github.getRecentCommits(params.count || 10);
        return res.status(200).json({
          success: true,
          data: commits,
          message: `${commits.length} recente commits opgehaald`
        });

      case 'get_issues':
        const issues = await github.getOpenIssues();
        return res.status(200).json({
          success: true,
          data: issues,
          message: `${issues.length} open issues gevonden`
        });

      case 'create_issue':
        if (!params.title) {
          return res.status(400).json({ error: 'Titel is verplicht voor nieuwe issue' });
        }
        const newIssue = await github.createIssue(
          params.title,
          params.body || '',
          params.labels
        );
        return res.status(201).json({
          success: true,
          data: newIssue,
          message: `Issue #${newIssue.number} aangemaakt: ${newIssue.title}`
        });

      case 'update_issue':
        if (!params.issueNumber) {
          return res.status(400).json({ error: 'Issue nummer is verplicht' });
        }
        const updatedIssue = await github.updateIssue(params.issueNumber, {
          title: params.title,
          body: params.body,
          state: params.state,
          labels: params.labels,
        });
        return res.status(200).json({
          success: true,
          data: updatedIssue,
          message: `Issue #${updatedIssue.number} bijgewerkt`
        });

      case 'get_file':
        if (!params.path) {
          return res.status(400).json({ error: 'Bestandspad is verplicht' });
        }
        const fileContent = await github.getFileContent(params.path, params.ref);
        return res.status(200).json({
          success: true,
          data: { content: fileContent, path: params.path },
          message: `Bestand ${params.path} opgehaald`
        });

      case 'code_review':
        if (!params.commitSha) {
          return res.status(400).json({ error: 'Commit SHA is verplicht voor code review' });
        }
        const review = await github.performCodeReview(params.commitSha);
        return res.status(200).json({
          success: true,
          data: review,
          message: `Code review uitgevoerd voor commit ${params.commitSha.substring(0, 7)}`
        });

      case 'ai_project_analysis':
        // Combineer repository data met AI analysis
        const repoData = await github.analyzeRepository();
        const aiAnalysis = {
          project_health: calculateProjectHealth(repoData),
          recommendations: generateRecommendations(repoData),
          next_sprint_suggestions: generateSprintSuggestions(repoData),
          technical_debt: analyzeTechnicalDebt(repoData),
        };
        
        return res.status(200).json({
          success: true,
          data: {
            repository: repoData,
            ai_insights: aiAnalysis,
          },
          message: 'AI project analyse voltooid'
        });

      default:
        return res.status(400).json({ 
          error: `Onbekende actie: ${action}`,
          available_actions: [
            'analyze_repository',
            'get_commits', 
            'get_issues',
            'create_issue',
            'update_issue',
            'get_file',
            'code_review',
            'ai_project_analysis'
          ]
        });
    }

  } catch (error) {
    console.error('GitHub API error:', error);
    return res.status(500).json({ 
      error: 'GitHub API fout',
      details: error instanceof Error ? error.message : 'Onbekende fout'
    });
  }
}

// AI Helper Functions
function calculateProjectHealth(repoData: any): string {
  const { stats, recent_commits, open_issues } = repoData;
  
  let score = 100;
  
  // Penalize for too many open issues
  if (open_issues.length > 20) score -= 20;
  else if (open_issues.length > 10) score -= 10;
  
  // Check commit frequency (last 10 commits)
  const recentCommitDates = recent_commits.map((c: any) => new Date(c.date));
  const daysSinceLastCommit = (Date.now() - Math.max(...recentCommitDates.map(d => d.getTime()))) / (1000 * 60 * 60 * 24);
  
  if (daysSinceLastCommit > 7) score -= 15;
  if (daysSinceLastCommit > 30) score -= 25;
  
  if (score >= 80) return 'Excellent';
  if (score >= 60) return 'Good';
  if (score >= 40) return 'Fair';
  return 'Needs Attention';
}

function generateRecommendations(repoData: any): string[] {
  const recommendations: string[] = [];
  const { stats, recent_commits, open_issues } = repoData;
  
  if (open_issues.length > 15) {
    recommendations.push('🔴 Prioriteer het sluiten van open issues - er zijn er meer dan 15');
  }
  
  if (recent_commits.length < 5) {
    recommendations.push('📈 Verhoog commit frequentie voor betere code tracking');
  }
  
  // Check for patterns in commit messages
  const commitMessages = recent_commits.map((c: any) => c.message.toLowerCase());
  const hasFixCommits = commitMessages.some(msg => msg.includes('fix') || msg.includes('bug'));
  
  if (hasFixCommits) {
    recommendations.push('🧪 Overweeg meer unit tests om bugs te voorkomen');
  }
  
  recommendations.push('🚀 Implementeer CI/CD pipeline voor automatische deployments');
  recommendations.push('📚 Update README.md met recente wijzigingen');
  
  return recommendations;
}

function generateSprintSuggestions(repoData: any): string[] {
  const suggestions: string[] = [];
  const { open_issues } = repoData;
  
  // Prioritize issues by labels
  const bugIssues = open_issues.filter((issue: any) => 
    issue.labels.some(label => label.toLowerCase().includes('bug'))
  );
  
  const featureIssues = open_issues.filter((issue: any) => 
    issue.labels.some(label => label.toLowerCase().includes('feature'))
  );
  
  if (bugIssues.length > 0) {
    suggestions.push(`🐛 Focus op ${bugIssues.length} bug fixes deze sprint`);
  }
  
  if (featureIssues.length > 0) {
    suggestions.push(`✨ Plan ${Math.min(3, featureIssues.length)} nieuwe features`);
  }
  
  suggestions.push('🔧 Refactor oude code voor betere maintainability');
  suggestions.push('⚡ Performance optimalisaties voor kritieke functies');
  suggestions.push('🛡️ Security audit van authentication flows');
  
  return suggestions;
}

function analyzeTechnicalDebt(repoData: any): {
  level: string;
  areas: string[];
  priority_actions: string[];
} {
  const { languages, recent_commits } = repoData;
  
  const areas: string[] = [];
  const priority_actions: string[] = [];
  
  // Check for diverse language usage (might indicate inconsistency)
  const langCount = Object.keys(languages).length;
  if (langCount > 5) {
    areas.push('Te veel verschillende talen - overweeg consolidatie');
  }
  
  // Check commit messages for debt indicators
  const commitMessages = recent_commits.map((c: any) => c.message.toLowerCase());
  const hasQuickFixes = commitMessages.some(msg => 
    msg.includes('quick fix') || msg.includes('hotfix') || msg.includes('temp')
  );
  
  if (hasQuickFixes) {
    areas.push('Quick fixes gedetecteerd - mogelijk technische schuld');
    priority_actions.push('Review en refactor quick fixes naar proper solutions');
  }
  
  priority_actions.push('Code quality metrics implementeren');
  priority_actions.push('Automated testing coverage verhogen');
  priority_actions.push('Dependencies updaten naar laatste versies');
  
  const level = areas.length > 2 ? 'High' : areas.length > 0 ? 'Medium' : 'Low';
  
  return { level, areas, priority_actions };
} 