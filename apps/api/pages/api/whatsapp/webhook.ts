import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '../../../lib/prisma'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { messages } = req.body
    
    for (const message of messages || []) {
      const phoneNumber = message.from
      const content = message.text?.body || ''
      
      // Find or create contact by phone
      let contact = await prisma.contact.findFirst({
        where: { phone: { contains: phoneNumber.slice(-9) } }
      })
      
      if (!contact) {
        contact = await prisma.contact.create({
          data: {
            email: `${phoneNumber}@whatsapp.temp`,
            phone: phoneNumber,
            source: 'WHATSAPP'
          }
        })
      }
      
      // Create conversation
      await prisma.conversation.create({
        data: {
          contactId: contact.id,
          type: 'CHAT',
          channel: 'WHATSAPP',
          content,
          direction: 'INBOUND'
        }
      })
    }
    
    res.json({ success: true })
  }
}