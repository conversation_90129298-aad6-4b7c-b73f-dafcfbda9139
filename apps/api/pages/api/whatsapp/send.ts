import { WhatsAppService } from '../../../lib/whatsapp-service';
import { prisma } from '../../../lib/prisma';
import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  const { contactId, message, templateName, templateParams } = req.body;
  
  try {
    const contact = await prisma.contact.findUnique({
      where: { id: contactId }
    });
    
    if (!contact?.phone) {
      return res.status(400).json({ error: 'Contact has no phone number' });
    }
    
    const whatsapp = new WhatsAppService();
    let result;
    
    if (templateName) {
      result = await whatsapp.sendTemplate(contact.phone, templateName, templateParams || []);
    } else {
      result = await whatsapp.sendMessage(contact.phone, message);
    }
    
    // Log conversation
    await prisma.conversation.create({
      data: {
        contactId: contact.id,
        type: 'CHAT',
        channel: 'WHATSAPP',
        content: message || `Template: ${templateName}`,
        direction: 'OUTBOUND'
      }
    });
    
    res.json({ success: true, messageId: result.messages[0].id });
  } catch (error) {
    console.error('WhatsApp send error:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
}