import { NextApiRequest, NextApiResponse } from 'next';
import { getAgentConfig } from '../../../lib/ai-agent-config';
import Anthropic from '@anthropic-ai/sdk';
import OpenAI from 'openai';

const orchestrator = new AIOrchestrator();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { code, language = 'typescript', action = 'review' } = req.body;

    if (!code) {
      return res.status(400).json({ error: 'Code is required' });
    }

    let result;
    
    switch (action) {
      case 'review':
        result = await orchestrator.codingAssistant(
          `Review deze ${language} code en geef feedback:\n\n${code}`,
          language
        );
        break;
        
      case 'optimize':
        result = await orchestrator.codingAssistant(
          `Optimaliseer deze ${language} code voor performance en leesbaarheid:\n\n${code}`,
          language
        );
        break;
        
      case 'debug':
        result = await orchestrator.codingAssistant(
          `Debug deze ${language} code en vind potentiële bugs:\n\n${code}`,
          language
        );
        break;
        
      case 'explain':
        result = await orchestrator.codingAssistant(
          `Leg deze ${language} code uit in eenvoudige Nederlandse termen:\n\n${code}`,
          language
        );
        break;
        
      case 'refactor':
        result = await orchestrator.codingAssistant(
          `Refactor deze ${language} code volgens best practices:\n\n${code}`,
          language
        );
        break;
        
      default:
        return res.status(400).json({ error: 'Invalid action. Use: review, optimize, debug, explain, refactor' });
    }
    
    res.status(200).json({
      ...result,
      action,
      language,
      originalCode: code
    });
  } catch (error) {
    console.error('Coding assistant error:', error);
    res.status(500).json({ 
      error: 'Failed to process code',
      details: error.message 
    });
  }
} 

export function getAgentConfig(agentName: string) {
  const configPath = path.join(process.cwd(), '.cursor/config/ai-agents.json');
  const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
  return config.agents[agentName];
}

async function callAIAgent(config: any, input: string): Promise<string> {
  const { model, prompt } = config;
  const fullPrompt = prompt.replace('{{{ input }}}', input);

  try {
    if (model.startsWith('anthropic/')) {
      // Gebruik Claude via Anthropic SDK
      const anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY
      });
      
      const response = await anthropic.messages.create({
        model: model.replace('anthropic/', ''),
        max_tokens: 2000,
        messages: [{ role: 'user', content: fullPrompt }]
      });
      
      return response.content[0].text;
    } 
    else if (model.startsWith('deepseek-coder')) {
      // Gebruik DeepSeek Coder via OpenAI-compatible API
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
      });
      
      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          { role: 'system', content: 'Je bent een ervaren software engineer.' },
          { role: 'user', content: fullPrompt }
        ],
        max_tokens: 2000
      });
      
      return response.choices[0].message.content || '';
    }
    else {
      throw new Error(`Model ${model} wordt niet ondersteund`);
    }
  } catch (error) {
    console.error(`AI Agent fout (${model}):`, error);
    throw new Error('Fout bij het aanroepen van de AI agent');
  }
} 