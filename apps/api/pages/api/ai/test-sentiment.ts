import { NextApiRequest, NextApiResponse } from 'next';
import { OpenAIService } from '../../../lib/openai-service';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    const sentiment = await OpenAIService.analyzeSentiment(text);
    const category = await OpenAIService.categorizeEmail('Test', text);
    
    res.status(200).json({ 
      text,
      sentiment,
      category,
      sentimentLabel: sentiment > 0.3 ? 'Positief' : sentiment < -0.3 ? 'Negatief' : 'Neutraal',
      timestamp: new Date().toISOString(),
      aiProvider: process.env.USE_LM_STUDIO === 'true' ? 'LM Studio' : 'OpenAI'
    });
  } catch (error) {
    console.error('Sentiment analysis test error:', error);
    res.status(500).json({ error: 'Failed to analyze sentiment' });
  }
} 