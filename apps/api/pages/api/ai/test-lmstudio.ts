import { NextApiRequest, NextApiResponse } from 'next';
import { queryLMStudio } from '../../../lib/lmstudio-service';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Test basic connectivity
    const testPrompt = 'Hall<PERSON>, kun je antwoorden in het Nederlands?';
    const response = await queryLMStudio(testPrompt);
    
    res.status(200).json({
      success: true,
      message: 'LM Studio is werkend!',
      testPrompt,
      response,
      timestamp: new Date().toISOString(),
      model: 'mistral-7b-instruct-v0.1',
      endpoint: 'http://127.0.0.1:1234/v1'
    });
  } catch (error) {
    console.error('LM Studio test error:', error);
    res.status(500).json({
      success: false,
      error: 'LM Studio niet bereikbaar',
      details: error.message,
      endpoint: 'http://127.0.0.1:1234/v1'
    });
  }
} 