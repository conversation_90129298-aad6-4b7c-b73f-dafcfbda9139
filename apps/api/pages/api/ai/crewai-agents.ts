// API endpoint voor CrewAI Multi-Agent workflows
import { NextApiRequest, NextApiResponse } from 'next';
import { spawn } from 'child_process';
import path from 'path';

interface CrewAIRequest {
  task?: string;
  type?: 'optimization' | 'custom' | 'analysis' | 'development' | 'planning';
  description?: string;
}

interface CrewAIResponse {
  success: boolean;
  result?: string;
  error?: string;
  agents?: string[];
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CrewAIResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { task, type = 'custom', description }: CrewAIRequest = req.body;

    // Pad naar het Python script
    const pythonScript = path.join(process.cwd(), '..', '..', 'crewai_agents.py');
    
    console.log('🤖 Starting CrewAI agents...');
    console.log('Task:', task);
    console.log('Type:', type);

    // Bepaal welke argumenten te gebruiken
    let args: string[] = [];
    
    if (type === 'optimization') {
      // Geen extra argumenten - run volledige optimalisatie
      args = [];
    } else if (task) {
      // Custom task
      args = [task];
    } else if (description) {
      // Gebruik description als task
      args = [description];
    } else {
      return res.status(400).json({
        success: false,
        error: 'Task of description is vereist',
        timestamp: new Date().toISOString()
      });
    }

    // Run Python script
    const result = await runCrewAIScript(pythonScript, args);

    console.log('✅ CrewAI completed');

    return res.status(200).json({
      success: true,
      result: result,
      agents: ['CRM Analyst', 'Lead Developer', 'Project Manager', 'AI Specialist'],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ CrewAI Error:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

function runCrewAIScript(scriptPath: string, args: string[]): Promise<string> {
  return new Promise((resolve, reject) => {
    // Activeer virtual environment en run script
    const command = 'source';
    const scriptArgs = [
      path.join(process.cwd(), '..', '..', 'ai-agents-env', 'bin', 'activate'),
      '&&',
      'python3',
      scriptPath,
      ...args
    ];

    console.log('Running:', command, scriptArgs.join(' '));

    const pythonProcess = spawn('bash', ['-c', `source ${path.join(process.cwd(), '..', '..', 'ai-agents-env', 'bin', 'activate')} && python3 ${scriptPath} ${args.join(' ')}`], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: path.dirname(scriptPath)
    });

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Python output:', output);
      stdout += output;
    });

    pythonProcess.stderr.on('data', (data) => {
      const error = data.toString();
      console.error('Python error:', error);
      stderr += error;
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(`Python script exited with code ${code}. Error: ${stderr}`));
      }
    });

    pythonProcess.on('error', (error) => {
      reject(new Error(`Failed to start Python process: ${error.message}`));
    });

    // Timeout na 5 minuten
    setTimeout(() => {
      pythonProcess.kill();
      reject(new Error('CrewAI timeout - process took too long'));
    }, 5 * 60 * 1000);
  });
} 