import { NextApiRequest, NextApiResponse } from 'next';
import { queryLMStudio } from '../../../lib/lmstudio-service';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({ error: 'Question is required' });
    }

    const response = await queryLMStudio(question);
    
    res.status(200).json({ 
      response,
      timestamp: new Date().toISOString(),
      model: 'mistral-7b-instruct-v0.1'
    });
  } catch (error) {
    console.error('AI API error:', error);
    res.status(500).json({ error: 'Failed to get AI response' });
  }
} 