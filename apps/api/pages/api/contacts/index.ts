import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '../../../lib/prisma'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    const contacts = await prisma.contact.findMany({
      include: {
        conversations: { take: 1, orderBy: { createdAt: 'desc' } },
        deals: { where: { stage: { not: 'CLOSED_LOST' } } },
        _count: { select: { conversations: true, deals: true } }
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    return res.json(contacts)
  }
  
  if (req.method === 'POST') {
    const { email, name, phone, company, source } = req.body
    
    const contact = await prisma.contact.create({
      data: {
        email,
        name,
        phone,
        company,
        source: source || 'EMAIL'
      }
    })
    
    return res.json(contact)
  }
}