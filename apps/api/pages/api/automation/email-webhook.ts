import { NextApiRequest, NextApiResponse } from 'next';
import { AutoAIWorkflows } from '../../../lib/auto-ai-workflows';

const autoAI = new AutoAIWorkflows();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { from, subject, content, timestamp } = req.body;

    if (!from || !subject || !content) {
      return res.status(400).json({ error: 'Missing required email data' });
    }

    console.log(`📧 Nieuwe email ontvangen van ${from}: ${subject}`);

    // Automatische AI verwerking
    const result = await autoAI.processIncomingEmail({
      from,
      subject,
      content,
      timestamp: timestamp || new Date().toISOString()
    });

    // Log resultaat
    console.log(`🤖 Email automatisch verwerkt:`, {
      category: result.analysis?.category,
      urgency: result.analysis?.urgency,
      actions: result.actions
    });

    res.status(200).json({
      message: 'Email automatisch verwerkt door AI',
      ...result
    });
  } catch (error) {
    console.error('Email automation error:', error);
    res.status(500).json({ 
      error: 'Failed to process email automatically',
      details: error.message 
    });
  }
} 