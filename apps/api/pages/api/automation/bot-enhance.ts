import { NextApiRequest, NextApiResponse } from 'next';
import { AutoAIWorkflows } from '../../../lib/auto-ai-workflows';

const autoAI = new AutoAIWorkflows();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { originalMessage, userContext } = req.body;

    if (!originalMessage) {
      return res.status(400).json({ error: 'Original message is required' });
    }

    console.log(`🤖 Auto-verbetering bot response`);

    // Automatische response verbetering
    const result = await autoAI.enhanceBotResponse(
      originalMessage,
      userContext || 'Algemene gebruiker'
    );

    console.log(`✨ Bot response verbeterd van ${originalMessage.length} naar ${result.enhancedMessage.length} karakters`);

    res.status(200).json({
      message: 'Bot response automatisch verbeterd',
      ...result
    });
  } catch (error) {
    console.error('Bot enhancement error:', error);
    res.status(500).json({ 
      error: 'Failed to enhance bot response',
      details: error.message 
    });
  }
} 