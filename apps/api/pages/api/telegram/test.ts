import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const token = process.env.TELEGRAM_BOT_TOKEN

  if (!token) {
    return res.status(400).json({ 
      success: false, 
      message: 'Telegram bot token niet geconfigureerd' 
    })
  }

  try {
    // Test bot info
    const response = await fetch(`https://api.telegram.org/bot${token}/getMe`)
    const data = await response.json()

    if (data.ok) {
      res.json({
        success: true,
        message: `Bot actief: @${data.result.username}`,
        bot: data.result
      })
    } else {
      res.json({
        success: false,
        message: 'Bot token ongeldig',
        error: data.description
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Test mislukt',
      error: error.message
    })
  }
} 