import { NextApiRequest, NextApiResponse } from 'next'
import { PrismaClient } from '@prisma/client'
import { exec } from 'child_process';

const prisma = new PrismaClient()

export async function queryMemoryBank(query: string) {
  return new Promise((resolve, reject) => {
    exec(`npx @allpepper/memory-bank-mcp query "${query}"`, 
      { env: { MEMORY_BANK_ROOT: process.env.MEMORY_BANK_ROOT } },
      (error, stdout, stderr) => {
        if (error) reject(error);
        else resolve(stdout);
      }
    );
  });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query
  console.log(`Fetching conversation for ID: ${id}`)

  if (req.method === 'GET') {
    try {
      const conversation = await prisma.conversation.findUnique({
        where: { id: id as string },
        include: {
          contact: true
        }
      })

      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' })
      }

      // Haal relevante herinneringen op
      const memories = await queryMemoryBank(
        `Get memories related to contact ${conversation.contactId}`
      );

      res.json({ ...conversation, memories });
    } catch (error) {
      console.error('Conversation error:', error)
      res.status(500).json({ error: 'Internal server error' })
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' })
  }
} 