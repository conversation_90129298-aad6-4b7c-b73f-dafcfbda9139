import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '../../../lib/prisma'
import { EmailService } from '../../../lib/email-service'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }
  
  const { from, to, subject, content, messageId } = req.body
  
  try {
    // Find or create contact
    let contact = await prisma.contact.findUnique({
      where: { email: from }
    })
    
    let isNewContact = false;
    
    if (!contact) {
      const contactInfo = EmailService.extractContactInfo(content)
      
      contact = await prisma.contact.create({
        data: {
          email: from,
          source: 'EMAIL',
          phone: contactInfo.phone,
          company: contactInfo.company,
          lastContact: new Date()
        }
      })
      isNewContact = true;
    } else {
      // Update last contact
      await prisma.contact.update({
        where: { id: contact.id },
        data: { lastContact: new Date() }
      })
    }
    
    // Create conversation
    const sentiment = await EmailService.analyzeSentiment(content)
    
    await prisma.conversation.create({
      data: {
        contactId: contact.id,
        type: 'EMAIL',
        channel: 'EMAIL',
        subject,
        content,
        direction: 'INBOUND',
        sentiment
      }
    })
    
    // Send auto-reply to new contacts
    if (isNewContact) {
      await EmailService.sendAutoReply(from, subject);
    }
    
    // Auto-create task if sentiment is negative
    if (sentiment < -0.3) {
      await prisma.task.create({
        data: {
          contactId: contact.id,
          title: `Urgente follow-up: ${subject}`,
          type: 'FOLLOWUP',
          priority: 'HIGH',
          dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours
        }
      })
    }
    
    res.json({ success: true, contactId: contact.id })
  } catch (error) {
    console.error('Email webhook error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}