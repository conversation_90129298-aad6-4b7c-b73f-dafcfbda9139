import { NextApiRequest, NextApiResponse } from 'next'
import * as nodemailer from 'nodemailer'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const config = {
    zohoEmail: process.env.ZOHO_EMAIL || 'NOT SET',
    zohoPasswordSet: !!process.env.ZOHO_PASSWORD,
    fromEmail: process.env.FROM_EMAIL || 'NOT SET',
    sendgridSet: !!process.env.SENDGRID_API_KEY,
    resendSet: !!process.env.RESEND_API_KEY,
    resendApiKey: process.env.RESEND_API_KEY ? '✅ Configured' : '❌ Not set'
  }

  // Test SMTP connection if credentials are set
  let smtpStatus = 'Not configured'
  if (process.env.ZOHO_EMAIL && process.env.ZOHO_PASSWORD) {
    try {
      const transporter = nodemailer.createTransport({
        host: 'smtp.zoho.eu',
        port: 587,
        secure: false,
        auth: {
          user: process.env.ZOHO_EMAIL,
          pass: process.env.ZOHO_PASSWORD
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      await transporter.verify()
      smtpStatus = '✅ SMTP connection successful!'
    } catch (error: any) {
      smtpStatus = `❌ SMTP error: ${error.message}`
    }
  }

  res.status(200).json({
    status: 'Email Configuration Check',
    config,
    smtpStatus,
    instructions: {
      step1: 'Create a .env file in the root directory',
      step2: 'Add ZOHO_EMAIL and ZOHO_PASSWORD',
      step3: 'For Zoho: use app-specific password from security settings',
      alternativeOptions: [
        'Use Resend.com with RESEND_API_KEY',
        'Use SendGrid with SENDGRID_API_KEY',
        'Use Ethereal.email for testing'
      ]
    }
  })
} 