import { NextApiRequest, NextApiResponse } from 'next'
import { EmailReceiver } from '../../../lib/email-receiver'

// Resend webhook voor inbound emails
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Verify webhook signature (optioneel maar aanbevolen)
  const webhookSecret = process.env.RESEND_WEBHOOK_SECRET
  if (webhookSecret) {
    // Implement signature verification here
    // const signature = req.headers['resend-signature']
  }

  try {
    const { type, data } = req.body

    // Handle different webhook types
    switch (type) {
      case 'email.received':
        // Process inbound email
        const { from, to, subject, html, text, message_id } = data
        
        await EmailReceiver.processIncomingEmail(
          from,
          subject,
          text || html || '',
          message_id
        )
        
        console.log(`Email received from ${from}: ${subject}`)
        break

      case 'email.bounced':
        console.log('Email bounced:', data)
        // Handle bounce
        break

      case 'email.complained':
        console.log('Spam complaint:', data)
        // Handle complaint
        break

      case 'email.delivered':
        console.log('Email delivered:', data)
        // Track delivery
        break

      case 'email.opened':
        console.log('Email opened:', data)
        // Track opens
        break

      case 'email.clicked':
        console.log('Link clicked:', data)
        // Track clicks
        break
    }

    res.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    res.status(500).json({ error: 'Webhook processing failed' })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
} 