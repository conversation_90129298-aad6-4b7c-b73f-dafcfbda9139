import { NextApiRequest, NextApiResponse } from 'next'
import { EmailTemplates } from '../../../lib/email-templates'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { templateType, to, data } = req.body

  try {
    let result = false;

    switch (templateType) {
      case 'welcome':
        result = await EmailTemplates.sendWelcomeEmail(to, data);
        break;
      
      case 'quote':
        result = await EmailTemplates.sendQuoteEmail(to, data);
        break;
      
      case 'bot-notification':
        result = await EmailTemplates.sendBotNotification(to, data);
        break;
      
      default:
        return res.status(400).json({ error: 'Invalid template type' });
    }

    if (result) {
      res.json({ 
        success: true, 
        message: `Template email '${templateType}' sent successfully` 
      });
    } else {
      res.json({ 
        success: false, 
        message: 'Failed to send template email' 
      });
    }
  } catch (error) {
    console.error('Template email error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
} 