<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agents Dashboard - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: white;
            text-align: center;
        }

        .status-bar.online {
            background: rgba(34, 197, 94, 0.2);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .agent-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .agent-card:hover {
            transform: translateY(-2px);
        }

        .agent-card h4 {
            margin-bottom: 8px;
        }

        .agent-card p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .action-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .custom-task {
            margin-top: 20px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .input-group input,
        .input-group textarea,
        .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .input-group input:focus,
        .input-group textarea:focus,
        .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .textarea {
            min-height: 100px;
            resize: vertical;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results {
            grid-column: 1 / -1;
        }

        .result-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
            line-height: 1.6;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .agent-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            color: #1565c0;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .agent-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Agents Dashboard</h1>
            <p>Yannova CRM - Intelligent Multi-Agent System</p>
        </div>

        <div class="status-bar online" id="statusBar">
            ✅ AI Agents System Online - Ready for Tasks
        </div>

        <div class="dashboard">
            <!-- Agents Overview -->
            <div class="card">
                <h3>🎯 AI Agents Team</h3>
                <div class="agent-grid">
                    <div class="agent-card" onclick="quickTask('analysis')">
                        <h4>📊 CRM Analyst</h4>
                        <p>Data analyse & performance insights</p>
                    </div>
                    <div class="agent-card" onclick="quickTask('development')">
                        <h4>💻 Lead Developer</h4>
                        <p>Code generatie & architectuur</p>
                    </div>
                    <div class="agent-card" onclick="quickTask('planning')">
                        <h4>📋 Project Manager</h4>
                        <p>Planning & projectcoördinatie</p>
                    </div>
                    <div class="agent-card" onclick="quickTask('ai_strategy')">
                        <h4>🤖 AI Specialist</h4>
                        <p>AI integratie & optimalisatie</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3>⚡ Quick Actions</h3>
                <div class="quick-actions">
                    <button class="action-btn" onclick="runTask('Analyseer de huidige CRM performance en geef verbeterpunten', 'analysis')">
                        📈 Performance Analyse
                    </button>
                    <button class="action-btn" onclick="runTask('Genereer TypeScript code voor een nieuwe CRM dashboard component', 'development')">
                        🔧 Code Generatie
                    </button>
                    <button class="action-btn" onclick="runTask('Maak een gedetailleerd projectplan voor CRM AI integratie', 'planning')">
                        📅 Project Planning
                    </button>
                    <button class="action-btn" onclick="runTask('Ontwikkel een AI strategie voor CRM optimalisatie', 'ai_strategy')">
                        🤖 AI Strategie
                    </button>
                </div>
            </div>

            <!-- Custom Task -->
            <div class="card">
                <h3>💬 Custom AI Task</h3>
                <form id="customTaskForm" class="custom-task">
                    <div class="input-group">
                        <label for="taskType">Agent Type:</label>
                        <select id="taskType" name="taskType">
                            <option value="auto">🎯 Auto-select (Aanbevolen)</option>
                            <option value="analysis">📊 CRM Analyst</option>
                            <option value="development">💻 Lead Developer</option>
                            <option value="planning">📋 Project Manager</option>
                            <option value="ai_strategy">🤖 AI Specialist</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label for="taskDescription">Taak Beschrijving:</label>
                        <textarea 
                            id="taskDescription" 
                            name="taskDescription" 
                            class="textarea"
                            placeholder="Beschrijf wat je wilt dat de AI agent doet...

Voorbeelden:
• Analyseer de CRM bottlenecks
• Genereer Vue.js code voor lead management
• Maak een roadmap voor AI integratie
• Ontwikkel een chatbot strategie"
                            required
                        ></textarea>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn">
                        🚀 Start AI Agent
                    </button>
                </form>
            </div>

            <!-- Results -->
            <div class="card results">
                <h3>📋 Agent Resultaten</h3>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>AI Agent aan het werk... Even geduld...</p>
                </div>
                
                <div id="status"></div>
                <div id="results" class="result-output" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let isProcessing = false;

        async function callAgent(task, type = 'auto') {
            if (isProcessing) {
                alert('Er is al een agent actief. Wacht tot deze klaar is.');
                return;
            }

            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const status = document.getElementById('status');
            const submitBtn = document.getElementById('submitBtn');
            const statusBar = document.getElementById('statusBar');
            
            isProcessing = true;
            
            // Show loading
            loading.style.display = 'block';
            results.style.display = 'none';
            status.innerHTML = '';
            submitBtn.disabled = true;
            statusBar.textContent = '🔄 AI Agent Processing...';
            statusBar.className = 'status-bar';
            
            // Disable all action buttons
            document.querySelectorAll('.action-btn').forEach(btn => btn.disabled = true);
            
            try {
                console.log('🤖 Starting AI agent...');
                console.log('Task:', task);
                console.log('Type:', type);
                
                const response = await fetch('/api/ai/simple-agents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task: task,
                        type: type
                    })
                });
                
                const data = await response.json();
                
                loading.style.display = 'none';
                isProcessing = false;
                submitBtn.disabled = false;
                
                // Re-enable action buttons
                document.querySelectorAll('.action-btn').forEach(btn => btn.disabled = false);
                
                if (data.success) {
                    statusBar.textContent = '✅ AI Agents System Online - Ready for Tasks';
                    statusBar.className = 'status-bar online';
                    
                    status.innerHTML = `
                        <div class="status success">
                            ✅ Taak voltooid door: ${data.agent}
                        </div>
                        <div class="agent-info">
                            🤖 Agent: ${data.agent}<br>
                            ⏱️ Uitvoertijd: ${data.execution_time}<br>
                            🕒 Timestamp: ${new Date(data.timestamp).toLocaleString('nl-NL')}
                        </div>
                    `;
                    results.innerHTML = data.result || 'Geen resultaat ontvangen';
                    results.style.display = 'block';
                } else {
                    statusBar.textContent = '❌ Error in AI Agent System';
                    statusBar.className = 'status-bar';
                    
                    status.innerHTML = `
                        <div class="status error">
                            ❌ Error: ${data.error}
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Error:', error);
                loading.style.display = 'none';
                isProcessing = false;
                submitBtn.disabled = false;
                
                // Re-enable action buttons
                document.querySelectorAll('.action-btn').forEach(btn => btn.disabled = false);
                
                statusBar.textContent = '❌ Connection Error';
                statusBar.className = 'status-bar';
                
                status.innerHTML = `
                    <div class="status error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Quick task functions
        async function runTask(task, type) {
            await callAgent(task, type);
        }
        
        async function quickTask(type) {
            const tasks = {
                'analysis': 'Analyseer de huidige Yannova CRM performance en identificeer de belangrijkste verbeterpunten',
                'development': 'Genereer TypeScript code voor een moderne CRM dashboard component met Vue.js',
                'planning': 'Maak een gedetailleerd implementatieplan voor AI integratie in het CRM systeem',
                'ai_strategy': 'Ontwikkel een comprehensive AI strategie voor CRM optimalisatie en automation'
            };
            
            await callAgent(tasks[type] || 'Algemene CRM optimalisatie analyse', type);
        }
        
        // Custom task form
        document.getElementById('customTaskForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const taskType = document.getElementById('taskType').value;
            const taskDescription = document.getElementById('taskDescription').value;
            
            if (!taskDescription.trim()) {
                alert('Voer een taakbeschrijving in');
                return;
            }
            
            await callAgent(taskDescription, taskType);
        });
        
        // Auto-resize textarea
        document.getElementById('taskDescription').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Test API connection on load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/ai/simple-agents', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ task: 'System test' })
                });
                
                if (response.ok) {
                    console.log('✅ AI Agents API connected successfully');
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                console.error('❌ API connection failed:', error);
                document.getElementById('statusBar').textContent = '❌ AI Agents System Offline';
                document.getElementById('statusBar').className = 'status-bar';
            }
        });
        
        console.log('🤖 Yannova AI Agents Dashboard Loaded');
        console.log('💡 Tip: Klik op een agent card of gebruik quick actions!');
    </script>
</body>
</html> 