<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Versturen - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0D1117;
            color: #c9d1d9;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: #161B22;
            border-radius: 12px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid #30363d;
        }

        h1 {
            color: #7AA2F7;
            margin-bottom: 30px;
            text-align: center;
            font-size: 28px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #c9d1d9;
            font-weight: 500;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px 16px;
            background: #0D1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #c9d1d9;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #7AA2F7;
            box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.1);
        }

        textarea {
            min-height: 150px;
            resize: vertical;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 32px;
        }

        button {
            flex: 1;
            padding: 12px 24px;
            background: #7AA2F7;
            border: none;
            border-radius: 6px;
            color: #0D1117;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #5a82d7;
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        .preset-button {
            background: #2EA043;
        }

        .preset-button:hover {
            background: #238636;
        }

        .message {
            margin-top: 20px;
            padding: 16px;
            border-radius: 6px;
            text-align: center;
            display: none;
        }

        .success {
            background: rgba(46, 160, 67, 0.1);
            border: 1px solid #238636;
            color: #3fb950;
        }

        .error {
            background: rgba(248, 81, 73, 0.1);
            border: 1px solid #f85149;
            color: #f85149;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #30363d;
            border-top: 3px solid #7AA2F7;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Versturen</h1>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="to">Aan:</label>
                <input type="email" id="to" name="to" required value="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="subject">Onderwerp:</label>
                <input type="text" id="subject" name="subject" required placeholder="Bijv: Succes met je vlucht naar Malaga!">
            </div>

            <div class="form-group">
                <label for="content">Bericht:</label>
                <textarea id="content" name="content" required placeholder="Schrijf hier je bericht..."></textarea>
            </div>

            <div class="form-group">
                <label for="testType">Type email:</label>
                <select id="testType" name="testType">
                    <option value="direct">Direct versturen</option>
                    <option value="auto-reply">Auto-reply</option>
                    <option value="intelligent">AI-powered reply</option>
                </select>
            </div>

            <div class="button-group">
                <button type="button" class="preset-button" onclick="fillMalagaEmail()">
                    🏖️ Malaga Email
                </button>
                <button type="submit">
                    ✉️ Verstuur Email
                </button>
            </div>
        </form>

        <div id="message" class="message"></div>
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p style="margin-top: 12px;">Email wordt verstuurd...</p>
        </div>
    </div>

    <script>
        function fillMalagaEmail() {
            document.getElementById('to').value = '<EMAIL>';
            document.getElementById('subject').value = 'Succes met je vlucht naar Malaga! ✈️🏖️';
            document.getElementById('content').value = `Hoi!

Ik hoorde dat je naar Malaga vliegt - wat gaaf! 

Malaga is echt een geweldige bestemming. Een paar tips voor je reis:

🌞 Het weer is er meestal fantastisch - vergeet je zonnebrand niet!
🏖️ De stranden zijn prachtig, vooral Playa de la Malagueta
🍤 Probeer zeker de lokale specialiteit "espetos de sardinas" (sardines aan spiesjes)
🏰 Het Alcazaba kasteel is een must-see met prachtig uitzicht over de stad
🎨 Het Picasso museum is ook zeker de moeite waard

Geniet van je reis en kom veilig aan! Als je nog vragen hebt of hulp nodig hebt met iets, laat het me weten.

Veel plezier in Malaga! 🇪🇸

Groetjes,
Leon`;
        }

        document.getElementById('emailForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const loading = document.getElementById('loading');
            const message = document.getElementById('message');
            const form = e.target;
            
            // Reset en toon loading
            message.style.display = 'none';
            loading.style.display = 'block';
            
            const formData = {
                to: form.to.value,
                subject: form.subject.value,
                content: form.content.value,
                testType: form.testType.value
            };

            try {
                const response = await fetch('http://localhost:3001/api/email/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                loading.style.display = 'none';
                message.style.display = 'block';
                
                if (result.success) {
                    message.className = 'message success';
                    message.textContent = '✅ Email succesvol verstuurd!';
                } else {
                    message.className = 'message error';
                    message.textContent = '❌ ' + (result.message || 'Er ging iets mis bij het versturen');
                }
            } catch (error) {
                loading.style.display = 'none';
                message.style.display = 'block';
                message.className = 'message error';
                message.textContent = '❌ Fout: ' + error.message;
            }
        });

        // Vul direct de Malaga email in
        window.onload = () => {
            fillMalagaEmail();
        };
    </script>
</body>
</html> 