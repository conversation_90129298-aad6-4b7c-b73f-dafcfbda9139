<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Management Dashboard - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 1.5rem;
            margin-right: 12px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }

        .input-group input,
        .input-group textarea,
        .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus,
        .input-group textarea:focus,
        .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .result-container {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            text-align: center;
            color: #667eea;
            font-weight: 600;
        }

        .error {
            color: #e53e3e;
            background: #fed7d7;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .success {
            color: #38a169;
            background: #c6f6d5;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 5px;
        }

        .issue-item,
        .commit-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .issue-title,
        .commit-message {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .issue-meta,
        .commit-meta {
            font-size: 0.8rem;
            color: #718096;
        }

        .health-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }

        .health-excellent {
            background: #c6f6d5;
            color: #22543d;
        }

        .health-good {
            background: #bee3f8;
            color: #2a4365;
        }

        .health-fair {
            background: #feebc8;
            color: #744210;
        }

        .health-attention {
            background: #fed7d7;
            color: #742a2a;
        }

        .recommendation-list {
            list-style: none;
            padding: 0;
        }

        .recommendation-list li {
            background: white;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            border-left: 3px solid #667eea;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐙 GitHub Management Dashboard</h1>
            <p>Repository analyse, issue tracking en AI-enhanced code review voor Yannova CRM</p>
        </div>

        <div class="dashboard-grid">
            <!-- Repository Analysis -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">Repository Analyse</div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="analyzeRepository()">🔍 Analyseer Repository</button>
                    <button class="btn btn-secondary" onclick="getProjectHealth()">🏥 Project Health Check</button>
                </div>
                <div id="repoAnalysis" class="result-container" style="display: none;"></div>
            </div>

            <!-- Commit Tracking -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📝</div>
                    <div class="card-title">Recent Commits</div>
                </div>
                <div class="input-group">
                    <label>Aantal commits:</label>
                    <select id="commitCount">
                        <option value="5">5 commits</option>
                        <option value="10" selected>10 commits</option>
                        <option value="20">20 commits</option>
                        <option value="50">50 commits</option>
                    </select>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="getCommits()">📋 Haal Commits Op</button>
                    <button class="btn btn-warning" onclick="reviewLatestCommit()">🔍 Review Laatste Commit</button>
                </div>
                <div id="commitsResult" class="result-container" style="display: none;"></div>
            </div>

            <!-- Issue Management -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">Issue Management</div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="getIssues()">📋 Bekijk Open Issues</button>
                    <button class="btn btn-success" onclick="showCreateIssueForm()">➕ Nieuwe Issue</button>
                </div>
                
                <div id="createIssueForm" style="display: none;">
                    <div class="input-group">
                        <label>Titel:</label>
                        <input type="text" id="issueTitle" placeholder="Issue titel...">
                    </div>
                    <div class="input-group">
                        <label>Beschrijving:</label>
                        <textarea id="issueBody" rows="3" placeholder="Issue beschrijving..."></textarea>
                    </div>
                    <div class="input-group">
                        <label>Labels (komma gescheiden):</label>
                        <input type="text" id="issueLabels" placeholder="bug, enhancement, documentation">
                    </div>
                    <button class="btn btn-success" onclick="createIssue()">✅ Maak Issue Aan</button>
                    <button class="btn btn-secondary" onclick="hideCreateIssueForm()">❌ Annuleer</button>
                </div>
                
                <div id="issuesResult" class="result-container" style="display: none;"></div>
            </div>

            <!-- Code Review -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🔍</div>
                    <div class="card-title">AI Code Review</div>
                </div>
                <div class="input-group">
                    <label>Commit SHA:</label>
                    <input type="text" id="commitSha" placeholder="Voer commit SHA in...">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="performCodeReview()">🤖 Start AI Review</button>
                    <button class="btn btn-secondary" onclick="getLatestCommitSha()">🔄 Gebruik Laatste Commit</button>
                </div>
                <div id="codeReviewResult" class="result-container" style="display: none;"></div>
            </div>

            <!-- AI Project Insights -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🧠</div>
                    <div class="card-title">AI Project Insights</div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="getAIProjectAnalysis()">🚀 Volledige AI Analyse</button>
                    <button class="btn btn-warning" onclick="getTechnicalDebtAnalysis()">⚠️ Technical Debt Check</button>
                </div>
                <div id="aiInsightsResult" class="result-container" style="display: none;"></div>
            </div>

            <!-- File Explorer -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📁</div>
                    <div class="card-title">File Explorer</div>
                </div>
                <div class="input-group">
                    <label>Bestandspad:</label>
                    <input type="text" id="filePath" placeholder="bijv. package.json, README.md">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="getFileContent()">📄 Bekijk Bestand</button>
                    <button class="btn btn-secondary" onclick="setCommonFile('package.json')">📦 package.json</button>
                    <button class="btn btn-secondary" onclick="setCommonFile('README.md')">📚 README.md</button>
                </div>
                <div id="fileResult" class="result-container" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/github/repository';

        async function callGitHubAPI(action, params = {}) {
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action, ...params }),
                });

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                return { error: 'Network error: ' + error.message };
            }
        }

        function showLoading(containerId) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            container.innerHTML = '<div class="loading">⏳ Bezig met laden...</div>';
        }

        function showResult(containerId, content) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            container.innerHTML = content;
        }

        function showError(containerId, error) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            container.innerHTML = `<div class="error">❌ ${error}</div>`;
        }

        async function analyzeRepository() {
            showLoading('repoAnalysis');
            const result = await callGitHubAPI('analyze_repository');
            
            if (result.error) {
                showError('repoAnalysis', result.error);
                return;
            }

            const data = result.data;
            const html = `
                <h3>📊 Repository Statistieken</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">⭐ ${data.stats.stars}</div>
                        <div class="stat-label">Stars</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">🍴 ${data.stats.forks}</div>
                        <div class="stat-label">Forks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">🎯 ${data.stats.open_issues}</div>
                        <div class="stat-label">Open Issues</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">📦 ${Math.round(data.stats.size / 1024)} MB</div>
                        <div class="stat-label">Repository Size</div>
                    </div>
                </div>
                
                <h4 style="margin-top: 20px;">🔀 Gebruikte Talen:</h4>
                <div style="margin-top: 10px;">
                    ${Object.entries(data.languages).map(([lang, bytes]) => 
                        `<span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; margin: 2px; display: inline-block; font-size: 0.8rem;">${lang}: ${Math.round(bytes / 1024)}KB</span>`
                    ).join('')}
                </div>

                <h4 style="margin-top: 20px;">📝 Recente Commits (${data.recent_commits.length}):</h4>
                <div style="margin-top: 10px;">
                    ${data.recent_commits.slice(0, 5).map(commit => `
                        <div class="commit-item">
                            <div class="commit-message">${commit.message}</div>
                            <div class="commit-meta">👤 ${commit.author} • 📅 ${new Date(commit.date).toLocaleDateString('nl-NL')}</div>
                        </div>
                    `).join('')}
                </div>

                <h4 style="margin-top: 20px;">🎯 Open Issues (${data.open_issues.length}):</h4>
                <div style="margin-top: 10px;">
                    ${data.open_issues.slice(0, 3).map(issue => `
                        <div class="issue-item">
                            <div class="issue-title">#${issue.number}: ${issue.title}</div>
                            <div class="issue-meta">🏷️ ${issue.labels.join(', ') || 'Geen labels'} • 📅 ${new Date(issue.created_at).toLocaleDateString('nl-NL')}</div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            showResult('repoAnalysis', html);
        }

        async function getCommits() {
            const count = document.getElementById('commitCount').value;
            showLoading('commitsResult');
            
            const result = await callGitHubAPI('get_commits', { count: parseInt(count) });
            
            if (result.error) {
                showError('commitsResult', result.error);
                return;
            }

            const html = `
                <h3>📝 Laatste ${result.data.length} Commits</h3>
                <div style="margin-top: 15px;">
                    ${result.data.map(commit => `
                        <div class="commit-item">
                            <div class="commit-message">${commit.message}</div>
                            <div class="commit-meta">
                                👤 ${commit.author} • 📅 ${new Date(commit.date).toLocaleDateString('nl-NL')} • 
                                🔗 <code style="background: #f1f5f9; padding: 2px 6px; border-radius: 4px;">${commit.sha.substring(0, 8)}</code>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            showResult('commitsResult', html);
        }

        async function getIssues() {
            showLoading('issuesResult');
            const result = await callGitHubAPI('get_issues');
            
            if (result.error) {
                showError('issuesResult', result.error);
                return;
            }

            const html = `
                <h3>🎯 Open Issues (${result.data.length})</h3>
                <div style="margin-top: 15px;">
                    ${result.data.map(issue => `
                        <div class="issue-item">
                            <div class="issue-title">#${issue.number}: ${issue.title}</div>
                            <div class="issue-meta">
                                🏷️ ${issue.labels.join(', ') || 'Geen labels'} • 
                                👤 ${issue.assignee || 'Niet toegewezen'} • 
                                📅 ${new Date(issue.created_at).toLocaleDateString('nl-NL')}
                            </div>
                            ${issue.body ? `<div style="margin-top: 8px; font-size: 0.9rem; color: #4a5568;">${issue.body.substring(0, 100)}...</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            
            showResult('issuesResult', html);
        }

        function showCreateIssueForm() {
            document.getElementById('createIssueForm').style.display = 'block';
        }

        function hideCreateIssueForm() {
            document.getElementById('createIssueForm').style.display = 'none';
            document.getElementById('issueTitle').value = '';
            document.getElementById('issueBody').value = '';
            document.getElementById('issueLabels').value = '';
        }

        async function createIssue() {
            const title = document.getElementById('issueTitle').value.trim();
            const body = document.getElementById('issueBody').value.trim();
            const labelsStr = document.getElementById('issueLabels').value.trim();
            
            if (!title) {
                alert('Titel is verplicht!');
                return;
            }

            const labels = labelsStr ? labelsStr.split(',').map(l => l.trim()) : [];
            
            showLoading('issuesResult');
            const result = await callGitHubAPI('create_issue', { title, body, labels });
            
            if (result.error) {
                showError('issuesResult', result.error);
                return;
            }

            hideCreateIssueForm();
            showResult('issuesResult', `
                <div class="success">✅ ${result.message}</div>
                <div class="issue-item" style="margin-top: 15px;">
                    <div class="issue-title">#${result.data.number}: ${result.data.title}</div>
                    <div class="issue-meta">🏷️ ${result.data.labels.join(', ') || 'Geen labels'}</div>
                </div>
            `);
        }

        async function performCodeReview() {
            const commitSha = document.getElementById('commitSha').value.trim();
            
            if (!commitSha) {
                alert('Commit SHA is verplicht!');
                return;
            }

            showLoading('codeReviewResult');
            const result = await callGitHubAPI('code_review', { commitSha });
            
            if (result.error) {
                showError('codeReviewResult', result.error);
                return;
            }

            const review = result.data;
            const html = `
                <h3>🤖 AI Code Review voor ${commitSha.substring(0, 8)}</h3>
                
                <h4 style="margin-top: 20px;">📁 Gewijzigde Bestanden (${review.files.length}):</h4>
                <div style="margin-top: 10px;">
                    ${review.files.map(file => `
                        <div style="background: white; padding: 10px; border-radius: 8px; margin-bottom: 8px;">
                            <strong>${file.filename}</strong>
                            <div style="font-size: 0.8rem; color: #718096; margin-top: 4px;">
                                ➕ ${file.additions} toevoegingen • ➖ ${file.deletions} verwijderingen • 📊 ${file.changes} wijzigingen
                            </div>
                        </div>
                    `).join('')}
                </div>

                ${review.security_issues.length > 0 ? `
                    <h4 style="margin-top: 20px; color: #e53e3e;">🛡️ Security Issues:</h4>
                    <ul class="recommendation-list">
                        ${review.security_issues.map(issue => `<li style="border-left-color: #e53e3e;">${issue}</li>`).join('')}
                    </ul>
                ` : ''}

                ${review.performance_notes.length > 0 ? `
                    <h4 style="margin-top: 20px; color: #ed8936;">⚡ Performance Notes:</h4>
                    <ul class="recommendation-list">
                        ${review.performance_notes.map(note => `<li style="border-left-color: #ed8936;">${note}</li>`).join('')}
                    </ul>
                ` : ''}

                ${review.suggestions.length > 0 ? `
                    <h4 style="margin-top: 20px; color: #667eea;">💡 Suggesties:</h4>
                    <ul class="recommendation-list">
                        ${review.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                ` : ''}
            `;
            
            showResult('codeReviewResult', html);
        }

        async function getLatestCommitSha() {
            showLoading('codeReviewResult');
            const result = await callGitHubAPI('get_commits', { count: 1 });
            
            if (result.error) {
                showError('codeReviewResult', result.error);
                return;
            }

            if (result.data.length > 0) {
                document.getElementById('commitSha').value = result.data[0].sha;
                showResult('codeReviewResult', `<div class="success">✅ Laatste commit SHA ingesteld: ${result.data[0].sha.substring(0, 8)}</div>`);
            }
        }

        async function getAIProjectAnalysis() {
            showLoading('aiInsightsResult');
            const result = await callGitHubAPI('ai_project_analysis');
            
            if (result.error) {
                showError('aiInsightsResult', result.error);
                return;
            }

            const insights = result.data.ai_insights;
            const healthClass = `health-${insights.project_health.toLowerCase().replace(' ', '-')}`;
            
            const html = `
                <h3>🧠 AI Project Analyse</h3>
                
                <h4 style="margin-top: 20px;">🏥 Project Health: 
                    <span class="health-badge ${healthClass}">${insights.project_health}</span>
                </h4>

                <h4 style="margin-top: 20px;">💡 Aanbevelingen:</h4>
                <ul class="recommendation-list">
                    ${insights.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>

                <h4 style="margin-top: 20px;">🚀 Sprint Suggesties:</h4>
                <ul class="recommendation-list">
                    ${insights.next_sprint_suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>

                <h4 style="margin-top: 20px;">⚠️ Technical Debt (${insights.technical_debt.level}):</h4>
                ${insights.technical_debt.areas.length > 0 ? `
                    <div style="margin-top: 10px;">
                        <strong>Probleemgebieden:</strong>
                        <ul class="recommendation-list">
                            ${insights.technical_debt.areas.map(area => `<li style="border-left-color: #ed8936;">${area}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                <div style="margin-top: 15px;">
                    <strong>Prioriteit Acties:</strong>
                    <ul class="recommendation-list">
                        ${insights.technical_debt.priority_actions.map(action => `<li>${action}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            showResult('aiInsightsResult', html);
        }

        async function getFileContent() {
            const path = document.getElementById('filePath').value.trim();
            
            if (!path) {
                alert('Bestandspad is verplicht!');
                return;
            }

            showLoading('fileResult');
            const result = await callGitHubAPI('get_file', { path });
            
            if (result.error) {
                showError('fileResult', result.error);
                return;
            }

            const html = `
                <h3>📄 ${result.data.path}</h3>
                <div style="margin-top: 15px;">
                    <pre style="background: #1a202c; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 0.8rem; line-height: 1.4;">${result.data.content}</pre>
                </div>
            `;
            
            showResult('fileResult', html);
        }

        function setCommonFile(filename) {
            document.getElementById('filePath').value = filename;
        }

        async function getProjectHealth() {
            await getAIProjectAnalysis();
        }

        async function getTechnicalDebtAnalysis() {
            await getAIProjectAnalysis();
        }

        async function reviewLatestCommit() {
            await getLatestCommitSha();
            setTimeout(() => {
                if (document.getElementById('commitSha').value) {
                    performCodeReview();
                }
            }, 1000);
        }

        // Auto-load repository analysis on page load
        window.addEventListener('load', () => {
            console.log('🐙 GitHub Management Dashboard geladen!');
            console.log('💡 Tip: Start met "Analyseer Repository" voor een volledig overzicht');
        });
    </script>
</body>
</html> 