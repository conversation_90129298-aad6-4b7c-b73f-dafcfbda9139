<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Dashboard - Yannova</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0D1117;
            color: #c9d1d9;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: #161B22;
            border-right: 1px solid #30363d;
            padding: 20px;
            overflow-y: auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #7AA2F7;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: #1F2937;
        }

        .nav-item.active {
            background: #7AA2F7;
            color: #0D1117;
        }

        .nav-icon {
            font-size: 20px;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #30363d;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #7AA2F7;
            color: #0D1117;
        }

        .btn-primary:hover {
            background: #5a82d7;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #161B22;
            padding: 24px;
            border-radius: 8px;
            border: 1px solid #30363d;
        }

        .stat-label {
            color: #8898aa;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #7AA2F7;
        }

        .stat-change {
            font-size: 14px;
            margin-top: 8px;
        }

        .stat-change.positive {
            color: #2EA043;
        }

        .stat-change.negative {
            color: #f85149;
        }

        /* Tables */
        .data-table {
            background: #161B22;
            border-radius: 8px;
            border: 1px solid #30363d;
            overflow: hidden;
        }

        .table-header {
            padding: 16px 24px;
            border-bottom: 1px solid #30363d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 12px 24px;
            background: #0D1117;
            font-weight: 600;
            font-size: 14px;
            color: #8898aa;
        }

        td {
            padding: 16px 24px;
            border-top: 1px solid #30363d;
        }

        tr:hover {
            background: #1F2937;
        }

        /* Email Badge */
        .email-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-new {
            background: #7AA2F7;
            color: #0D1117;
        }

        .badge-replied {
            background: #2EA043;
            color: white;
        }

        .badge-urgent {
            background: #f85149;
            color: white;
        }

        /* Priority Badge */
        .priority {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .priority-high {
            background: #f85149;
            color: white;
        }

        .priority-medium {
            background: #ffa657;
            color: #0D1117;
        }

        .priority-low {
            background: #30363d;
            color: #c9d1d9;
        }

        /* Search Bar */
        .search-bar {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            background: #0D1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #c9d1d9;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #8898aa;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background: #161B22;
            margin: 50px auto;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            border-radius: 8px;
            border: 1px solid #30363d;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            font-size: 24px;
            cursor: pointer;
            color: #8898aa;
        }

        .close-btn:hover {
            color: #c9d1d9;
        }

        /* Email Thread */
        .email-thread {
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .email-message {
            background: #0D1117;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .email-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 14px;
            color: #8898aa;
        }

        .email-body {
            line-height: 1.6;
        }

        .reply-box {
            margin-top: 20px;
        }

        .reply-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            background: #0D1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #c9d1d9;
            font-family: inherit;
            resize: vertical;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 40px;
            color: #8898aa;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <span>🚀</span>
            <span>Yannova CRM</span>
        </div>
        
        <nav>
            <div class="nav-item active" onclick="showSection('dashboard')">
                <span class="nav-icon">📊</span>
                <span>Dashboard</span>
            </div>
            <div class="nav-item" onclick="showSection('contacts')">
                <span class="nav-icon">👥</span>
                <span>Contacten</span>
            </div>
            <div class="nav-item" onclick="showSection('emails')">
                <span class="nav-icon">📧</span>
                <span>Emails</span>
            </div>
            <div class="nav-item" onclick="showSection('tasks')">
                <span class="nav-icon">✅</span>
                <span>Taken</span>
            </div>
            <div class="nav-item" onclick="showSection('deals')">
                <span class="nav-icon">💰</span>
                <span>Deals</span>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="section">
            <div class="header">
                <h1 class="page-title">Dashboard</h1>
                <div class="header-actions">
                    <div class="search-bar">
                        <span class="search-icon">🔍</span>
                        <input type="text" class="search-input" placeholder="Zoeken...">
                    </div>
                    <button class="btn btn-primary" onclick="composeEmail()">
                        ✉️ Nieuwe Email
                    </button>
                </div>
            </div>

            <!-- Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-label">Totaal Contacten</div>
                    <div class="stat-value" id="total-contacts">0</div>
                    <div class="stat-change positive">+12% deze maand</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Ongelezen Emails</div>
                    <div class="stat-value" id="unread-emails">0</div>
                    <div class="stat-change negative">3 urgent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Open Taken</div>
                    <div class="stat-value" id="open-tasks">0</div>
                    <div class="stat-change">5 vandaag</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Actieve Deals</div>
                    <div class="stat-value" id="active-deals">0</div>
                    <div class="stat-change positive">€45.000 pipeline</div>
                </div>
            </div>

            <!-- Recent Emails -->
            <div class="data-table">
                <div class="table-header">
                    <h2 class="table-title">Recente Emails</h2>
                    <a href="#" onclick="showSection('emails')">Bekijk alle →</a>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>Van</th>
                            <th>Onderwerp</th>
                            <th>Ontvangen</th>
                            <th>Status</th>
                            <th>Acties</th>
                        </tr>
                    </thead>
                    <tbody id="recent-emails">
                        <tr>
                            <td colspan="5" class="loading">Emails laden...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Other sections (hidden by default) -->
        <div id="contacts-section" class="section" style="display: none;">
            <div class="header">
                <h1 class="page-title">Contacten</h1>
                <button class="btn btn-primary">+ Nieuw Contact</button>
            </div>
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>Naam</th>
                            <th>Email</th>
                            <th>Bedrijf</th>
                            <th>Status</th>
                            <th>Laatste Contact</th>
                        </tr>
                    </thead>
                    <tbody id="contacts-list">
                        <tr>
                            <td colspan="5" class="loading">Contacten laden...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Email Modal -->
    <div id="email-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Email Conversatie</h2>
                <span class="close-btn" onclick="closeModal()">&times;</span>
            </div>
            <div id="email-thread" class="email-thread"></div>
            <div class="reply-box">
                <textarea class="reply-textarea" placeholder="Schrijf een antwoord..."></textarea>
                <div style="margin-top: 12px; text-align: right;">
                    <button class="btn btn-primary" onclick="sendReply()">Verstuur Antwoord</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard
        async function initDashboard() {
            await loadStats();
            await loadRecentEmails();
            await loadContacts();
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/crm/stats');
                const stats = await response.json();
                
                document.getElementById('total-contacts').textContent = stats.totalContacts || 0;
                document.getElementById('unread-emails').textContent = stats.unreadEmails || 0;
                document.getElementById('open-tasks').textContent = stats.openTasks || 0;
                document.getElementById('active-deals').textContent = stats.activeDeals || 0;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load recent emails
        async function loadRecentEmails() {
            try {
                const response = await fetch('/api/crm/emails?limit=5');
                const emails = await response.json();
                
                const tbody = document.getElementById('recent-emails');
                if (emails.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">Geen emails gevonden</td></tr>';
                    return;
                }
                
                tbody.innerHTML = emails.map(email => `
                    <tr onclick="viewEmail('${email.id}')">
                        <td>${email.contact.email}</td>
                        <td>${email.subject || 'Geen onderwerp'}</td>
                        <td>${new Date(email.createdAt).toLocaleDateString('nl-NL')}</td>
                        <td>
                            <span class="email-badge ${email.readAt ? 'badge-replied' : 'badge-new'}">
                                ${email.readAt ? 'Gelezen' : 'Nieuw'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm" onclick="event.stopPropagation(); viewEmail('${email.id}')">
                                Bekijk
                            </button>
                        </td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading emails:', error);
            }
        }

        // Load contacts
        async function loadContacts() {
            try {
                const response = await fetch('/api/contacts');
                const contacts = await response.json();
                
                const tbody = document.getElementById('contacts-list');
                if (contacts.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">Geen contacten gevonden</td></tr>';
                    return;
                }
                
                tbody.innerHTML = contacts.map(contact => `
                    <tr>
                        <td>${contact.name || '-'}</td>
                        <td>${contact.email}</td>
                        <td>${contact.company || '-'}</td>
                        <td>${contact.status}</td>
                        <td>${contact.lastContact ? new Date(contact.lastContact).toLocaleDateString('nl-NL') : '-'}</td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading contacts:', error);
            }
        }

        // Show section
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(s => s.style.display = 'none');
            
            // Show selected section
            document.getElementById(`${section}-section`).style.display = 'block';
            
            // Update nav
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            event.target.closest('.nav-item').classList.add('active');
        }

        // View email
        async function viewEmail(emailId) {
            const modal = document.getElementById('email-modal');
            modal.style.display = 'block';
            
            try {
                const response = await fetch(`/api/crm/conversation/${emailId}`);
                const conversation = await response.json();
                
                const thread = document.getElementById('email-thread');
                thread.innerHTML = `
                    <div class="email-message">
                        <div class="email-header">
                            <strong>${conversation.contact.email}</strong>
                            <span>${new Date(conversation.createdAt).toLocaleString('nl-NL')}</span>
                        </div>
                        <div class="email-body">${conversation.content}</div>
                    </div>
                `;
                
                // Mark as read
                await fetch(`/api/crm/conversation/${emailId}/read`, { method: 'POST' });
                await loadRecentEmails();
                await loadStats();
            } catch (error) {
                console.error('Error loading email:', error);
            }
        }

        // Close modal
        function closeModal() {
            document.getElementById('email-modal').style.display = 'none';
        }

        // Compose new email
        function composeEmail() {
            window.location.href = '/send-email.html';
        }

        // Send reply
        async function sendReply() {
            alert('Reply functie komt binnenkort!');
        }

        // Initialize on load
        window.onload = initDashboard;
        
        // Refresh every 30 seconds
        setInterval(initDashboard, 30000);
    </script>
</body>
</html> 