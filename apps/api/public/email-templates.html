<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Templates - Yannova CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0D1117;
            color: #c9d1d9;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #7AA2F7;
            margin-bottom: 40px;
            text-align: center;
            font-size: 36px;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .template-card {
            background: #161B22;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #30363d;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            border-color: #7AA2F7;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(122, 162, 247, 0.1);
        }

        .template-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .template-icon {
            font-size: 32px;
        }

        .template-title {
            font-size: 20px;
            font-weight: 600;
            color: #c9d1d9;
        }

        .template-description {
            color: #8898aa;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 16px;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #c9d1d9;
            font-size: 14px;
            font-weight: 500;
        }

        input, textarea, select {
            width: 100%;
            padding: 10px 14px;
            background: #0D1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #c9d1d9;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #7AA2F7;
            box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.1);
        }

        textarea {
            min-height: 80px;
            resize: vertical;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        button {
            flex: 1;
            padding: 10px 20px;
            background: #7AA2F7;
            border: none;
            border-radius: 6px;
            color: #0D1117;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #5a82d7;
            transform: translateY(-1px);
        }

        .preview-button {
            background: #2EA043;
        }

        .preview-button:hover {
            background: #238636;
        }

        .message {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: 6px;
            font-weight: 500;
            display: none;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .success {
            background: rgba(46, 160, 67, 0.9);
            color: white;
        }

        .error {
            background: rgba(248, 81, 73, 0.9);
            color: white;
        }

        .add-item-button {
            background: #30363d;
            padding: 8px 16px;
            font-size: 14px;
            margin-top: 8px;
        }

        .add-item-button:hover {
            background: #484f58;
        }

        .quote-items {
            background: #0D1117;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .quote-item {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 8px;
            margin-bottom: 8px;
            align-items: center;
        }

        .remove-button {
            background: #f85149;
            padding: 6px 12px;
            font-size: 12px;
        }

        .remove-button:hover {
            background: #da3633;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Templates</h1>
        
        <div class="template-grid">
            <!-- Welcome Email Template -->
            <div class="template-card">
                <div class="template-header">
                    <span class="template-icon">👋</span>
                    <h2 class="template-title">Welcome Email</h2>
                </div>
                <p class="template-description">
                    Stuur een welkomstmail naar nieuwe gebruikers met alle belangrijke informatie.
                </p>
                <form id="welcomeForm">
                    <div class="form-group">
                        <label for="welcome-to">Email adres:</label>
                        <input type="email" id="welcome-to" required value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="welcome-name">Naam:</label>
                        <input type="text" id="welcome-name" required value="Leon">
                    </div>
                    <div class="button-group">
                        <button type="submit">Verstuur</button>
                        <button type="button" class="preview-button" onclick="previewTemplate('welcome')">Preview</button>
                    </div>
                </form>
            </div>

            <!-- Quote Email Template -->
            <div class="template-card">
                <div class="template-header">
                    <span class="template-icon">💰</span>
                    <h2 class="template-title">Offerte Email</h2>
                </div>
                <p class="template-description">
                    Verstuur professionele offertes met itemized pricing en automatische BTW berekening.
                </p>
                <form id="quoteForm">
                    <div class="form-group">
                        <label for="quote-to">Email adres:</label>
                        <input type="email" id="quote-to" required value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="quote-client">Klant naam:</label>
                        <input type="text" id="quote-client" required value="Innovar Labs">
                    </div>
                    <div class="form-group">
                        <label for="quote-project">Project naam:</label>
                        <input type="text" id="quote-project" required value="CRM Systeem">
                    </div>
                    <div class="form-group">
                        <label>Items:</label>
                        <div id="quoteItems" class="quote-items"></div>
                        <button type="button" class="add-item-button" onclick="addQuoteItem()">+ Item toevoegen</button>
                    </div>
                    <div class="form-group">
                        <label for="quote-valid">Geldig tot:</label>
                        <input type="date" id="quote-valid" required>
                    </div>
                    <div class="form-group">
                        <label for="quote-notes">Opmerkingen:</label>
                        <textarea id="quote-notes" placeholder="Optionele opmerkingen..."></textarea>
                    </div>
                    <div class="button-group">
                        <button type="submit">Verstuur</button>
                        <button type="button" class="preview-button" onclick="previewTemplate('quote')">Preview</button>
                    </div>
                </form>
            </div>

            <!-- Bot Notification Template -->
            <div class="template-card">
                <div class="template-header">
                    <span class="template-icon">🤖</span>
                    <h2 class="template-title">Bot Notification</h2>
                </div>
                <p class="template-description">
                    Ontvang notificaties van je Telegram of WhatsApp bots met gedetailleerde informatie.
                </p>
                <form id="botForm">
                    <div class="form-group">
                        <label for="bot-to">Email adres:</label>
                        <input type="email" id="bot-to" required value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="bot-name">Bot naam:</label>
                        <input type="text" id="bot-name" required value="Yannova Support Bot">
                    </div>
                    <div class="form-group">
                        <label for="bot-type">Event type:</label>
                        <select id="bot-type" required>
                            <option value="message">Message</option>
                            <option value="error">Error</option>
                            <option value="alert">Alert</option>
                            <option value="update">Update</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bot-user">Gebruiker (optioneel):</label>
                        <input type="text" id="bot-user" placeholder="@username">
                    </div>
                    <div class="form-group">
                        <label for="bot-message">Bericht:</label>
                        <textarea id="bot-message" required>Er is een nieuwe support aanvraag binnengekomen.</textarea>
                    </div>
                    <div class="button-group">
                        <button type="submit">Verstuur</button>
                        <button type="button" class="preview-button" onclick="previewTemplate('bot')">Preview</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="message" class="message"></div>
    </div>

    <script>
        // Quote items management
        let quoteItems = [
            { description: 'Bot Development', price: 2500, quantity: 1 },
            { description: 'CRM Integratie', price: 1500, quantity: 1 },
            { description: 'Support (per maand)', price: 250, quantity: 12 }
        ];

        function renderQuoteItems() {
            const container = document.getElementById('quoteItems');
            container.innerHTML = quoteItems.map((item, index) => `
                <div class="quote-item">
                    <input type="text" value="${item.description}" onchange="updateQuoteItem(${index}, 'description', this.value)" placeholder="Beschrijving">
                    <input type="number" value="${item.price}" onchange="updateQuoteItem(${index}, 'price', parseFloat(this.value))" placeholder="Prijs">
                    <input type="number" value="${item.quantity}" onchange="updateQuoteItem(${index}, 'quantity', parseInt(this.value))" placeholder="Aantal">
                    <button type="button" class="remove-button" onclick="removeQuoteItem(${index})">Verwijder</button>
                </div>
            `).join('');
        }

        function addQuoteItem() {
            quoteItems.push({ description: '', price: 0, quantity: 1 });
            renderQuoteItems();
        }

        function removeQuoteItem(index) {
            quoteItems.splice(index, 1);
            renderQuoteItems();
        }

        function updateQuoteItem(index, field, value) {
            quoteItems[index][field] = value;
        }

        // Initialize
        renderQuoteItems();
        
        // Set default date to 30 days from now
        const validUntil = new Date();
        validUntil.setDate(validUntil.getDate() + 30);
        document.getElementById('quote-valid').value = validUntil.toISOString().split('T')[0];

        // Form handlers
        document.getElementById('welcomeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await sendTemplate('welcome', {
                to: document.getElementById('welcome-to').value,
                data: {
                    name: document.getElementById('welcome-name').value,
                    email: document.getElementById('welcome-to').value
                }
            });
        });

        document.getElementById('quoteForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await sendTemplate('quote', {
                to: document.getElementById('quote-to').value,
                data: {
                    clientName: document.getElementById('quote-client').value,
                    projectName: document.getElementById('quote-project').value,
                    items: quoteItems.filter(item => item.description),
                    validUntil: new Date(document.getElementById('quote-valid').value).toLocaleDateString('nl-NL'),
                    notes: document.getElementById('quote-notes').value || undefined
                }
            });
        });

        document.getElementById('botForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await sendTemplate('bot-notification', {
                to: document.getElementById('bot-to').value,
                data: {
                    botName: document.getElementById('bot-name').value,
                    eventType: document.getElementById('bot-type').value,
                    userName: document.getElementById('bot-user').value || undefined,
                    message: document.getElementById('bot-message').value,
                    timestamp: new Date().toISOString(),
                    metadata: {
                        source: 'Web Form',
                        ip: '127.0.0.1',
                        userAgent: navigator.userAgent
                    }
                }
            });
        });

        async function sendTemplate(templateType, payload) {
            try {
                const response = await fetch('http://localhost:3001/api/email/send-template', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ templateType, ...payload })
                });

                const result = await response.json();
                showMessage(result.success ? 'success' : 'error', result.message || 'Er ging iets mis');
            } catch (error) {
                showMessage('error', 'Fout: ' + error.message);
            }
        }

        function showMessage(type, text) {
            const message = document.getElementById('message');
            message.className = `message ${type}`;
            message.textContent = text;
            message.style.display = 'block';
            
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        function previewTemplate(type) {
            // In een echte implementatie zou dit een preview modal openen
            showMessage('success', `Preview voor ${type} template komt binnenkort!`);
        }
    </script>
</body>
</html> 