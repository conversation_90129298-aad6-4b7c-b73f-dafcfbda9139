// apps/workers/email-handler.ts
export interface Env {
  CRM_API_URL: string;
  CRM_API_KEY: string;
  PERSONAL_EMAIL: string;
}

// Cloudflare Workers types
interface ForwardableEmailMessage {
  from: string;
  to: string;
  subject: string;
  text(): Promise<string>;
  forward(email: string): Promise<void>;
  headers: Map<string, string>;
}

interface ExecutionContext {
  waitUntil(promise: Promise<any>): void;
  passThroughOnException(): void;
}

export default {
  async email(message: ForwardableEmailMessage, env: Env, ctx: ExecutionContext): Promise<void> {
    try {
      const { from, to, subject } = message;
      const content = await message.text();
      
      // Extract email content
      const emailData = {
        from: from,
        to: to,
        subject: subject,
        content: content,
        messageId: message.headers.get('message-id'),
        timestamp: new Date().toISOString()
      };
      
      // Send to CRM API
      const response = await fetch(`${env.CRM_API_URL}/api/email/webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${env.CRM_API_KEY}`
        },
        body: JSON.stringify(emailData)
      });
      
      if (!response.ok) {
        console.error(`CRM API error: ${response.status}`);
      }
      
      // Forward to personal email
      await message.forward(env.PERSONAL_EMAIL);
      
    } catch (error) {
      console.error('Email processing error:', error);
      // Still forward the email even if CRM fails
      await message.forward(env.PERSONAL_EMAIL);
    }
  }
};