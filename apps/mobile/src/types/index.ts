export interface Contact {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  company?: string;
  status: 'LEAD' | 'PROSPECT' | 'CUSTOMER';
  lastContact?: string;
  conversationCount: number;
  dealCount: number;
}

export interface Conversation {
  id: string;
  type: 'EMAIL' | 'CHAT' | 'CALL';
  channel: 'EMAIL' | 'WHATSAPP' | 'TELEGRAM';
  subject?: string;
  content: string;
  direction: 'INBOUND' | 'OUTBOUND';
  createdAt: string;
  sentiment?: number;
}