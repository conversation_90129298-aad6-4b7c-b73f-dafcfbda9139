import { Contact, Conversation } from '../types';
import axios from 'axios';

class APIService {
  private baseURL = 'https://api.yannova.be';
  
  async getContacts(): Promise<Contact[]> {
    const response = await fetch(`${this.baseURL}/api/contacts`);
    return response.json();
  }
  
  async getContactConversations(contactId: string): Promise<Conversation[]> {
    const response = await fetch(`${this.baseURL}/api/contacts/${contactId}/conversations`);
    return response.json();
  }
  
  async createTask(data: { contactId: string; title: string; type: string; dueDate?: string }) {
    const response = await fetch(`${this.baseURL}/api/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    return response.json();
  }
  
  async sendWhatsAppMessage(contactId: string, message: string) {
    const response = await fetch(`${this.baseURL}/api/whatsapp/send`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ contactId, message })
    });
    return response.json();
  }

  async askAI(question: string) {
    return axios.post('/api/ai/ask', { question });
  }
}

export const api = new APIService();