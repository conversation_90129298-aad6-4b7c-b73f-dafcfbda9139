#!/usr/bin/env python3
"""
Yannova CRM AI Workflow Manager
Geautomatiseerde workflows met LM Studio & OpenAI fallback
Nu beschik<PERSON>ar voor alle projecten!
"""

import os
import json
import subprocess
import sys
import requests
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path
import time
import threading
import difflib
import tempfile
import subprocess
try:
    import colorama
    colorama.init()
    HAS_COLORAMA = True
except ImportError:
    HAS_COLORAMA = False

# Global configuration for all projects
GLOBAL_CONFIG_DIR = Path.home() / '.yannova_ai'
GLOBAL_CONFIG_FILE = GLOBAL_CONFIG_DIR / 'config.json'
GLOBAL_LOG_DIR = GLOBAL_CONFIG_DIR / 'logs'

# LM Studio configuration
LM_STUDIO_BASE_URL = "http://127.0.0.1:1234/v1"
LM_STUDIO_MODEL = "mistral-7b-instruct-v0.1"

# Handle optional dependencies gracefully
DEPENDENCIES_STATUS = {
    'dotenv': False,
    'openai': False,
    'composio': False,
    'git': False,
    'requests': True,  # Should be available in standard Python
    'lm_studio': False,
    'watchdog': False
}

# Try importing dotenv
try:
    from dotenv import load_dotenv
    DEPENDENCIES_STATUS['dotenv'] = True
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv niet geïnstalleerd. Run: pip install python-dotenv")

# Try importing OpenAI
try:
    from openai import OpenAI
    DEPENDENCIES_STATUS['openai'] = True
except ImportError:
    print("⚠️  openai niet geïnstalleerd. Run: pip install openai")

# Try importing Composio (optional)
try:
    from composio_openai import ComposioToolSet, Action
    DEPENDENCIES_STATUS['composio'] = True
except ImportError as e:
    print("ℹ️  composio-openai niet geïnstalleerd (optioneel). Run: pip install composio-openai")
    DEPENDENCIES_STATUS['composio'] = False
    COMPOSIO_ERROR = str(e)

# Try importing GitPython as fallback
try:
    import git
    DEPENDENCIES_STATUS['git'] = True
except ImportError:
    print("ℹ️  GitPython niet geïnstalleerd (optioneel). Run: pip install GitPython")
    DEPENDENCIES_STATUS['git'] = False

# Try importing watchdog for live file monitoring
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    DEPENDENCIES_STATUS['watchdog'] = True
except ImportError:
    print("ℹ️  watchdog niet geïnstalleerd (vereist voor live wijzigingen). Run: pip install watchdog")
    DEPENDENCIES_STATUS['watchdog'] = False


class YannovaFileWatcher(FileSystemEventHandler):
    """Monitor bestandswijzigingen in real-time"""
    def __init__(self, callback):
        self.callback = callback
        
    def on_modified(self, event):
        if not event.is_directory:
            self.callback(event.src_path)

class YannovaCRMWorkflowManager:
    def __init__(self):
        """Initialiseer met dependency checks"""
        print("\n" + "="*50)
        print("🔍 Controleer afhankelijkheden...")
        
        if not DEPENDENCIES_STATUS['watchdog']:
            print("⚠️  Live monitoring niet beschikbaar zonder watchdog")
            print("   Installeer met: pip install watchdog")
            
        if not DEPENDENCIES_STATUS['composio']:
            print(f"⚠️  GitHub integratie niet beschikbaar: {COMPOSIO_ERROR}")
            print("   Installeer optioneel met: pip install composio-openai")
            
        if not DEPENDENCIES_STATUS['git']:
            print("⚠️  Git integratie beperkt zonder GitPython")
            print("   Installeer optioneel met: pip install GitPython")
            
        print("="*50 + "\n")
        self.project_root = os.getcwd()
        self.setup_global_config()
        self.setup_logging()
        
        # Initialize clients as None
        self.openai_client = None
        self.composio_toolset = None
        self.lm_studio_available = False
        self.ai_available = False
        self.file_watcher = None
        self.watching = False
        self.last_modified = {}
        self.crm_events = []
        self.crm_contacts = self.load_crm_contacts()
        self.file_history = {}
        self.diff_viewer = "terminal"  # terminal, browser, editor

    def set_diff_viewer(self, viewer):
        """Stel de preview methode in"""
        valid_viewers = ["terminal", "browser", "editor"]
        if viewer in valid_viewers:
            self.diff_viewer = viewer
            print(f"🔍 Preview methode ingesteld op: {viewer}")
        else:
            print(f"⚠️  Ongeldige viewer: {viewer}. Kies uit: {', '.join(valid_viewers)}")

    def load_crm_contacts(self):
        """Laad CRM contacten uit database"""
        try:
            # In een echte implementatie zou je dit uit je database halen
            return [
                {"id": 1, "name": "Jan Jansen", "email": "<EMAIL>", "status": "actief"},
                {"id": 2, "name": "Piet Pieters", "email": "<EMAIL>", "status": "inactief"}
            ]
        except Exception as e:
            self.log_action("ERROR", f"Fout bij laden CRM contacten: {str(e)}")
            return []

    def track_crm_event(self, event_type, details):
        """Registreer CRM-gebeurtenis"""
        timestamp = datetime.now().isoformat()
        event = {
            "type": event_type,
            "timestamp": timestamp,
            "details": details
        }
        self.crm_events.append(event)
        self.log_action("CRM_EVENT", f"{event_type}: {details}")

    def setup_global_config(self):
        """Setup global configuration directory voor alle projecten"""
        try:
            # Maak global directories aan
            GLOBAL_CONFIG_DIR.mkdir(exist_ok=True)
            GLOBAL_LOG_DIR.mkdir(exist_ok=True)
            
            # Load of create global config
            if GLOBAL_CONFIG_FILE.exists():
                with open(GLOBAL_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    self.global_config = json.load(f)
            else:
                self.global_config = {
                    'version': '2.0.0',
                    'created': datetime.now().isoformat(),
                    'projects': [],
                    'ai_preferences': {
                        'prefer_lm_studio': True,
                        'fallback_to_openai': True,
                        'model': LM_STUDIO_MODEL
                    }
                }
                self.save_global_config()
            
            # Add current project to global config
            project_path = str(self.project_root)
            if project_path not in self.global_config.get('projects', []):
                self.global_config['projects'].append(project_path)
                self.save_global_config()
                
        except Exception as e:
            print(f"⚠️  Could not setup global config: {e}")
            self.global_config = {}
    
    def save_global_config(self):
        """Save global configuration"""
        try:
            with open(GLOBAL_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.global_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️  Could not save global config: {e}")
        
    def setup_logging(self):
        """Setup logging voor workflow tracking"""
        # Use global log directory if available
        if GLOBAL_LOG_DIR.exists():
            timestamp = datetime.now().strftime('%Y%m%d')
            project_name = Path(self.project_root).name
            self.log_file = GLOBAL_LOG_DIR / f"{project_name}_{timestamp}.log"
        else:
            self.log_file = f"workflow_log_{datetime.now().strftime('%Y%m%d')}.txt"
        
    def log_action(self, action: str, details: str = ""):
        """Log workflow acties"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {action}: {details}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
        print(f"✅ {action}")
    
    def query_lm_studio(self, prompt: str, system_prompt: str = "") -> str:
        """Query LM Studio voor AI responses"""
        if not self.lm_studio_available:
            raise Exception("LM Studio niet beschikbaar")
        
        # Bereid messages voor
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        try:
            response = requests.post(
                f"{LM_STUDIO_BASE_URL}/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer lm-studio"
                },
                json={
                    "model": LM_STUDIO_MODEL,
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data['choices'][0]['message']['content']
            else:
                raise Exception(f"LM Studio HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_action("ERROR", f"LM Studio query failed: {str(e)}")
            raise
        
    def analyze_code_changes(self) -> Dict:
        """Analyseer recente code wijzigingen voor automatische acties"""
        try:
            # Git status ophalen
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  capture_output=True, text=True, cwd=self.project_root)
            
            changes = {
                'modified_files': [],
                'new_files': [],
                'deleted_files': [],
                'has_changes': False
            }
            
            if result.stdout:
                changes['has_changes'] = True
                for line in result.stdout.strip().split('\n'):
                    if line.startswith(' M '):
                        changes['modified_files'].append(line[3:])
                    elif line.startswith('A '):
                        changes['new_files'].append(line[3:])
                    elif line.startswith(' D '):
                        changes['deleted_files'].append(line[3:])
                        
            return changes
            
        except Exception as e:
            self.log_action("ERROR", f"Fout bij analyseren code wijzigingen: {str(e)}")
            return {'has_changes': False}
    
    def generate_smart_commit_message(self, changes: Dict) -> str:
        """Genereer intelligente commit message op basis van wijzigingen"""
        if not changes['has_changes']:
            return ""

        # Als AI niet beschikbaar is, gebruik fallback logic
        if not self.ai_available:
            return self.generate_fallback_commit_message(changes)

        files_context = []

        # Analyseer gewijzigde bestanden
        for file in changes['modified_files'][:5]:  # Limiteer tot 5 bestanden
            try:
                with open(os.path.join(self.project_root, file), 'r', encoding='utf-8') as f:
                    content = f.read()[:500]  # Eerste 500 karakters
                    files_context.append(f"Bestand: {file}\nInhoud preview: {content}")
            except:
                files_context.append(f"Bestand: {file}")

        prompt = f"""Genereer een professionele commit message in het Nederlands voor deze wijzigingen:

Gewijzigde bestanden: {', '.join(changes['modified_files'])}
Nieuwe bestanden: {', '.join(changes['new_files'])}
Verwijderde bestanden: {', '.join(changes['deleted_files'])}

Context van bestanden:
{chr(10).join(files_context)}

Maak een korte, duidelijke commit message die beschrijft wat er is veranderd.
Format: "type: beschrijving" (bijv. "feat: voeg klant dashboard toe" of "fix: los email validatie bug op")
Geef alleen de commit message terug, geen extra tekst."""

        system_prompt = "Je bent een ervaren Nederlandse developer die professionele commit messages schrijft. Houd je antwoord kort en duidelijk."

        try:
            # Probeer eerst LM Studio
            if self.lm_studio_available:
                try:
                    commit_message = self.query_lm_studio(prompt, system_prompt).strip()
                    self.log_action("COMMIT_MESSAGE_GENERATED", f"LM Studio: {commit_message}")
                    return commit_message
                except Exception as e:
                    self.log_action("WARNING", f"LM Studio failed, trying OpenAI: {str(e)}")

            # Fallback naar OpenAI
            if self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=100,
                    temperature=0.3
                )

                if response.choices[0].message.content is not None:
                    commit_message = response.choices[0].message.content.strip()
                else:
                    commit_message = self.generate_fallback_commit_message(changes)
                self.log_action("COMMIT_MESSAGE_GENERATED", f"OpenAI: {commit_message}")
                return commit_message

            # Als alles faalt, gebruik fallback
            return self.generate_fallback_commit_message(changes)

        except Exception as e:
            self.log_action("ERROR", f"Fout bij genereren commit message: {str(e)}")
            return self.generate_fallback_commit_message(changes)

    def generate_fallback_commit_message(self, changes: Dict) -> str:
        """Genereer commit message zonder AI"""
        # Intelligente fallback logic
        if any('api' in f.lower() for f in changes['modified_files']):
            if any('dashboard' in f.lower() for f in changes['modified_files']):
                return "feat: verbeter dashboard functionaliteit en API endpoints"
            else:
                return "fix: los API bugs op en verbeter error handling"
        elif any('mobile' in f.lower() for f in changes['modified_files']):
            return "feat: voeg nieuwe mobile componenten toe"
        elif any('workflow' in f.lower() or 'ai_' in f.lower() for f in changes['new_files']):
            return "feat: voeg AI workflow automatisering toe"
        elif any('.md' in f for f in changes['modified_files']):
            return "docs: update documentatie"
        elif any('test' in f.lower() for f in changes['modified_files']):
            return "test: voeg tests toe en verbeter test coverage"
        else:
            return "chore: algemene code verbeteringen en updates"
    
    def create_github_issue_for_bugs(self, error_logs: List[str]) -> bool:
        """Maak automatisch GitHub issues aan voor gevonden bugs"""
        if not error_logs:
            return False

        if not self.ai_available or not self.openai_client or not self.composio_toolset:
            self.log_action("INFO", "GitHub issue creation overgeslagen - AI of Composio niet beschikbaar")
            print("⚠️  GitHub issue creation vereist OpenAI API key en Composio")
            return False

        try:
            # Gebruik Composio om GitHub issue aan te maken
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_ISSUE])

            for error in error_logs[:3]:  # Max 3 issues per run
                issue_title = f"🐛 Bug gevonden: {error[:50]}..."
                issue_body = f"""
## Bug Beschrijving
Er is automatisch een bug gedetecteerd in het Yannova CRM systeem.

**Error Details:**
```
{error}
```

**Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Automatisch aangemaakt door:** AI Workflow Manager

## Volgende Stappen
- [ ] Reproduceer de bug
- [ ] Identificeer root cause
- [ ] Implementeer fix
- [ ] Test de oplossing
- [ ] Deploy naar productie

---
*Dit issue is automatisch aangemaakt door de AI Workflow Manager*
                """

                # GitHub issue aanmaken via Composio
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{
                        "role": "user",
                        "content": f"Maak een GitHub issue aan met titel: {issue_title} en body: {issue_body}"
                    }],
                    tools=tools
                )

                self.log_action("GITHUB_ISSUE_CREATED", f"Issue aangemaakt voor: {error[:50]}")

            return True

        except Exception as e:
            self.log_action("ERROR", f"Fout bij aanmaken GitHub issue: {str(e)}")
            return False
    
    def scan_for_errors(self) -> List[str]:
        """Scan project bestanden voor potentiële errors en bugs"""
        error_patterns = [
            'console.error',
            'throw new Error',
            'catch (error)',
            'TODO: FIX',
            'FIXME',
            'BUG:',
            'HACK:'
        ]
        
        found_errors = []
        
        # Scan belangrijke directories
        scan_dirs = ['apps/api', 'apps/mobile', 'apps/workers', 'packages']
        
        for scan_dir in scan_dirs:
            dir_path = os.path.join(self.project_root, scan_dir)
            if not os.path.exists(dir_path):
                continue
                
            for root, dirs, files in os.walk(dir_path):
                # Skip node_modules
                if 'node_modules' in root:
                    continue
                    
                for file in files:
                    if file.endswith(('.ts', '.tsx', '.js', '.jsx', '.py')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                for i, line in enumerate(content.split('\n'), 1):
                                    for pattern in error_patterns:
                                        if pattern.lower() in line.lower():
                                            error_info = f"{file_path}:{i} - {line.strip()}"
                                            found_errors.append(error_info)
                        except:
                            continue
        
        return found_errors[:10]  # Limiteer tot 10 errors
    
    def run_automated_workflow(self):
        """Voer de volledige geautomatiseerde workflow uit"""
        print("🚀 Yannova CRM AI Workflow Manager gestart...")
        print(f"📍 Project directory: {self.project_root}")
        self.log_action("WORKFLOW_STARTED", "Automatische workflow gestart")

        # Show dependency status
        print("\n📦 Dependency Status:")
        for dep, available in DEPENDENCIES_STATUS.items():
            status = "✅" if available else "❌"
            print(f"   {status} {dep}")

        # Check of AI beschikbaar is
        if not hasattr(self, 'ai_available') or not self.ai_available:
            print("\n⚠️  AI functionaliteit niet beschikbaar - werkend in basis modus")
            print("💡 Voor volledige AI features:")
            print("   1. Run: python setup_ai_dependencies.py")
            print("   2. Vul je OpenAI API key in het .env bestand in")
            print("   3. Restart de workflow manager")
        else:
            print("\n✅ AI functionaliteit actief!")
            
        print("\n" + "="*50)

        # 1. Analyseer code wijzigingen
        changes = self.analyze_code_changes()

        # 2. Genereer smart commit message als er wijzigingen zijn
        if changes['has_changes']:
            commit_message = self.generate_smart_commit_message(changes)
            print(f"💡 Voorgestelde commit message: {commit_message}")

            # Vraag gebruiker of commit moet worden uitgevoerd
            user_input = input("Wil je deze commit message gebruiken? (y/n): ")
            if user_input.lower() == 'y':
                try:
                    subprocess.run(['git', 'add', '.'], cwd=self.project_root)
                    subprocess.run(['git', 'commit', '-m', commit_message], cwd=self.project_root)
                    self.log_action("GIT_COMMIT", f"Commit uitgevoerd: {commit_message}")
                    print("✅ Commit succesvol uitgevoerd!")
                except Exception as e:
                    self.log_action("ERROR", f"Fout bij git commit: {str(e)}")
                    print(f"❌ Fout bij commit: {str(e)}")
        else:
            print("ℹ️  Geen code wijzigingen gevonden")

        # 3. Scan voor errors en maak issues aan
        errors = self.scan_for_errors()
        if errors:
            print(f"⚠️  {len(errors)} potentiële problemen gevonden")
            for error in errors[:5]:
                print(f"   - {error}")

            if hasattr(self, 'ai_available') and self.ai_available:
                user_input = input("Wil je automatisch GitHub issues aanmaken voor deze problemen? (y/n): ")
                if user_input.lower() == 'y':
                    self.create_github_issue_for_bugs(errors)
            else:
                print("💡 GitHub issue creation vereist OpenAI API key")
        else:
            print("✅ Geen problemen gevonden in de code scan")

        # 4. Project status rapport
        self.generate_status_report()

        print("\n" + "="*50)
        print("✅ Workflow voltooid!")
        print("="*50)
        print(f"📄 Logbestand: {self.log_file}")
        print("📊 Status rapport: project_status_report.json")
        
        # Show AI status and recommendations
        if self.lm_studio_available and self.openai_client:
            print("🤖 AI Status: LM Studio + OpenAI beschikbaar")
        elif self.lm_studio_available:
            print("🏠 AI Status: LM Studio actief (lokale AI)")
        elif self.openai_client:
            print("☁️ AI Status: OpenAI API actief")
        else:
            print("⚠️ AI Status: Alleen fallback beschikbaar")
            print("💡 Voor AI features:")
            print("   - Start LM Studio met een model")
            print("   - Of stel OPENAI_API_KEY in")
        
        # Global installation tip
        if not GLOBAL_CONFIG_DIR.exists():
            print("\n🌟 Tip: Installeer globally voor alle projecten!")
            print("   python install_global_ai_workflow.py")
            print("   Dan kun je 'yannova-ai' gebruiken in elk project!")
    
    def generate_status_report(self):
        """Genereer een status rapport van het project"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'project': 'Yannova CRM',
            'git_status': self.analyze_code_changes(),
            'error_count': len(self.scan_for_errors()),
            'workflow_log': str(self.log_file)  # Convert Path to string
        }

        with open('project_status_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        self.log_action("STATUS_REPORT", "Project status rapport gegenereerd")

    def start_file_watching(self):
        """Start met monitoren van bestandswijzigingen"""
        if not DEPENDENCIES_STATUS.get('watchdog', False):
            print("⚠️  Watchdog niet geïnstalleerd. Run: pip install watchdog")
            return
            
        print("👀 Start live monitoring van bestandswijzigingen...")
        print("   Druk op Ctrl+C om te stoppen")
        
        event_handler = YannovaFileWatcher(self.handle_file_change)
        observer = Observer()
        observer.schedule(event_handler, self.project_root, recursive=True)
        observer.start()
        self.file_watcher = observer
        self.watching = True
        
        try:
            while self.watching:
                time.sleep(1)
        except KeyboardInterrupt:
            observer.stop()
        observer.join()
        
    def handle_file_change(self, file_path):
        """Verwerk bestandswijziging met verbeterde preview"""
        rel_path = os.path.relpath(file_path, self.project_root)
        
        # Vermijd dubbele meldingen
        current_mtime = os.path.getmtime(file_path)
        if rel_path in self.last_modified and self.last_modified[rel_path] == current_mtime:
            return
            
        self.last_modified[rel_path] = current_mtime
        
        print(f"\n📁 Bestand gewijzigd: {rel_path}")
        print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")
        
        # Toon preview op geselecteerde manier
        if self.diff_viewer == "terminal":
            self.show_terminal_diff(rel_path)
        elif self.diff_viewer == "browser":
            self.show_browser_diff(rel_path)
        elif self.diff_viewer == "editor":
            self.show_editor_diff(rel_path)
            
        # Analyseer de wijziging
        self.analyze_single_change(rel_path)

    def show_terminal_diff(self, rel_path):
        """Toon uitgebreide diff in terminal met syntax highlighting"""
        try:
            full_path = os.path.join(self.project_root, rel_path)
            with open(full_path, 'r', encoding='utf-8') as f:
                current_content = f.read().splitlines()
                
            old_content = self.file_history.get(rel_path, [])
            
            if old_content:
                diff = difflib.unified_diff(
                    old_content, 
                    current_content,
                    fromfile=f"Oud: {rel_path}",
                    tofile=f"Nieuw: {rel_path}",
                    n=10
                )
                
                diff_lines = list(diff)
                if HAS_COLORAMA:
                    for line in diff_lines:
                        if line.startswith('+'):
                            print(colorama.Fore.GREEN + line + colorama.Style.RESET_ALL)
                        elif line.startswith('-'):
                            print(colorama.Fore.RED + line + colorama.Style.RESET_ALL)
                        elif line.startswith('@'):
                            print(colorama.Fore.BLUE + line + colorama.Style.RESET_ALL)
                        else:
                            print(line)
                else:
                    print('\n'.join(diff_lines))
            else:
                print(f"ℹ️  Eerste wijziging gedetecteerd in {rel_path}")
                print("🔍 Volledige inhoud:\n" + "\n".join(current_content))
                
            # Update geschiedenis
            self.file_history[rel_path] = current_content
            
        except Exception as e:
            print(f"⚠️ Kon diff niet tonen: {str(e)}")

    def show_browser_diff(self, rel_path):
        """Toon uitgebreide diff in browser met syntax highlighting"""
        try:
            full_path = os.path.join(self.project_root, rel_path)
            with open(full_path, 'r', encoding='utf-8') as f:
                current_content = f.read()
                
            # Haal vorige versie op
            old_content = self.file_history.get(rel_path, "")
            
            if old_content:
                # Maak HTML diff met syntax highlighting
                differ = difflib.HtmlDiff(tabsize=4, wrapcolumn=80)
                diff_html = differ.make_file(
                    old_content.splitlines(), 
                    current_content.splitlines(),
                    fromdesc=f"Oud: {rel_path}",
                    todesc=f"Nieuw: {rel_path}",
                    context=True,
                    numlines=10
                )
                
                # Verbeterde opmaak
                diff_html = diff_html.replace(
                    '</style>',
                    '''
                    </style>
                    <style>
                        table.diff { width: 100%; font-family: monospace; }
                        .diff_header { background-color: #e0e0e0; }
                        td.diff_header { width: 50px; }
                        .diff_add { background-color: #ddffdd; }
                        .diff_chg { background-color: #ffffcc; }
                        .diff_sub { background-color: #ffdddd; }
                    </style>
                    '''
                )
                
                # Sla HTML op en open in browser
                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.html') as tmp:
                    tmp.write(diff_html)
                    tmp_path = tmp.name
                
                # Open in browser
                if sys.platform == "win32":
                    os.startfile(tmp_path)
                else:
                    subprocess.run(["open", tmp_path] if sys.platform == "darwin" else ["xdg-open", tmp_path])
                    
                print(f"🌐 Diff geopend in browser: {tmp_path}")
            else:
                print(f"ℹ️  Eerste wijziging gedetecteerd in {rel_path}")
                print("🔍 Volledige inhoud:\n" + current_content)
                
            # Update geschiedenis
            self.file_history[rel_path] = current_content
            
        except Exception as e:
            print(f"⚠️ Kon browser diff niet tonen: {str(e)}")
            self.show_terminal_diff(rel_path)  # Fallback naar terminal

    def show_editor_diff(self, rel_path):
        """Toon diff in standaard editor"""
        try:
            # Maak temp bestand met diff
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.diff') as tmp:
                with open(os.path.join(self.project_root, rel_path), 'r', encoding='utf-8') as f:
                    current_content = f.read().splitlines()
                    
                old_content = self.file_history.get(rel_path, [])
                
                if old_content:
                    diff = difflib.unified_diff(
                        old_content, 
                        current_content,
                        fromfile=f"Oud: {rel_path}",
                        tofile=f"Nieuw: {rel_path}",
                        n=10
                    )
                    tmp.write("\n".join(diff))
                else:
                    tmp.write(f"Eerste wijziging: {rel_path}\n")
                    tmp.write("\n".join(current_content[:20]))
                
                tmp_path = tmp.name
                
            # Open in standaard editor
            if sys.platform == "win32":
                os.startfile(tmp_path)
            else:
                subprocess.run(["open", tmp_path] if sys.platform == "darwin" else ["xdg-open", tmp_path])
                
            print(f"📝 Diff geopend in editor: {tmp_path}")
            
            # Update geschiedenis
            self.file_history[rel_path] = current_content
            
        except Exception as e:
            print(f"⚠️ Kon editor diff niet tonen: {str(e)}")
            self.show_terminal_diff(rel_path)  # Fallback naar terminal

    def analyze_single_change(self, file_path):
        """Analyseer een enkele bestandswijziging"""
        # Controleer op bekende problemen
        error_patterns = ['console.error', 'throw new Error', 'TODO: FIX', 'FIXME']
        try:
            with open(os.path.join(self.project_root, file_path), 'r', encoding='utf-8') as f:
                for i, line in enumerate(f, 1):
                    for pattern in error_patterns:
                        if pattern in line:
                            print(f"⚠️  Potentieel probleem gevonden in {file_path}:{i}")
                            print(f"   {line.strip()}")
                            return
        except:
            pass
            
        print("✅ Geen bekende problemen gevonden in deze wijziging")

    def update_crm_contacts(self):
        """Synchroniseer contacten met CRM-systeem"""
        print("🔄 CRM-contacten bijwerken...")
        try:
            # Hier zou je de echte CRM-integratie implementeren
            # Bijvoorbeeld: API-call naar je CRM-systeem
            self.track_crm_event("CONTACT_UPDATE", "Contacten gesynchroniseerd")
            print("✅ CRM-contacten succesvol bijgewerkt!")
        except Exception as e:
            self.track_crm_event("ERROR", f"CRM-synchronisatiefout: {str(e)}")
            print(f"❌ Fout bij CRM-synchronisatie: {str(e)}")

if __name__ == "__main__":
    # Controleer of we in de juiste directory zijn
    if not os.path.exists('package.json'):
        print("❌ Fout: Voer dit script uit vanuit de root directory van je Yannova CRM project")
        exit(1)

    # Toon welkomstbericht
    print("\n" + "="*50)
    print("🌟 Yannova CRM AI Workflow Manager v2.0")
    print("="*50)
    print("Gebruik:")
    print("  Standaard workflow: python ai_workflow_manager.py")
    print("  Live monitoring:    python ai_workflow_manager.py --watch")
    print("  Preview opties:     --preview-terminal, --preview-browser, --preview-editor")
    print("="*50 + "\n")

    # Start de workflow manager
    try:
        workflow_manager = YannovaCRMWorkflowManager()
        
        # Preview optie verwerken
        if "--preview-terminal" in sys.argv:
            workflow_manager.set_diff_viewer("terminal")
        elif "--preview-browser" in sys.argv:
            workflow_manager.set_diff_viewer("browser")
        elif "--preview-editor" in sys.argv:
            workflow_manager.set_diff_viewer("editor")
            
        if "--watch" in sys.argv or "-w" in sys.argv:
            if DEPENDENCIES_STATUS.get('watchdog', False):
                workflow_manager.start_file_watching()
            else:
                print("❌ Live monitoring vereist watchdog. Installeer met: pip install watchdog")
                exit(1)
        else:
            # Check of initialisatie succesvol was
            if hasattr(workflow_manager, 'ai_available'):
                workflow_manager.run_automated_workflow()
            else:
                print("❌ Workflow manager kon niet worden geïnitialiseerd of AI is niet beschikbaar")
                print("🔧 Controleer je setup en probeer opnieuw")
                exit(1)

    except Exception as e:
        print(f"❌ Onverwachte fout: {str(e)}")
        print("🔧 Controleer je setup en probeer opnieuw")
        exit(1)
