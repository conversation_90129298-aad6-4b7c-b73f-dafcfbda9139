# Yannova AI Workflow Manager Setup

De AI Workflow Manager automatiseert veel taken in al je projecten en bespaart je tijd met intelligente functies. Nu met **LM Studio support** voor lokale AI!

## 🚀 Snelle Start (Global Installation)

### 1. Global Installatie (Aanbevolen)

```bash
# Installeer globally voor alle projecten
python install_global_ai_workflow.py

# Herstart terminal of:
source ~/.bashrc

# Nu beschikbaar overal!
yannova-ai
```

### 2. Project-Specifieke Installatie

```bash
# Optie 1: Automatische setup 
python setup_ai_dependencies.py

# Optie 2: Handmatige installatie
pip install python-dotenv openai requests
```

## 🤖 AI Configuratie

### 🏠 LM Studio (Lokaal - Aanbevolen)

1. **Start LM Studio** op je computer
2. **Laad een model** (bijv. Mistral-7B-Instruct)  
3. **Start Local Server** op http://127.0.0.1:1234
4. **Ready!** - De workflow manager detecteert LM Studio automatisch

**Voordelen LM Studio:**
- ✅ Privacy - data blijft lokaal
- ✅ Geen API kosten
- ✅ Snelle response times
- ✅ Werkt offline

### ☁️ OpenAI API (Fallback)

**Optioneel** - alleen nodig als je geen LM Studio hebt:

1. Ga naar [OpenAI Platform](https://platform.openai.com/api-keys)
2. Maak een nieuwe API key aan
3. Kopieer de key en voeg toe aan `.env`:

```env
OPENAI_API_KEY=sk-jouw-api-key-hier
```

### 3. Start de Workflow Manager

```bash
# Global (overal beschikbaar)
yannova-ai

# Of project-specifiek
python ai_workflow_manager.py
```

## 🔄 AI Provider Prioriteit

De workflow manager gebruikt deze volgorde:

1. **🏠 LM Studio** (lokaal) - als beschikbaar
2. **☁️ OpenAI API** - als LM Studio faalt
3. **🛠️ Fallback Logic** - als beide falen

## ✨ Functies

### 🤖 AI-Gegenereerde Commit Messages
- Analyseert je code wijzigingen
- Genereert professionele commit messages in het Nederlands
- Gebruikt intelligente fallback als AI niet beschikbaar is

### 🐛 Automatische Bug Detection
- Scant je code voor potentiële problemen
- Vindt TODO's, FIXME's en error patterns
- Kan automatisch GitHub issues aanmaken

### 📊 Project Status Rapportage
- Genereert overzicht van project status
- Houdt workflow logs bij
- Exporteert naar JSON voor analyse

### 🔄 Git Integratie
- Automatische git status analyse
- Smart commit functionaliteit
- Detailgedag change tracking

## 🛠️ Configuratie

### Environment Variables (.env)

```env
# Verplicht voor AI functies
OPENAI_API_KEY=sk-jouw-api-key-hier
OPENAI_MODEL=gpt-4

# Optioneel voor GitHub integratie
GITHUB_TOKEN=ghp_jouw-github-token
GITHUB_REPO=username/yannova-crm

# Project instellingen
PROJECT_NAME=Yannova CRM
ENVIRONMENT=development
```

### Optionele Dependencies

Voor extra functionaliteit, installeer:

```bash
# GitHub integratie (voor automatische issue creation)
pip install composio-openai

# Verbeterde Git functionaliteit
pip install GitPython
```

## 📋 Workflow Stappen

Wanneer je `python ai_workflow_manager.py` uitvoert:

1. **Code Analyse**: Detecteert wijzigingen in je project
2. **Smart Commit**: Genereert intelligente commit message
3. **Bug Scan**: Zoekt naar potentiële problemen
4. **Issue Creation**: Maakt optioneel GitHub issues aan
5. **Status Report**: Genereert project overzicht

## 🔧 Troubleshooting

### Import Errors
Als je import errors krijgt:
```bash
python setup_ai_dependencies.py
```

### API Key Problemen
- Controleer of je API key correct is in `.env`
- Zorg dat je API key geldig is en credit heeft
- Test met: `python -c "import openai; print('OpenAI geïnstalleerd')"`

### Git Problemen
- Zorg dat je in een git repository bent
- Controleer of git geïnstalleerd is: `git --version`

## 💡 Tips

### Voor Beste Resultaten
1. **Regelmatig runnen**: Voor de beste commit messages
2. **Clean code**: Houd je code georganiseerd voor betere analyse
3. **API Credits**: Monitor je OpenAI API usage

### Aanpassingen
- Pas commit message templates aan in de code
- Voeg custom error patterns toe aan `scan_for_errors()`
- Configureer je eigen workflow stappen

## 🔐 Veiligheid

- Sla nooit je `.env` bestand op in git
- Gebruik minimale API permissions
- Monitor je API key usage regelmatig

## 📚 Hulp & Support

- Check de logs in `workflow_log_YYYYMMDD.txt`
- Bekijk `project_status_report.json` voor details
- Voor problemen: controleer eerst je API key en dependencies

## 🎯 Geavanceerde Features

### Custom Workflows
Je kunt de workflow manager uitbreiden door:
- Nieuwe methoden toe te voegen aan de `YannovaCRMWorkflowManager` class
- Custom error patterns definiëren
- Eigen AI prompts schrijven

### Integraties
- GitHub Actions voor automatische workflows
- Slack/Teams notificaties
- Custom webhooks voor externe systemen 