# Yannova MCP Servers Environment Variables
# Kopieer dit bestand naar .env en vul de waarden in

# Database
DATABASE_URL="postgresql://yannova:securepassword@localhost:5432/yannova_crm"
POSTGRES_PASSWORD=securepassword

# AI Services
OPENAI_API_KEY=your_openai_api_key_here
CLAUDE_API_KEY=your_claude_api_key_here

# Bot Integration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
WHATSAPP_API_KEY=your_whatsapp_api_key_here

# Monitoring
GRAFANA_PASSWORD=admin

# Trading APIs (optioneel)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
MEXC_API_KEY=your_mexc_api_key_here
MEXC_SECRET_KEY=your_mexc_secret_key_here

# Email Service
RESEND_API_KEY=your_resend_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here

# GitHub Integration
GITHUB_TOKEN=your_github_token_here

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Application
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
LOG_LEVEL=info