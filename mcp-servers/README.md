# Yannova MCP Servers

Drie verschillende MCP (Model Context Protocol) servers voor verschillende use cases:

## 1. Standalone Server (`standalone-server.ts`)
**Basis MCP server met algemene tools**
- Rekenen (som, vermenigvuldiging)
- UUID generatie
- Datum formatting (Nederlandse stijl)

## 2. CRM Server (`crm-server.ts`)
**CRM-specifieke tools**
- Contact management (CRUD)
- Conversatie tracking
- CRM statistieken
- Tag-based filtering

## 3. AI Enhanced Server (`ai-enhanced-server.ts`)
**Geavanceerde AI-powered tools**
- AI workflow orchestratie
- Sentiment analyse
- Email response generatie
- Trading signaal management
- Bot configuratie (Telegram/WhatsApp)
- Multi-agent AI orchestratie

## Installatie

```bash
npm install
npm run build
```

## Gebruik

### Development mode
```bash
npm run dev:basic    # Basis server
npm run dev:crm      # CRM server
npm run dev:ai       # AI enhanced server
```

### Productie mode
```bash
npm run build
npm run start:basic  # Basis server
npm run start:crm    # CRM server
npm run start:ai     # AI enhanced server
```

## MCP Client Configuratie

Voeg toe aan je MCP client config (bijvoorbeeld Claude Desktop):

```json
{
  "mcpServers": {
    "yannova-basic": {
      "command": "node",
      "args": ["path/to/dist/standalone-server.js"]
    },
    "yannova-crm": {
      "command": "node",
      "args": ["path/to/dist/crm-server.js"]
    },
    "yannova-ai": {
      "command": "node",
      "args": ["path/to/dist/ai-enhanced-server.js"]
    }
  }
}
```

## Voorbeelden

### Basis Server
```javascript
// Som berekenen
calculate_sum({"a": 5, "b": 3}) // Returns: "Som: 8"

// Datum formatteren
format_date({"date": "2024-01-15", "format": "long"}) // Returns: "15 januari 2024"
```

### CRM Server
```javascript
// Contact aanmaken
create_contact({
  "name": "Jan Jansen",
  "email": "<EMAIL>",
  "phone": "06-12345678",
  "company": "Jansen BV",
  "tags": ["prospect", "dutch"]
})

// CRM stats
get_crm_stats() // Returns complete statistics
```

### AI Enhanced Server
```javascript
// Sentiment analyse
analyze_sentiment({"text": "Ik ben zeer tevreden met de service!"})

// Trading signaal
create_trading_signal({
  "symbol": "BTC/USDT",
  "action": "buy",
  "price": 45000,
  "reasoning": "Bullish breakout pattern",
  "confidence": 85
})

// AI workflow
create_ai_workflow({
  "name": "Email Response Automation",
  "description": "Automatisch reageren op support emails",
  "steps": [
    {"type": "ai_analysis", "config": {"model": "claude"}},
    {"type": "email_send", "config": {"template": "support_response"}}
  ]
})
```

## Architectuur

Elke server is gebouwd met:
- **@modelcontextprotocol/sdk**: Officiële MCP SDK
- **TypeScript**: Type safety
- **ESM modules**: Moderne JavaScript
- **In-memory storage**: Voor demo (vervang door echte database)

## Integratie met Yannova CRM

De servers zijn ontworpen om te integreren met:
- Bestaande API endpoints
- Telegram/WhatsApp bots
- Trading systemen
- Email automation
- AI workflows

Voor productie-gebruik, vervang in-memory storage door echte database calls naar je CRM systeem.