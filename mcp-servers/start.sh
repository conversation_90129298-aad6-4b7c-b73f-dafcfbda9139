#!/bin/bash

# Yannova MCP Servers Startup Script
# Leon - Yannova CRM Integration

echo "🚀 Yannova MCP Servers"
echo "======================="

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build TypeScript files
echo "🔨 Building TypeScript files..."
npm run build

# Menu voor server keuze
echo ""
echo "Kies een server om te starten:"
echo "1) Basis Server (rekenen, UUID, datum)"
echo "2) CRM Server (contact management, stats)"
echo "3) AI Enhanced Server (workflows, sentiment, trading)"
echo "4) Alle servers parallel"
echo "5) Development mode (met auto-reload)"
echo ""

read -p "Voer je keuze in (1-5): " choice

case $choice in
    1)
        echo "🎯 Starting Basis Server..."
        node dist/standalone-server.js
        ;;
    2)
        echo "🎯 Starting CRM Server..."
        node dist/crm-server.js
        ;;
    3)
        echo "🎯 Starting AI Enhanced Server..."
        node dist/ai-enhanced-server.js
        ;;
    4)
        echo "🎯 Starting alle servers parallel..."
        echo "Basis Server op poort 3001, CRM Server op poort 3002, AI Server op poort 3003"
        node dist/standalone-server.js & 
        node dist/crm-server.js & 
        node dist/ai-enhanced-server.js &
        wait
        ;;
    5)
        echo "🎯 Development mode - kies server:"
        echo "1) Basis"
        echo "2) CRM" 
        echo "3) AI Enhanced"
        read -p "Keuze: " dev_choice
        case $dev_choice in
            1) npm run dev:basic ;;
            2) npm run dev:crm ;;
            3) npm run dev:ai ;;
            *) echo "Ongeldige keuze" ;;
        esac
        ;;
    *)
        echo "Ongeldige keuze. Gebruik 1-5."
        exit 1
        ;;
esac