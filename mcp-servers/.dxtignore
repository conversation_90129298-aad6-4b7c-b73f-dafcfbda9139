# Development files
*.ts
tsconfig.json
.git/
.gitignore

# Documentation (keep only essential)
DXT_README.md
*.md

# Development tools
tsx
nodemon.json

# Test files
test-client.js
*.test.js
*.spec.js

# Build artifacts (we only need dist/)
src/

# Development dependencies (keep only runtime deps)
node_modules/@types/
node_modules/typescript/
node_modules/tsx/

# Docker files (not needed for DXT)
docker-compose.yml
Dockerfile
deploy.sh

# Logs and temp files
*.log
.tmp/
.cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db 