#!/usr/bin/env node

// Yannova MCP Server Test Client
// <PERSON> test<PERSON> van MCP servers

import { spawn } from 'child_process';
import { createInterface } from 'readline';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🧪 Yannova MCP Server Test Client');
console.log('===================================');

// Test scenarios
const testScenarios = {
  basic: [
    { name: 'calculate_sum', arguments: { a: 5, b: 3 } },
    { name: 'generate_uuid', arguments: {} },
    { name: 'format_date', arguments: { date: '2024-01-15', format: 'long' } }
  ],
  crm: [
    { name: 'create_contact', arguments: { name: '<PERSON>', email: '<EMAIL>', phone: '06-12345678' } },
    { name: 'get_crm_stats', arguments: {} },
    { name: 'list_contacts', arguments: { limit: 5 } }
  ],
  ai: [
    { name: 'analyze_sentiment', arguments: { text: '<PERSON><PERSON> ben zeer tevreden met de service!' } },
    { name: 'create_trading_signal', arguments: { symbol: 'BTC/USDT', action: 'buy', price: 45000, reasoning: 'Bullish breakout' } },
    { name: 'generate_email_response', arguments: { original_email: 'Hallo, ik wil meer informatie.', response_tone: 'professional' } }
  ]
};

function testServer(serverScript, scenarios) {
  console.log(`\n🎯 Testing ${serverScript}...`);
  
  const server = spawn('node', [serverScript], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  server.on('error', (error) => {
    console.error(`❌ Server error: ${error.message}`);
  });

  server.stderr.on('data', (data) => {
    console.log(`📊 Server: ${data.toString().trim()}`);
  });

  // Test ListTools
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };

  server.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  server.stdout.on('data', (data) => {
    const response = data.toString().trim();
    console.log(`📨 Response: ${response}`);
  });

  // Test tools na korte delay
  setTimeout(() => {
    scenarios.forEach((scenario, index) => {
      const toolRequest = {
        jsonrpc: '2.0',
        id: index + 2,
        method: 'tools/call',
        params: {
          name: scenario.name,
          arguments: scenario.arguments
        }
      };

      server.stdin.write(JSON.stringify(toolRequest) + '\n');
      console.log(`🔧 Testing tool: ${scenario.name}`);
    });

    // Stop server na tests
    setTimeout(() => {
      server.kill();
      console.log(`✅ ${serverScript} test completed`);
    }, 2000);
  }, 1000);
}

function showMenu() {
  console.log('\nWelke server wil je testen?');
  console.log('1) Basis Server');
  console.log('2) CRM Server');
  console.log('3) AI Enhanced Server');
  console.log('4) Alle servers');
  console.log('5) Custom tool test');
  console.log('6) Exit');
}

function handleChoice(choice) {
  switch(choice) {
    case '1':
      testServer('dist/standalone-server.js', testScenarios.basic);
      break;
    case '2':
      testServer('dist/crm-server.js', testScenarios.crm);
      break;
    case '3':
      testServer('dist/ai-enhanced-server.js', testScenarios.ai);
      break;
    case '4':
      console.log('🔄 Testing alle servers...');
      testServer('dist/standalone-server.js', testScenarios.basic);
      setTimeout(() => testServer('dist/crm-server.js', testScenarios.crm), 3000);
      setTimeout(() => testServer('dist/ai-enhanced-server.js', testScenarios.ai), 6000);
      break;
    case '5':
      customToolTest();
      break;
    case '6':
      console.log('👋 Tot ziens!');
      process.exit(0);
      break;
    default:
      console.log('❌ Ongeldige keuze');
      showMenu();
  }
}

function customToolTest() {
  console.log('\n🛠️ Custom Tool Test');
  rl.question('Server script (bijv. dist/crm-server.js): ', (serverScript) => {
    rl.question('Tool name: ', (toolName) => {
      rl.question('Arguments (JSON): ', (argsJson) => {
        try {
          const args = JSON.parse(argsJson);
          testServer(serverScript, [{ name: toolName, arguments: args }]);
        } catch (error) {
          console.error('❌ Invalid JSON:', error.message);
        }
        showMenu();
      });
    });
  });
}

// Start menu
showMenu();
rl.question('Keuze: ', handleChoice);

rl.on('close', () => {
  console.log('\n👋 Test client afgesloten');
  process.exit(0);
});