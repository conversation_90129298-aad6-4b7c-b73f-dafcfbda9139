{"dxt_version": "0.1", "name": "yannova-crm-mcp", "version": "1.0.0", "description": "Yannova CRM MCP extension met AI-powered workflow management, Telegram/WhatsApp integratie, en trading automatisering", "author": {"name": "<PERSON> <PERSON> <PERSON><PERSON>"}, "server": {"type": "node", "entry_point": "dist/crm-server.js", "mcp_config": {"command": "node", "args": ["${__dirname}/dist/crm-server.js"]}}, "capabilities": ["crm_management", "ai_workflows", "telegram_integration", "whatsapp_integration", "email_automation", "trading_signals", "contact_management"], "requirements": {"node_version": ">=18.0.0", "dependencies": ["@modelcontextprotocol/sdk"]}, "configuration": {"environment_variables": ["OPENAI_API_KEY", "TELEGRAM_BOT_TOKEN", "WHATSAPP_TOKEN", "RESEND_API_KEY"]}}