import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ErrorCode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";

interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  status: "active" | "inactive" | "prospect";
  created_at: string;
  tags?: string[];
}

interface CRMStats {
  total_contacts: number;
  active_contacts: number;
  prospects: number;
  conversations_today: number;
  emails_sent: number;
}

// In-memory storage (in productie zou dit een database zijn)
const contacts: Map<string, Contact> = new Map();
const conversations: Map<string, any[]> = new Map();

const server = new Server({
  name: "yannova-crm-server",
  version: "1.0.0"
}, {
  capabilities: {
    tools: {}
  }
});

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "create_contact",
        description: "Maak een nieuwe contact aan in CRM",
        inputSchema: {
          type: "object",
          properties: {
            name: { type: "string", description: "Naam van het contact" },
            email: { type: "string", description: "Email adres" },
            phone: { type: "string", description: "Telefoonnummer" },
            company: { type: "string", description: "Bedrijfsnaam" },
            tags: { type: "array", items: { type: "string" }, description: "Tags voor categorisering" }
          },
          required: ["name", "email"]
        }
      },
      {
        name: "get_contact",
        description: "Haal contact op via ID of email",
        inputSchema: {
          type: "object",
          properties: {
            id: { type: "string", description: "Contact ID" },
            email: { type: "string", description: "Email adres" }
          }
        }
      },
      {
        name: "list_contacts",
        description: "Lijst alle contacten met optionele filters",
        inputSchema: {
          type: "object",
          properties: {
            status: { type: "string", enum: ["active", "inactive", "prospect"], description: "Filter op status" },
            tag: { type: "string", description: "Filter op tag" },
            limit: { type: "number", description: "Maximaal aantal resultaten" }
          }
        }
      },
      {
        name: "update_contact",
        description: "Update een bestaand contact",
        inputSchema: {
          type: "object",
          properties: {
            id: { type: "string", description: "Contact ID" },
            name: { type: "string", description: "Nieuwe naam" },
            email: { type: "string", description: "Nieuw email" },
            phone: { type: "string", description: "Nieuw telefoonnummer" },
            company: { type: "string", description: "Nieuwe bedrijfsnaam" },
            status: { type: "string", enum: ["active", "inactive", "prospect"], description: "Nieuwe status" },
            tags: { type: "array", items: { type: "string" }, description: "Nieuwe tags" }
          },
          required: ["id"]
        }
      },
      {
        name: "delete_contact",
        description: "Verwijder een contact",
        inputSchema: {
          type: "object",
          properties: {
            id: { type: "string", description: "Contact ID" }
          },
          required: ["id"]
        }
      },
      {
        name: "get_crm_stats",
        description: "Haal CRM statistieken op",
        inputSchema: {
          type: "object",
          properties: {},
          required: []
        }
      },
      {
        name: "add_conversation_note",
        description: "Voeg een notitie toe aan een contact conversatie",
        inputSchema: {
          type: "object",
          properties: {
            contact_id: { type: "string", description: "Contact ID" },
            note: { type: "string", description: "Conversatie notitie" },
            type: { type: "string", enum: ["email", "phone", "meeting", "note"], description: "Type conversatie" }
          },
          required: ["contact_id", "note", "type"]
        }
      },
      {
        name: "get_conversation_history",
        description: "Haal conversatie geschiedenis op voor een contact",
        inputSchema: {
          type: "object",
          properties: {
            contact_id: { type: "string", description: "Contact ID" },
            limit: { type: "number", description: "Maximaal aantal items" }
          },
          required: ["contact_id"]
        }
      }
    ]
  };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case "create_contact":
        const { name: contactName, email, phone, company, tags } = args as any;
        const contactId = crypto.randomUUID();
        const newContact: Contact = {
          id: contactId,
          name: contactName,
          email,
          phone,
          company,
          status: "prospect",
          created_at: new Date().toISOString(),
          tags: tags || []
        };
        contacts.set(contactId, newContact);
        return {
          content: [
            {
              type: "text",
              text: `Contact aangemaakt: ${contactName} (ID: ${contactId})`
            }
          ]
        };

      case "get_contact":
        const { id, email: searchEmail } = args as any;
        let contact: Contact | undefined;
        
        if (id) {
          contact = contacts.get(id);
        } else if (searchEmail) {
          contact = Array.from(contacts.values()).find(c => c.email === searchEmail);
        }
        
        if (!contact) {
          return {
            content: [
              {
                type: "text",
                text: "Contact niet gevonden"
              }
            ]
          };
        }
        
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(contact, null, 2)
            }
          ]
        };

      case "list_contacts":
        const { status, tag, limit } = args as any;
        let filteredContacts = Array.from(contacts.values());
        
        if (status) {
          filteredContacts = filteredContacts.filter(c => c.status === status);
        }
        if (tag) {
          filteredContacts = filteredContacts.filter(c => c.tags?.includes(tag));
        }
        if (limit) {
          filteredContacts = filteredContacts.slice(0, limit);
        }
        
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(filteredContacts, null, 2)
            }
          ]
        };

      case "update_contact":
        const updateData = args as any;
        const existingContact = contacts.get(updateData.id);
        
        if (!existingContact) {
          return {
            content: [
              {
                type: "text",
                text: "Contact niet gevonden"
              }
            ]
          };
        }
        
        const updatedContact = { ...existingContact, ...updateData };
        contacts.set(updateData.id, updatedContact);
        
        return {
          content: [
            {
              type: "text",
              text: `Contact bijgewerkt: ${updatedContact.name}`
            }
          ]
        };

      case "delete_contact":
        const { id: deleteId } = args as any;
        const deleted = contacts.delete(deleteId);
        
        return {
          content: [
            {
              type: "text",
              text: deleted ? "Contact verwijderd" : "Contact niet gevonden"
            }
          ]
        };

      case "get_crm_stats":
        const allContacts = Array.from(contacts.values());
        const stats: CRMStats = {
          total_contacts: allContacts.length,
          active_contacts: allContacts.filter(c => c.status === "active").length,
          prospects: allContacts.filter(c => c.status === "prospect").length,
          conversations_today: Array.from(conversations.values()).reduce((sum, conv) => sum + conv.length, 0),
          emails_sent: 0 // Zou uit email service komen
        };
        
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(stats, null, 2)
            }
          ]
        };

      case "add_conversation_note":
        const { contact_id, note, type } = args as any;
        if (!contacts.has(contact_id)) {
          return {
            content: [
              {
                type: "text",
                text: "Contact niet gevonden"
              }
            ]
          };
        }
        
        const conversationEntry = {
          id: crypto.randomUUID(),
          contact_id,
          note,
          type,
          timestamp: new Date().toISOString()
        };
        
        if (!conversations.has(contact_id)) {
          conversations.set(contact_id, []);
        }
        conversations.get(contact_id)!.push(conversationEntry);
        
        return {
          content: [
            {
              type: "text",
              text: `Conversatie notitie toegevoegd aan contact ${contact_id}`
            }
          ]
        };

      case "get_conversation_history":
        const { contact_id: historyContactId, limit: historyLimit } = args as any;
        const history = conversations.get(historyContactId) || [];
        const limitedHistory = historyLimit ? history.slice(-historyLimit) : history;
        
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(limitedHistory, null, 2)
            }
          ]
        };

      default:
        throw new McpError(ErrorCode.MethodNotFound, `Tool ${name} niet gevonden`);
    }
  } catch (error) {
    throw new McpError(ErrorCode.InternalError, `Fout bij uitvoeren van tool ${name}: ${error}`);
  }
});

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("Yannova CRM MCP Server gestart");
}

main().catch(console.error);