{"name": "yannova-mcp-servers", "version": "1.0.0", "description": "MCP Servers voor Yannova CRM systeem", "main": "index.js", "type": "module", "scripts": {"start:basic": "node dist/standalone-server.js", "start:crm": "node dist/crm-server.js", "start:ai": "node dist/ai-enhanced-server.js", "build": "tsc", "dev:basic": "tsx standalone-server.ts", "dev:crm": "tsx crm-server.ts", "dev:ai": "tsx ai-enhanced-server.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"typescript": "^5.0.0", "tsx": "^4.0.0", "@types/node": "^20.0.0"}, "keywords": ["mcp", "crm", "ai", "telegram", "whatsapp", "trading", "automation"], "author": "<PERSON> <PERSON> <PERSON><PERSON>", "license": "MIT"}