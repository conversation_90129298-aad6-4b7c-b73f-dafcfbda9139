version: '3.8'

services:
  # Basis MCP Server
  mcp-basic:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yannova-mcp-basic
    environment:
      - SERVER_TYPE=basic
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - yannova-network
    ports:
      - "3001:3000"
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CRM MCP Server
  mcp-crm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yannova-mcp-crm
    environment:
      - SERVER_TYPE=crm
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL:-}
    restart: unless-stopped
    networks:
      - yannova-network
    ports:
      - "3002:3000"
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Enhanced MCP Server
  mcp-ai:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yannova-mcp-ai
    environment:
      - SERVER_TYPE=ai
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY:-}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-}
      - WHATSAPP_API_KEY=${WHATSAPP_API_KEY:-}
    restart: unless-stopped
    networks:
      - yannova-network
    ports:
      - "3003:3000"
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: yannova-postgres
    environment:
      - POSTGRES_DB=yannova_crm
      - POSTGRES_USER=yannova
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-securepassword}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - yannova-network
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: yannova-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - yannova-network
    ports:
      - "6379:6379"
    restart: unless-stopped

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: yannova-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - mcp-basic
      - mcp-crm
      - mcp-ai
    networks:
      - yannova-network
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: yannova-prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - yannova-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: yannova-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    networks:
      - yannova-network
    restart: unless-stopped

networks:
  yannova-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data: