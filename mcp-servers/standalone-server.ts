import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ErrorCode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";

const server = new Server({
  name: "yannova-basic-server",
  version: "1.0.0"
}, {
  capabilities: {
    tools: {}
  }
});

// Basis tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "calculate_sum",
        description: "Tel twee getallen bij elkaar op",
        inputSchema: {
          type: "object",
          properties: {
            a: { type: "number", description: "Eerste getal" },
            b: { type: "number", description: "Tweede getal" }
          },
          required: ["a", "b"]
        }
      },
      {
        name: "calculate_multiply",
        description: "Vermenigvuldig twee getallen",
        inputSchema: {
          type: "object",
          properties: {
            a: { type: "number", description: "Eerste getal" },
            b: { type: "number", description: "Tweede getal" }
          },
          required: ["a", "b"]
        }
      },
      {
        name: "generate_uuid",
        description: "Genereer een UUID",
        inputSchema: {
          type: "object",
          properties: {},
          required: []
        }
      },
      {
        name: "format_date",
        description: "Formatteer datum naar Nederlandse standaard",
        inputSchema: {
          type: "object",
          properties: {
            date: { type: "string", description: "Datum in ISO format" },
            format: { type: "string", description: "Format type (short, long, full)", enum: ["short", "long", "full"] }
          },
          required: ["date"]
        }
      }
    ]
  };
});

// Tool execution handler
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case "calculate_sum":
        const { a, b } = args as { a: number; b: number };
        return {
          content: [
            {
              type: "text",
              text: `Som: ${a + b}`
            }
          ]
        };

      case "calculate_multiply":
        const { a: x, b: y } = args as { a: number; b: number };
        return {
          content: [
            {
              type: "text",
              text: `Product: ${x * y}`
            }
          ]
        };

      case "generate_uuid":
        const uuid = crypto.randomUUID();
        return {
          content: [
            {
              type: "text",
              text: uuid
            }
          ]
        };

      case "format_date":
        const { date, format = "short" } = args as { date: string; format?: "short" | "long" | "full" };
        const dateObj = new Date(date);
        const formatOptions = {
          short: { day: "2-digit" as const, month: "2-digit" as const, year: "numeric" as const },
          long: { day: "2-digit" as const, month: "long" as const, year: "numeric" as const },
          full: { weekday: "long" as const, day: "2-digit" as const, month: "long" as const, year: "numeric" as const }
        };
        const options: Intl.DateTimeFormatOptions = formatOptions[format];
        
        const formatted = dateObj.toLocaleDateString("nl-NL", options);
        return {
          content: [
            {
              type: "text",
              text: formatted
            }
          ]
        };

      default:
        throw new McpError(ErrorCode.MethodNotFound, `Tool ${name} niet gevonden`);
    }
  } catch (error) {
    throw new McpError(ErrorCode.InternalError, `Fout bij uitvoeren van tool ${name}: ${error}`);
  }
});

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("Yannova Basic MCP Server gestart");
}

main().catch(console.error);