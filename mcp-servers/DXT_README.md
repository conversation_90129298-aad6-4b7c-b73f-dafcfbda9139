# Yannova CRM Desktop Extension (DXT)

Een krachtige Desktop Extension voor Claude Desktop die je Yannova CRM systeem integreert met AI-assistenten via het Model Context Protocol (MCP).

## 🚀 Wat is dit?

Deze DXT extensie maakt je Yannova CRM systeem toegankelijk voor Claude Desktop en andere MCP-compatibele AI tools. In plaats van handmatig tussen verschillende systemen te schakelen, kun je nu direct vanuit je AI-assistent:

- CRM contacten beheren
- Telegram/WhatsApp berichten versturen
- Email automatisering uitvoeren
- Trading signalen verwerken
- Workflow management uitvoeren

## 📦 Installatie

### Methode 1: Direct installeren in Claude Desktop

1. Download het `mcp-servers.dxt` bestand
2. Open Claude Desktop
3. Ga naar Settings → Extensions
4. Sleep het `.dxt` bestand naar het venster
5. Klik op "Install"
6. Configureer de benodigde API keys (zie hieronder)

### Methode 2: Handmatige installatie

Als je Claude Desktop nog niet hebt of een andere MCP-compatibele applicatie gebruikt:

```bash
# Installeer de DXT toolkit
npm install -g @anthropic-ai/dxt

# Valideer de extensie
dxt validate manifest.json

# Installeer in je MCP client
# (specifieke instructies afhankelijk van je client)
```

## ⚙️ Configuratie

Na installatie moet je de volgende environment variables configureren:

### Vereiste API Keys
- `OPENAI_API_KEY` - Voor AI functionaliteit
- `TELEGRAM_BOT_TOKEN` - Voor Telegram integratie
- `WHATSAPP_TOKEN` - Voor WhatsApp integratie  
- `RESEND_API_KEY` - Voor email functionaliteit

### Optionele Configuratie
- `DATABASE_URL` - Voor database connectie
- `REDIS_URL` - Voor caching (optioneel)

## 🛠️ Beschikbare Functionaliteiten

### CRM Management
- **Contacten beheren**: Toevoegen, bewerken, zoeken van contacten
- **Conversatie tracking**: Volledige gespreksgeschiedenis
- **Lead management**: Automatische lead scoring en follow-up

### Communicatie Integratie
- **Telegram Bot**: Verstuur berichten, ontvang notificaties
- **WhatsApp Business**: Automatische berichten en responses
- **Email Automation**: Templates, bulk verzending, tracking

### AI-Powered Workflows
- **Smart Responses**: AI-gegenereerde antwoorden op basis van context
- **Sentiment Analysis**: Automatische stemming detectie in berichten
- **Lead Scoring**: AI-gedreven lead prioritering

### Trading & Analytics
- **Signal Processing**: Verwerk trading signalen
- **Performance Tracking**: Analyseer CRM prestaties
- **Reporting**: Geautomatiseerde rapporten

## 🔧 Ontwikkeling

### Project Structuur
```
mcp-servers/
├── manifest.json          # DXT configuratie
├── dist/                  # Gecompileerde code
│   ├── crm-server.js      # Hoofd CRM server
│   ├── ai-enhanced-server.js  # AI-enhanced versie
│   └── standalone-server.js   # Basis server
├── src/                   # TypeScript broncode
└── package.json          # Dependencies
```

### Bouwen vanaf broncode
```bash
# Clone repository
git clone [repository-url]
cd yannova-crm/mcp-servers

# Installeer dependencies
npm install

# Build TypeScript
npm run build

# Package als DXT extensie
dxt pack
```

### Development Mode
```bash
# Start development server
npm run dev:crm

# Test met MCP client
node test-client.js
```

## 🔒 Beveiliging

- Alle API keys worden veilig opgeslagen in OS keychain
- Lokale data processing - geen externe servers
- Encrypted communicatie met externe services
- Configureerbare toegangsbeperkingen

## 📚 Gebruik Voorbeelden

### Contact Management
```
"Voeg een nieuwe contact toe: John Doe, email: <EMAIL>, telefoon: +31612345678"
```

### Messaging
```
"Stuur een WhatsApp bericht naar John Doe: Bedankt voor je interesse in onze diensten!"
```

### Analytics
```
"Genereer een rapport van alle leads van deze week"
```

### AI Workflows
```
"Analyseer het sentiment van de laatste 10 conversaties met prospect leads"
```

## 🆘 Troubleshooting

### Extensie laadt niet
- Controleer of alle vereiste API keys zijn ingesteld
- Valideer manifest.json: `dxt validate manifest.json`
- Bekijk Claude Desktop logs voor error details

### Functionaliteit werkt niet
- Controleer internet connectie
- Verifieer API key geldigheid
- Restart Claude Desktop na configuratie wijzigingen

### Performance Issues
- Controleer beschikbare geheugen (extensie gebruikt ~40MB)
- Restart extensie via Settings → Extensions

## 🔄 Updates

De extensie wordt automatisch bijgewerkt via Claude Desktop. Voor handmatige updates:

1. Download nieuwste `.dxt` bestand
2. Verwijder oude versie in Settings → Extensions
3. Installeer nieuwe versie

## 📄 Licentie

MIT License - zie LICENSE bestand voor details

## 🤝 Bijdragen

Bijdragen zijn welkom! Zie de hoofd repository voor development guidelines.

## 📞 Support

- GitHub Issues: [repository-url]/issues
- Email: <EMAIL>
- Documentation: [docs-url]

---

**Gebouwd met ❤️ door het Yannova team**

*Deze extensie maakt gebruik van het Model Context Protocol (MCP) en Desktop Extension Toolkit (DXT) van Anthropic.* 