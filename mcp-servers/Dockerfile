FROM node:18-alpine

# Install dependencies for native modules
RUN apk add --no-cache python3 make g++

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node -e "process.exit(0)"

# Start command based on SERVER_TYPE environment variable
CMD if [ "$SERVER_TYPE" = "basic" ]; then \
      node dist/standalone-server.js; \
    elif [ "$SERVER_TYPE" = "crm" ]; then \
      node dist/crm-server.js; \
    elif [ "$SERVER_TYPE" = "ai" ]; then \
      node dist/ai-enhanced-server.js; \
    else \
      node dist/standalone-server.js; \
    fi