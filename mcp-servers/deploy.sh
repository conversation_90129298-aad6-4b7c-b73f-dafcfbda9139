#!/bin/bash

# Yannova MCP Servers Deployment Script
# Leon - Production deployment automation

set -e

echo "🚀 Yannova MCP Servers Deployment"
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before running deployment."
    exit 1
fi

# Build and deploy based on argument
case "$1" in
    "dev")
        echo "🔧 Deploying in development mode..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d
        ;;
    "prod")
        echo "🏭 Deploying in production mode..."
        docker-compose up --build -d
        ;;
    "stop")
        echo "🛑 Stopping all services..."
        docker-compose down
        ;;
    "restart")
        echo "🔄 Restarting services..."
        docker-compose down
        docker-compose up -d
        ;;
    "logs")
        echo "📊 Showing logs..."
        docker-compose logs -f
        ;;
    "status")
        echo "📈 Service status:"
        docker-compose ps
        ;;
    "clean")
        echo "🧹 Cleaning up..."
        docker-compose down -v
        docker system prune -f
        ;;
    "backup")
        echo "💾 Creating backup..."
        mkdir -p backups
        timestamp=$(date +%Y%m%d_%H%M%S)
        docker-compose exec postgres pg_dump -U yannova yannova_crm > backups/backup_$timestamp.sql
        docker-compose exec redis redis-cli BGSAVE
        echo "✅ Backup created: backups/backup_$timestamp.sql"
        ;;
    "restore")
        if [ -z "$2" ]; then
            echo "❌ Usage: $0 restore <backup_file>"
            exit 1
        fi
        echo "🔄 Restoring from backup: $2"
        docker-compose exec -T postgres psql -U yannova yannova_crm < "$2"
        echo "✅ Restore completed"
        ;;
    *)
        echo "📋 Usage: $0 {dev|prod|stop|restart|logs|status|clean|backup|restore}"
        echo ""
        echo "Commands:"
        echo "  dev      - Deploy in development mode"
        echo "  prod     - Deploy in production mode"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  logs     - Show service logs"
        echo "  status   - Show service status"
        echo "  clean    - Clean up containers and volumes"
        echo "  backup   - Create database backup"
        echo "  restore  - Restore from backup file"
        exit 1
        ;;
esac

if [ "$1" = "dev" ] || [ "$1" = "prod" ]; then
    echo ""
    echo "✅ Deployment completed!"
    echo ""
    echo "🌐 Services available at:"
    echo "  - Basic MCP Server: http://localhost:3001"
    echo "  - CRM MCP Server: http://localhost:3002"
    echo "  - AI Enhanced MCP Server: http://localhost:3003"
    echo "  - Grafana Dashboard: http://localhost:3000 (admin/admin)"
    echo "  - Prometheus: http://localhost:9090"
    echo ""
    echo "📊 Check status with: $0 status"
    echo "📋 View logs with: $0 logs"
fi